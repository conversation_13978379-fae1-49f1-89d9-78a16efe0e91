<template>
  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    width="600px"
    class="device-type-edit-dialog"
  >
    <el-form
      :model="deviceTypeModel"
      :rules="rules"
      ref="formRef"
      label-width="100px"
      class="device-type-edit-form"
    >
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="类型名称" prop="typeName">
            <el-input
              v-model="deviceTypeModel.typeName"
              placeholder="请输入类型名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="类型代号" prop="typeCode">
            <el-input
              v-model="deviceTypeModel.typeCode"
              placeholder="请输入类型代号"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="deviceTypeModel.sort"
              :min="0"
              style="width: 100%"
              placeholder="排序"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="父级ID" prop="parentId">
            <el-input-number
              v-model="deviceTypeModel.parentId"
              :min="0"
              :disabled="parentIdDisabled"
              style="width: 100%"
              placeholder="父级ID"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="小区ID" prop="communityId">
            <el-input-number
              v-model="deviceTypeModel.communityId"
              :min="0"
              :disabled="true"
              style="width: 100%"
              placeholder="小区ID"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { addDeviceType, editDeviceType, getDeviceType } from '@/api/deviceApi'
import { getSelectedCommunityId } from '@/store/modules/options'
import mitt from '@/utils/mitt'

export default {
  data() {
    return {
      dialog: {
        show: false,
        title: '',
        type: ''
      },
      deviceTypeModel: {
        id: null,
        typeName: '',
        typeCode: '',
        parentId: 0,
        ancestors: '',
        sort: 0,
        communityId: null
      },
      parentIdDisabled: false, // 控制父级ID是否可编辑
      rules: {
        typeName: [
          { required: true, message: '请输入类型名称', trigger: 'blur' }
        ],
        typeCode: [
          { required: true, message: '请输入类型代号', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.dialog.type === 'add') {
            addDeviceType(this.deviceTypeModel).then(() => {
              this.$message.success('操作成功')
              this.dialog.show = false
              this.$emit('search')
            })
          } else {
            editDeviceType(this.deviceTypeModel).then(() => {
              this.$message.success('操作成功')
              this.dialog.show = false
              this.$emit('search')
            })
          }
        }
      })
    },
    resetForm() {
      this.deviceTypeModel = {
        id: null,
        typeName: '',
        typeCode: '',
        parentId: 0,
        ancestors: '',
        sort: 0,
        communityId: getSelectedCommunityId() // 自动设置当前选中的小区ID
      }
      this.parentIdDisabled = false
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
    }
  },
  mounted() {
    mitt.on('openDeviceTypeEdit', (data) => {
      this.resetForm()
      this.dialog.type = data.type

      if (data.type === 'add') {
        this.dialog.title = '添加设备类型'
        // 如果是从右上角添加，父级ID不可编辑，设为0
        if (!data.parentId) {
          this.parentIdDisabled = true
          this.deviceTypeModel.parentId = 0
        } else {
          // 如果是从列表项添加，设置父级ID并禁用编辑
          this.parentIdDisabled = true
          this.deviceTypeModel.parentId = data.parentId
        }
      } else {
        this.dialog.title = '编辑设备类型'
        this.parentIdDisabled = false // 编辑时父级ID可以修改
        getDeviceType(data.id).then(res => {
          this.deviceTypeModel = res.data.data
        })
      }

      this.dialog.show = true
    })
  }
}
</script>

<style scoped>
.device-type-edit-dialog .el-form {
  padding: 20px;
}

.device-type-edit-form .el-form-item {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
