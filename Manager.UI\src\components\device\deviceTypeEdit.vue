<template>
  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    width="600px"
    class="device-type-edit-dialog"
  >
    <el-form
      :model="deviceTypeModel"
      :rules="rules"
      ref="formRef"
      label-width="100px"
      class="device-type-edit-form"
    >
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="类型名称" prop="typeName">
            <el-input
              v-model="deviceTypeModel.typeName"
              placeholder="请输入类型名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="类型代号" prop="typeCode">
            <el-input
              v-model="deviceTypeModel.typeCode"
              placeholder="请输入类型代号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="父级ID" prop="parentId">
            <el-input-number
              v-model="deviceTypeModel.parentId"
              :min="0"
              style="width: 100%"
              placeholder="父级ID"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="deviceTypeModel.sort"
              :min="0"
              style="width: 100%"
              placeholder="排序"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="祖级列表" prop="ancestors">
            <el-input
              v-model="deviceTypeModel.ancestors"
              placeholder="祖级列表(逗号分隔)"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="小区ID" prop="communityId">
            <el-input-number
              v-model="deviceTypeModel.communityId"
              :min="0"
              style="width: 100%"
              placeholder="小区ID"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { addDeviceType, editDeviceType, getDeviceType } from '@/api/deviceApi'
import mitt from '@/utils/mitt'

export default {
  data() {
    return {
      dialog: {
        show: false,
        title: '',
        type: ''
      },
      deviceTypeModel: {
        id: null,
        typeName: '',
        typeCode: '',
        parentId: 0,
        ancestors: '',
        sort: 0,
        communityId: null
      },
      rules: {
        typeName: [
          { required: true, message: '请输入类型名称', trigger: 'blur' }
        ],
        typeCode: [
          { required: true, message: '请输入类型代号', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.dialog.type === 'add') {
            addDeviceType(this.deviceTypeModel).then(() => {
              this.$message.success('操作成功')
              this.dialog.show = false
              this.$emit('search')
            })
          } else {
            editDeviceType(this.deviceTypeModel).then(() => {
              this.$message.success('操作成功')
              this.dialog.show = false
              this.$emit('search')
            })
          }
        }
      })
    },
    resetForm() {
      this.deviceTypeModel = {
        id: null,
        typeName: '',
        typeCode: '',
        parentId: 0,
        ancestors: '',
        sort: 0,
        communityId: null
      }
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
    }
  },
  mounted() {
    mitt.on('openDeviceTypeEdit', (data) => {
      this.resetForm()
      this.dialog.type = data.type
      
      if (data.type === 'add') {
        this.dialog.title = '添加设备类型'
      } else {
        this.dialog.title = '编辑设备类型'
        getDeviceType(data.id).then(res => {
          this.deviceTypeModel = res.data.data
        })
      }
      
      this.dialog.show = true
    })
  }
}
</script>

<style scoped>
.device-type-edit-dialog .el-form {
  padding: 20px;
}

.device-type-edit-form .el-form-item {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
