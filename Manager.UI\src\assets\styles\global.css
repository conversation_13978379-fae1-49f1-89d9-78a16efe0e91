/* 导入主题变量 */
@import './theme.css';

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--background-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* 暗色主题滚动条 */
.dark-theme ::-webkit-scrollbar-thumb {
  background: #555;
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* 侧边栏滚动条 */
.sidebar ::-webkit-scrollbar {
  width: 6px;
}

.sidebar ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.sidebar ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.dark-theme .sidebar ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
}

.dark-theme .sidebar ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-duration);
}

a:hover {
  color: var(--primary-light);
}

/* 按钮样式增强 */
.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* 全局样式覆盖 - 确保按钮样式正确 */
.el-button.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.el-button.el-button--text,
.el-button.is-text {
  background-color: transparent;
  border-color: transparent;
  color: var(--primary-color);
}

.el-button--primary:hover,
.el-button--primary:focus {
  background-color: var(--primary-light);
  border-color: var(--primary-light);
}

.el-button--success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.el-button--warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.el-button--danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.el-button--info {
  background-color: var(--info-color);
  border-color: var(--info-color);
}

/* 表单样式增强 */
.el-input__inner:focus {
  border-color: var(--primary-color);
}

/* 通用卡片样式 */
.card {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px 20px;
  margin-bottom: 20px;
  transition: box-shadow var(--transition-duration), background-color var(--transition-duration);
  border: 1px solid var(--border-color);
}

/* 搜索卡片：横向一行，内容不掉行 */
.card--search {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: none;
  height: auto;
  padding: 20px 20px;
  margin-bottom: 20px;
  background: none;
  box-shadow: none;
}

.card--search .el-button,
.card--search .el-input {
  margin-bottom: 0 !important;
  min-width: 80px;
  white-space: nowrap;
  width: auto !important;
  display: inline-flex;
}

/* 表格卡片撑满剩余空间 */
.card--table {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  height: 100%;
  overflow: auto;
}

/* 响应式：小屏下搜索区纵向排列 */
@media (max-width: 768px) {
  .card--search {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
    padding: 10px 5px;
  }
  .card--search .el-button,
  .card--search .el-input {
    width: 100% !important;
    min-width: 0;
  }
}

/* 暗色主题下的卡片 */
.dark-theme .card,
.dark-theme .card--search,
.dark-theme .card--table {
  background-color: var(--card-background) !important;
  border: 1px solid #444;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3) !important;
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px !important;
  min-height: 400px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px 20px;
  height: 100%;
}

/* 表格列样式 */
.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

/* 数据表格样式 */
.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
  background-color: #fff;
}

/* 表格行和单元格样式 */
.el-table__body tr,
.el-table__body td,
.el-table__header tr,
.el-table__header th {
  background-color: #fff;
}

.card:hover, .table-container:hover {
  box-shadow: var(--box-shadow-hover);
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 16px;
  font-weight: 500;
  line-height: 1.2;
}

h1 {
  font-size: 2rem;
}

h2 {
  font-size: 1.75rem;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

h5 {
  font-size: 1rem;
}

h6 {
  font-size: 0.875rem;
}

/* 文本样式 */
.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-info {
  color: var(--info-color);
}

.text-muted {
  color: var(--text-secondary);
}

/* 边距工具类 */
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 1rem; }
.ml-4 { margin-left: 1.5rem; }
.ml-5 { margin-left: 3rem; }

.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 1rem; }
.mr-4 { margin-right: 1.5rem; }
.mr-5 { margin-right: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* 弹性布局工具类 */
.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.justify-content-start { justify-content: flex-start; }
.justify-content-center { justify-content: center; }
.justify-content-end { justify-content: flex-end; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.align-items-start { align-items: flex-start; }
.align-items-center { align-items: center; }
.align-items-end { align-items: flex-end; }
.flex-grow-1 { flex-grow: 1; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-wrap { flex-wrap: wrap; }

/* 聊天界面特定样式 */
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: background-color var(--transition-duration), color var(--transition-duration);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--content-padding);
}

.message {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
}

.message.user {
  flex-direction: row-reverse;
}

.message-content {
  max-width: 70%;
  margin: 0 12px;
  padding: 12px 16px;
  border-radius: var(--border-radius);
}

.user .message-content {
  background: var(--primary-color);
  color: white;
}

.assistant .message-content {
  background: var(--divider-color);
  color: var(--text-primary);
}

.message-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.user .avatar {
  background: var(--primary-color);
  color: white;
}

.chat-input {
  padding: 15px;
  background: var(--background-color);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.chat-input textarea {
  flex: 1;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  resize: none;
  background-color: white;
  color: var(--text-primary);
  transition: background-color var(--transition-duration), color var(--transition-duration), border-color var(--transition-duration);
}

/* 加载动画 */
.typing-indicator {
  display: flex;
  padding: 12px 16px;
  background: var(--divider-color);
  border-radius: var(--border-radius);
  margin: 0 12px;
}

.typing-indicator span {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: var(--text-secondary);
  border-radius: 50%;
  margin-right: 5px;
  animation: typing 1s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

/* 暗色主题全局样式 */

/* 全局暗色主题覆盖 - 确保所有元素都应用暗色主题 */
.dark-theme * {
  border-color: #444 !important;
}

/* 确保所有图标都有透明背景 */
.dark-theme .el-icon,
.dark-theme .el-icon *,
.dark-theme svg,
.dark-theme [class*="icon"],
.dark-theme [class*="icon"] * {
  background-color: transparent !important;
}

/* 确保所有文本按钮背景透明 */
.dark-theme .el-button--text,
.dark-theme button.el-button--text,
.dark-theme .el-button.is-text,
.dark-theme [class*="el-button"].is-text,
.dark-theme [class*="el-button"].el-button--text {
  background-color: transparent !important;
  border-color: transparent !important;
  box-shadow: none !important;
}

/* 确保所有背景色都是暗色 - 排除表格相关元素 */
.dark-theme *:not(.el-button):not(.el-tag):not(a):not(button):not(input):not(textarea):not(select):not(.el-input__inner):not(.el-input__wrapper):not(.el-textarea__inner):not(.el-textarea__wrapper):not(.el-select-dropdown__item):not(.el-cascader-node):not(.el-menu-item):not(.el-sub-menu__title):not(.el-dropdown-menu__item):not(.el-tooltip__popper):not(.el-message):not(.el-notification):not(.el-badge__content):not(.el-loading-mask):not(.el-loading-spinner):not(.el-progress-bar__inner):not(.el-switch__core):not(.el-checkbox__inner):not(.el-radio__inner):not(.el-table):not(.el-table *):not(.el-table__body):not(.el-table__header):not(.el-table__body tr):not(.el-table__body td):not(.el-table__header tr):not(.el-table__header th) {
  background-color: #333 !important;
}

/* 暗色主题下的表格样式 */
.dark-theme .el-table,
.dark-theme .el-table__body,
.dark-theme .el-table__header,
.dark-theme .el-table__footer,
.dark-theme .el-table__fixed,
.dark-theme .el-table__fixed-right,
.dark-theme .el-table__column-filter-trigger,
.dark-theme .el-table .cell,
.dark-theme .el-table__body tr,
.dark-theme .el-table__body td,
.dark-theme .el-table__header tr,
.dark-theme .el-table__header th {
  background-color: var(--card-background) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

/* 基础元素 */
.dark-theme body,
.dark-theme #app {
  background-color: #222;
  color: #e0e0e0;
}

/* 容器和卡片 */
.dark-theme .el-container,
.dark-theme .el-main,
.dark-theme .el-card,
.dark-theme .el-dialog,
.dark-theme .el-drawer {
  background-color: #333;
  color: #e0e0e0;
}

/* 暗色主题下的表单行样式 */
.dark-theme .form-row,
.dark-theme .el-row:not(.search-row):not(.table-container):not(.views-row) {
  background-color: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

/* 表格 - 使用更高优先级的选择器 */
.dark-theme .el-table,
.dark-theme .el-table__inner-wrapper,
.dark-theme .el-table__body,
.dark-theme .el-table__header,
.dark-theme .el-table__body-wrapper,
.dark-theme .el-table__header-wrapper,
.dark-theme .el-table__footer-wrapper,
.dark-theme .el-table__append-wrapper,
.dark-theme .el-table .hidden-columns,
.dark-theme .el-table__column-resize-proxy,
.dark-theme .el-table__column-filter-trigger,
.dark-theme .el-table__column-filter-trigger i,
.dark-theme .el-table .ascending,
.dark-theme .el-table .descending,
.dark-theme .el-table .el-table-column--selection,
.dark-theme .el-table .el-table__row,
.dark-theme .el-table .el-table__row td,
.dark-theme .el-table .el-table__row th,
.dark-theme .el-table .el-table__row .cell,
.dark-theme .el-table .el-table__row .cell > *,
.dark-theme .el-table__body tr.el-table__row,
.dark-theme .el-table__body tr.el-table__row td.el-table__cell,
.dark-theme .el-table__body tr.el-table__row td.el-table__cell div,
.dark-theme .el-table__body tr.el-table__row td.el-table__cell span,
.dark-theme .el-table__body tr.el-table__row td.el-table__cell .cell,
.dark-theme .el-table__body tr.el-table__row td.el-table__cell .cell > *,
.dark-theme .el-table__body tr.el-table__row--striped,
.dark-theme .el-table__body tr.el-table__row--striped td.el-table__cell,
.dark-theme .el-table__body tr.el-table__row--striped td.el-table__cell div,
.dark-theme .el-table__body tr.el-table__row--striped td.el-table__cell span,
.dark-theme .el-table__body tr.el-table__row--striped td.el-table__cell .cell,
.dark-theme .el-table__body tr.el-table__row--striped td.el-table__cell .cell > *,
.dark-theme .el-table__header-wrapper tr,
.dark-theme .el-table__header-wrapper th,
.dark-theme .el-table__header-wrapper th.is-leaf,
.dark-theme .el-table__header tr,
.dark-theme .el-table__header th,
.dark-theme .el-table__header th.is-leaf,
.dark-theme .el-table__body tr,
.dark-theme .el-table__body td,
.dark-theme .el-table__footer tr,
.dark-theme .el-table__footer td {
  background-color: var(--card-background) !important;
  color: var(--text-primary) !important;
  border-color: #444 !important;
}

/* 表格头部样式 */
.dark-theme .el-table__header-wrapper th,
.dark-theme .el-table__header th,
.dark-theme .el-table__header-wrapper .cell,
.dark-theme .el-table__header .cell {
  background-color: #252525 !important;
}

/* 暗色主题下表格填充样式 */
.dark-theme .el-table,
.dark-theme .el-table__inner-wrapper,
.dark-theme .el-table__body-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.dark-theme .el-table__body-wrapper {
  overflow-y: auto;
  height: auto !important;
}

.dark-theme .el-table__header-wrapper {
  width: 100% !important;
  background-color: transparent !important;
}

/* 修复表格行和单元格 - 使用通配符确保覆盖所有元素 */
.dark-theme .el-table__body tr,
.dark-theme .el-table__body td,
.dark-theme .el-table__body tr *,
.dark-theme .el-table__body td *,
.dark-theme .el-table__body .cell,
.dark-theme .el-table__body .cell * {
  background-color: var(--card-background) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
  transition: background-color 0.25s ease !important;
}

/* 表格行悬停效果 */
.dark-theme .el-table__body tr.hover-row > td.el-table__cell,
.dark-theme .el-table__body tr:hover > td.el-table__cell {
  background-color: var(--primary-dark) !important;
  opacity: 0.8;
}

/* 强制所有表格文本颜色 */
.dark-theme .el-table,
.dark-theme .el-table *,
.dark-theme .el-table__body,
.dark-theme .el-table__body *,
.dark-theme .el-table .cell,
.dark-theme .el-table .cell * {
  color: var(--text-primary) !important;
}

/* 修复表格边框和分割线 */
.dark-theme .el-table::before,
.dark-theme .el-table::after,
.dark-theme .el-table__fixed::before,
.dark-theme .el-table__fixed::after,
.dark-theme .el-table__fixed-right::before,
.dark-theme .el-table__fixed-right::after,
.dark-theme .el-table .el-table__cell div.cell.el-tooltip::before,
.dark-theme .el-table .el-table__cell div.cell.el-tooltip::after {
  background-color: var(--border-color) !important;
}

/* 修复表格外边框和分割线 */
.dark-theme .el-table--border,
.dark-theme .el-table--group,
.dark-theme .el-table--border::after,
.dark-theme .el-table--border::before,
.dark-theme .el-table__border-left-patch,
.dark-theme .el-table__border-right-patch {
  border-color: var(--border-color) !important;
  background-color: var(--border-color) !important;
}

/* 修复表格行分割线 */
.dark-theme .el-table__border-left-patch,
.dark-theme .el-table__border-right-patch,
.dark-theme .el-table__border-top-patch {
  background-color: var(--border-color) !important;
}

.dark-theme .el-table th,
.dark-theme .el-table th.is-leaf,
.dark-theme .el-table .el-table__cell,
.dark-theme .el-table tr,
.dark-theme .el-table td,
.dark-theme .el-table tbody tr,
.dark-theme .el-table tbody td {
  background-color: var(--card-background) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

/* 确保表格内的所有文本元素都有正确的颜色 */
.dark-theme .el-table .cell,
.dark-theme .el-table th > .cell,
.dark-theme .el-table .cell > *,
.dark-theme .el-table .cell > span,
.dark-theme .el-table .cell > div,
.dark-theme .el-table--border th.el-table__cell,
.dark-theme .el-table--border td.el-table__cell {
  color: var(--text-primary) !important;
}

/* 表格条纹行 */
.dark-theme .el-table--striped .el-table__body tr.el-table__row--striped td,
.dark-theme .el-table--striped .el-table__body tr.el-table__row--striped {
  background-color: var(--divider-color) !important;
}

/* 表格选中行 */
.dark-theme .el-table__body tr.current-row > td,
.dark-theme .el-table__body tr.current-row {
  background-color: var(--primary-color) !important;
  color: #ffffff !important;
  opacity: 0.8;
}

/* 表头样式 */
.dark-theme .el-table__header-wrapper,
.dark-theme .el-table__header-wrapper th,
.dark-theme .el-table__header-wrapper tr,
.dark-theme .el-table__header-wrapper .cell,
.dark-theme .el-table__header,
.dark-theme .el-table__header th,
.dark-theme .el-table__header tr,
.dark-theme .el-table__header .cell {
  background-color: var(--divider-color) !important;
  color: var(--text-primary) !important;
  font-weight: bold !important;
  border-color: var(--border-color) !important;
}

/* 表头底部边框 */
.dark-theme .el-table__header-wrapper::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: var(--border-color) !important;
  z-index: 1;
}

/* 表尾样式 */
.dark-theme .el-table__footer-wrapper,
.dark-theme .el-table__footer-wrapper td,
.dark-theme .el-table__footer-wrapper tr,
.dark-theme .el-table__footer-wrapper .cell,
.dark-theme .el-table__footer,
.dark-theme .el-table__footer td,
.dark-theme .el-table__footer tr,
.dark-theme .el-table__footer .cell {
  background-color: #2c2c2c !important;
  color: #ffffff !important;
  border-color: #444 !important;
}

/* 修复表格内的空数据显示 */
.dark-theme .el-table__empty-block,
.dark-theme .el-table__empty-block .el-table__empty-text,
.dark-theme .el-table__empty-block .el-empty,
.dark-theme .el-table__empty-block .el-empty__description,
.dark-theme .el-table__empty-block .el-empty__description p,
.dark-theme .el-table__empty-block .el-empty__image {
  background-color: #333 !important;
  color: #999 !important;
  width: 100% !important;
}

/* 确保空数据文本可见 */
.dark-theme .el-table__empty-text,
.dark-theme .el-empty__description,
.dark-theme .el-empty__description p {
  color: #999 !important;
}

/* 修复表格单元格边框 */
.dark-theme .el-table--border .el-table__cell,
.dark-theme .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed,
.dark-theme .el-table__fixed-right-patch,
.dark-theme .el-table__fixed-header-wrapper {
  border-right: 1px solid var(--border-color) !important;
  border-bottom: 1px solid var(--border-color) !important;
  background-color: var(--card-background) !important;
}

.dark-theme .el-table--border .el-table__cell:first-child {
  border-left: 1px solid var(--border-color) !important;
}

/* 修复表格行边框 */
.dark-theme .el-table td.el-table__cell,
.dark-theme .el-table th.el-table__cell.is-leaf {
  border-bottom: 1px solid var(--border-color) !important;
}

/* 修复表格固定列 */
.dark-theme .el-table__fixed,
.dark-theme .el-table__fixed-right {
  background-color: #333 !important;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3) !important;
}

.dark-theme .el-table__fixed-right-patch {
  background-color: #1e2a3b !important;
}

/* 修复表格展开行 */
.dark-theme .el-table__expanded-cell {
  background-color: #2c2c2c !important;
  color: #ffffff !important;
}

/* 修复表格排序图标 */
.dark-theme .el-table .sort-caret {
  border-top-color: #999 !important;
  border-bottom-color: #999 !important;
}

.dark-theme .el-table .ascending .sort-caret.ascending {
  border-bottom-color: #ffffff !important;
}

.dark-theme .el-table .descending .sort-caret.descending {
  border-top-color: #ffffff !important;
}

/* 表格内的操作按钮 */
.dark-theme .el-table .el-button--text {
  color: var(--primary-color) !important;
  background-color: transparent !important;
}

.dark-theme .el-table .el-button--text:hover {
  color: var(--primary-light) !important;
  background-color: transparent !important;
}

/* 表格内的链接 */
.dark-theme .el-table a {
  color: var(--primary-color) !important;
  background-color: transparent !important;
}

.dark-theme .el-table a:hover {
  color: var(--primary-light) !important;
  background-color: transparent !important;
}

/* 表格内的状态标签 */
.dark-theme .el-table .el-tag {
  border-color: transparent !important;
  background-color: #444 !important;
}

/* 特定修复 - 针对红色标记区域 */
.dark-theme .el-table__body tr.el-table__row td.el-table__cell,
.dark-theme .el-table__body tr.el-table__row--striped td.el-table__cell,
.dark-theme .el-table__body tr.hover-row td.el-table__cell,
.dark-theme .el-table__body tr.current-row td.el-table__cell,
.dark-theme .el-table__body tr td.el-table__cell {
  background-color: #333 !important;
  color: #ffffff !important;
  border-color: #444 !important;
}

/* 确保所有表格内容都有正确的背景色 */
.dark-theme .el-table__body tr td.el-table__cell .cell,
.dark-theme .el-table__body tr td.el-table__cell .cell > *,
.dark-theme .el-table__body tr td.el-table__cell .cell > div,
.dark-theme .el-table__body tr td.el-table__cell .cell > span {
  background-color: #333 !important;
  color: #ffffff !important;
}

/* 特定修复 - 针对图片中红色标记的区域 */
.dark-theme .el-table__body tr:nth-child(1) td:nth-child(3),
.dark-theme .el-table__body tr:nth-child(2) td:nth-child(3),
.dark-theme .el-table__body tr:nth-child(3) td:nth-child(3),
.dark-theme .el-table__body tr:nth-child(4) td:nth-child(3),
.dark-theme .el-table__body tr:nth-child(1) td:nth-child(3) *,
.dark-theme .el-table__body tr:nth-child(2) td:nth-child(3) *,
.dark-theme .el-table__body tr:nth-child(3) td:nth-child(3) *,
.dark-theme .el-table__body tr:nth-child(4) td:nth-child(3) * {
  background-color: #333 !important;
  color: #ffffff !important;
}

/* 修复表格行 */
.dark-theme .el-table__body tr:nth-child(1),
.dark-theme .el-table__body tr:nth-child(2),
.dark-theme .el-table__body tr:nth-child(3),
.dark-theme .el-table__body tr:nth-child(4),
.dark-theme .el-table__body tr:nth-child(5),
.dark-theme .el-table__body tr:nth-child(1) *,
.dark-theme .el-table__body tr:nth-child(2) *,
.dark-theme .el-table__body tr:nth-child(3) *,
.dark-theme .el-table__body tr:nth-child(4) *,
.dark-theme .el-table__body tr:nth-child(5) * {
  background-color: #333 !important;
  color: #ffffff !important;
}

/* 特定修复 - 针对表格中的红色标记区域 */
.dark-theme .el-table__body tr td.el-table__cell:nth-child(1),
.dark-theme .el-table__body tr td.el-table__cell:nth-child(2),
.dark-theme .el-table__body tr td.el-table__cell:nth-child(3),
.dark-theme .el-table__body tr td.el-table__cell:nth-child(4),
.dark-theme .el-table__body tr td.el-table__cell:nth-child(5),
.dark-theme .el-table__body tr td.el-table__cell:nth-child(6),
.dark-theme .el-table__body tr td.el-table__cell:nth-child(7),
.dark-theme .el-table__body tr td.el-table__cell:nth-child(8),
.dark-theme .el-table__body tr td.el-table__cell:nth-child(1) *,
.dark-theme .el-table__body tr td.el-table__cell:nth-child(2) *,
.dark-theme .el-table__body tr td.el-table__cell:nth-child(3) *,
.dark-theme .el-table__body tr td.el-table__cell:nth-child(4) *,
.dark-theme .el-table__body tr td.el-table__cell:nth-child(5) *,
.dark-theme .el-table__body tr td.el-table__cell:nth-child(6) *,
.dark-theme .el-table__body tr td.el-table__cell:nth-child(7) *,
.dark-theme .el-table__body tr td.el-table__cell:nth-child(8) * {
  background-color: var(--card-background) !important;
  color: var(--text-primary) !important;
}

/* 特定修复 - 针对表格行的背景色 */
.dark-theme .el-table__body tr,
.dark-theme .el-table__body tr.el-table__row,
.dark-theme .el-table__body tr.el-table__row--striped,
.dark-theme .el-table__body tr:hover,
.dark-theme .el-table__body tr.hover-row,
.dark-theme .el-table__body tr.current-row,
.dark-theme .el-table tr,
.dark-theme .el-table tr.el-table__row,
.dark-theme .el-table tr.el-table__row--striped,
.dark-theme .el-table tr:hover,
.dark-theme .el-table tr.hover-row,
.dark-theme .el-table tr.current-row {
  background-color: var(--card-background) !important;
  border-color: #444 !important;
}

/* 表单元素 */
.dark-theme .el-input__inner,
.dark-theme .el-input__wrapper,
.dark-theme .el-textarea__inner,
.dark-theme .el-textarea__wrapper,
.dark-theme .el-select-dropdown,
.dark-theme .el-select-dropdown__item,
.dark-theme .el-date-picker,
.dark-theme .el-date-table th,
.dark-theme .el-date-table td,
.dark-theme .el-picker-panel,
.dark-theme .el-time-panel {
  background-color: #444 !important;
  color: #e0e0e0 !important;
  border-color: #555 !important;
  --el-input-bg-color: #444 !important;
  --el-input-border-color: #555 !important;
  --el-input-text-color: #e0e0e0 !important;
  --el-input-hover-border-color: #666 !important;
  --el-input-focus-border-color: var(--primary-color) !important;
}

.dark-theme .el-input__inner::placeholder,
.dark-theme .el-textarea__inner::placeholder {
  color: #999 !important;
}

/* 修复输入框图标 */
.dark-theme .el-input__prefix,
.dark-theme .el-input__suffix,
.dark-theme .el-input__icon,
.dark-theme .el-input__prefix *,
.dark-theme .el-input__suffix *,
.dark-theme .el-input__icon *,
.dark-theme .el-input__prefix .el-icon,
.dark-theme .el-input__suffix .el-icon,
.dark-theme .el-input__prefix .el-icon *,
.dark-theme .el-input__suffix .el-icon *,
.dark-theme .el-input__prefix svg,
.dark-theme .el-input__suffix svg {
  color: #999 !important;
  background-color: transparent !important;
}

/* 按钮 */
.dark-theme .el-button--default {
  background-color: #444 !important;
  border-color: #555 !important;
  color: #e0e0e0 !important;
}

.dark-theme .el-button--default:hover,
.dark-theme .el-button--default:focus {
  background-color: #555 !important;
  border-color: #666 !important;
  color: #fff !important;
}

.dark-theme .el-button--primary {
  background-color: #0056b3 !important;
  border-color: #0056b3 !important;
  color: white !important;
}

.dark-theme .el-button--primary:hover,
.dark-theme .el-button--primary:focus {
  background-color: #0069d9 !important;
  border-color: #0069d9 !important;
}

/* 文本按钮样式 */
.dark-theme .el-button--text,
.dark-theme button.el-button--text,
.dark-theme .el-button.is-text {
  background-color: transparent !important;
  border-color: transparent !important;
  color: #409EFF !important;
  box-shadow: none !important;
}

.dark-theme .el-button--text:hover,
.dark-theme button.el-button--text:hover,
.dark-theme .el-button.is-text:hover {
  background-color: transparent !important;
  border-color: transparent !important;
  color: #66b1ff !important;
}

/* 修复按钮内的图标 */
.dark-theme .el-button .el-icon,
.dark-theme .el-button i {
  color: inherit !important;
  background-color: transparent !important;
}

/* 特定修复 - 针对图片中红色标记的按钮 */
.dark-theme .el-button.el-button--primary,
.dark-theme button.el-button--primary,
.dark-theme .el-button[type="primary"],
.dark-theme .el-button[type="primary"],
.dark-theme .el-button.is-primary,
.dark-theme button.is-primary,
.dark-theme .el-button.el-button--primary,
.dark-theme button.el-button--primary,
.dark-theme .el-button[type="primary"],
.dark-theme button[type="primary"] {
  background-color: #0056b3 !important;
  border-color: #0056b3 !important;
  color: white !important;
}

/* 表格中的按钮特殊处理 */
.dark-theme .el-table [class*="el-button"],
.dark-theme .el-table button,
.dark-theme .el-table-column--selection button,
.dark-theme .el-table-column--selection .el-button,
.dark-theme .el-table__body tr td.el-table__cell .el-button,
.dark-theme .el-table__body tr td.el-table__cell button {
  background-color: transparent !important;
  border-color: transparent !important;
  color: #409EFF !important;
}

/* 确保所有文本按钮背景透明 */
.dark-theme .el-button--text,
.dark-theme button.el-button--text,
.dark-theme .el-button.is-text,
.dark-theme .el-button.is-link,
.dark-theme .el-button--link,
.dark-theme button.el-button--link,
.dark-theme [class*="el-button"].is-text,
.dark-theme [class*="el-button"].el-button--text,
.dark-theme [class*="el-button"].is-link,
.dark-theme [class*="el-button"].el-button--link,
.dark-theme button[type="text"],
.dark-theme .el-button[type="text"],
.dark-theme button[size="mini"],
.dark-theme .el-button[size="mini"],
.dark-theme .el-button.el-button--default.is-text,
.dark-theme button.el-button--default.is-text {
  background-color: transparent !important;
  border-color: transparent !important;
  color: #409EFF !important;
  box-shadow: none !important;
}

/* 确保添加字典按钮样式正确 */
.dark-theme .el-button.el-button--primary:not(.is-text):not(.el-button--text),
.dark-theme button.el-button--primary:not(.is-text):not(.el-button--text),
.dark-theme .el-row .el-button[type="primary"],
.dark-theme .el-row button[type="primary"],
.dark-theme .el-col .el-button[type="primary"],
.dark-theme .el-col button[type="primary"] {
  background-color: #0056b3 !important;
  border-color: #0056b3 !important;
  color: white !important;
}

/* 视图组件通用样式 */
/* 1. 按钮样式 */
.dark-theme .el-button[type="primary"],
.dark-theme .el-button.el-button--primary {
  background-color: #0056b3 !important;
  border-color: #0056b3 !important;
  color: white !important;
}

.dark-theme .el-table .el-button--text,
.dark-theme .el-table .el-button.is-text {
  background-color: transparent !important;
  border-color: transparent !important;
  color: #409EFF !important;
}

/* 2. 分页样式 */
.el-pagination {
  margin-top: 15px;
  text-align: right;
}

/* 3. 表格样式 */
.el-table {
  width: 100%;
  margin-bottom: 15px;
  border-radius: 4px;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff !important;
}

/* 表格内部元素填充 */
.el-table__inner-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.el-table__body-wrapper {
  flex: 1;
  overflow-y: auto;
  height: auto !important;
}

/* 修复表格头部样式 */
.el-table__header-wrapper {
  width: 100% !important;
  background-color: transparent !important;
}

/* 4. 搜索区域样式 */
.el-input {
  width: 100%;
}

/* 搜索行样式 */
.search-row {
  margin-bottom: 20px;
  background-color: #fff !important;
  padding: 20px 20px;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
}

/* 添加按钮列样式 */
.add-button-col {
  display: flex;
  justify-content: flex-end;
}

/* 暗色主题下的搜索行样式 */
.dark-theme .search-row {
  background-color: var(--card-background) !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3) !important;
  border: 1px solid #444 !important;
  width: 100%;
}

/* 暗色主题下的表格容器样式 */
.dark-theme .table-container {
  background-color: var(--card-background) !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3) !important;
  border: 1px solid #444 !important;
  width: 100%;
}

/* 5. 操作列样式 */
.el-table .el-button--text {
  padding: 2px 0;
  margin: 0 5px;
}

/* 6. 对话框样式 */
.dialog-footer {
  text-align: center;
  margin-top: 20px;
}

.dialog-footer .el-button {
  min-width: 100px;
}

/* 7. 响应式样式 */
@media (max-width: 768px) {
  /* 表格响应式 */
  .el-table {
    width: 100%;
    overflow-x: auto;
  }

  /* 表格容器响应式 */
  .table-container {
    padding: 5px !important;
  }

  /* 表格列响应式 */
  .el-table__header-wrapper,
  .el-table__body-wrapper {
    overflow-x: auto;
  }

  /* 表单响应式 */
  .el-form-item {
    margin-bottom: 15px;
  }

  /* 表单项响应式 */
  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    width: 100%;
  }

  /* 输入框响应式 */
  .el-input {
    width: 100% !important;
  }

  /* 按钮响应式 */
  .el-button {
    margin-bottom: 5px;
  }

  /* 行和列响应式 */
  .el-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .el-col {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }

  /* 分页响应式 */
  .el-pagination {
    text-align: center;
    margin-top: 10px;
    flex-wrap: wrap;
    justify-content: center;
  }

  /* 搜索区域响应式 */
  .el-form--inline {
    display: block;
  }

  /* 对话框响应式 */
  .el-dialog {
    width: 90% !important;
    margin: 10px auto !important;
  }

  /* 卡片响应式 */
  .el-card {
    margin-bottom: 10px;
  }
}

/* 导航栏和头部按钮特殊处理 */
.dark-theme .el-header .el-button,
.dark-theme .el-header button,
.dark-theme .el-header [class*="el-button"] {
  background-color: #444 !important;
  border-color: #555 !important;
  color: #e0e0e0 !important;
}

.dark-theme .el-header .el-button:hover,
.dark-theme .el-header button:hover,
.dark-theme .el-header [class*="el-button"]:hover {
  background-color: #555 !important;
  border-color: #666 !important;
  color: #fff !important;
}

/* 全局图片预览层级设置 - 确保在所有元素之上 */
.el-image-viewer__wrapper {
  z-index: 99999 !important;
}

.el-image-viewer__mask {
  z-index: 99998 !important;
}

.el-image-viewer__btn {
  z-index: 100000 !important;
}

.el-image-viewer__canvas {
  z-index: 99999 !important;
}

.el-image-viewer__actions {
  z-index: 100001 !important;
}

.el-image-viewer__close {
  z-index: 100002 !important;
}

/* 确保图片预览的所有子元素都有足够高的层级 */
.el-image-viewer__wrapper * {
  z-index: inherit;
}

/* 特定修复 - 针对添加字典按钮 */
.dark-theme .el-row .el-button[style*="float: right"],
.dark-theme .el-row button[style*="float: right"] {
  background-color: #0056b3 !important;
  border-color: #0056b3 !important;
  color: white !important;
}

.dark-theme .el-row .el-button[style*="float: right"]:hover,
.dark-theme .el-row button[style*="float: right"]:hover {
  background-color: #0069d9 !important;
  border-color: #0069d9 !important;
}

/* 特定修复 - 针对表格中的操作区域 */
.dark-theme .el-table__body tr td.el-table__cell:last-child,
.dark-theme .el-table__body tr td.el-table__cell:last-child *,
.dark-theme .el-table__body tr td.el-table__cell:last-child .cell,
.dark-theme .el-table__body tr td.el-table__cell:last-child .cell * {
  background-color: #333 !important;
  color: #ffffff !important;
}

/* 特定修复 - 针对表格中的操作按钮 */
.dark-theme .el-table__body tr td.el-table__cell:last-child .el-button,
.dark-theme .el-table__body tr td.el-table__cell:last-child button,
.dark-theme .el-table__body tr td.el-table__cell:last-child .el-button *,
.dark-theme .el-table__body tr td.el-table__cell:last-child button *,
.dark-theme .el-table__body tr td.el-table__cell .el-button--text,
.dark-theme .el-table__body tr td.el-table__cell button[type="text"],
.dark-theme .el-table__body tr td.el-table__cell button[size="mini"],
.dark-theme .el-table__body tr td.el-table__cell .el-button.is-text,
.dark-theme .el-table__body tr td.el-table__cell .el-button.el-button--text {
  background-color: transparent !important;
  border-color: transparent !important;
  color: #409EFF !important;
}

/* 分页 */
.dark-theme .el-pagination {
  background-color: transparent !important;
  color: #e0e0e0 !important;
}

.dark-theme .el-pagination button,
.dark-theme .el-pagination span:not([class*=suffix]),
.dark-theme .el-pagination .el-select .el-input .el-input__inner {
  background-color: #444 !important;
  color: #e0e0e0 !important;
}

.dark-theme .el-pagination .btn-prev,
.dark-theme .el-pagination .btn-next,
.dark-theme .el-pagination .el-icon {
  background-color: #444 !important;
  color: #e0e0e0 !important;
}

.dark-theme .el-pagination .el-pager li {
  background-color: #444 !important;
  color: #e0e0e0 !important;
}

.dark-theme .el-pagination .el-pager li.active {
  background-color: #0056b3 !important;
  color: white !important;
}

/* 弹出菜单 */
.dark-theme .el-dropdown-menu,
.dark-theme .el-dropdown-menu__item,
.dark-theme .el-select-dropdown,
.dark-theme .el-select-dropdown__item,
.dark-theme .el-cascader__dropdown,
.dark-theme .el-cascader-node,
.dark-theme .el-cascader-panel,
.dark-theme .el-menu--popup {
  background-color: #444 !important;
  color: #e0e0e0 !important;
}

.dark-theme .el-dropdown-menu__item:hover,
.dark-theme .el-select-dropdown__item:hover,
.dark-theme .el-select-dropdown__item.hover,
.dark-theme .el-cascader-node:not(.is-disabled):hover,
.dark-theme .el-cascader-node:not(.is-disabled):focus {
  background-color: #555 !important;
}

/* 导航菜单 */
.dark-theme .el-menu,
.dark-theme .el-menu--horizontal,
.dark-theme .el-menu--vertical,
.dark-theme .el-menu-item,
.dark-theme .el-sub-menu,
.dark-theme .el-sub-menu__title {
  background-color: #1e2a3b !important;
  color: #e0e0e0 !important;
  border-color: #333 !important;
}

.dark-theme .el-menu-item:hover,
.dark-theme .el-menu-item:focus,
.dark-theme .el-sub-menu__title:hover,
.dark-theme .el-sub-menu__title:focus {
  background-color: #2c3e50 !important;
}

.dark-theme .el-menu-item.is-active {
  color: #409EFF !important;
  background-color: rgba(64, 158, 255, 0.1) !important;
}

/* 强制设置弹出菜单样式 */
.dark-theme .el-menu--popup {
  background-color: #1e2a3b !important;
}

.dark-theme .el-menu--popup .el-menu-item {
  background-color: #1e2a3b !important;
  color: #e0e0e0 !important;
}

.dark-theme .el-menu--popup .el-menu-item:hover,
.dark-theme .el-menu--popup .el-menu-item:focus {
  background-color: #2c3e50 !important;
}

.dark-theme .el-menu--popup .el-menu-item.is-active {
  color: #409EFF !important;
  background-color: rgba(64, 158, 255, 0.1) !important;
}

/* 确保菜单项文本颜色 */
.el-menu--vertical .el-menu-item,
.el-menu--vertical .el-sub-menu__title,
.el-menu--horizontal .el-menu-item,
.el-menu--horizontal .el-sub-menu__title,
.sidebar .el-menu-item,
.sidebar .el-sub-menu__title {
  color: #fff !important;
}

.dark-theme .el-select-dropdown__item.selected,
.dark-theme .el-cascader-node.in-active-path,
.dark-theme .el-cascader-node.is-active,
.dark-theme .el-cascader-node.is-selectable.in-checked-path {
  color: var(--primary-color) !important;
  font-weight: bold;
}

/* 标签页 */
.dark-theme .el-tabs__item {
  color: #b0b0b0 !important;
}

.dark-theme .el-tabs__item.is-active {
  color: var(--primary-color) !important;
}

.dark-theme .el-tabs__nav-wrap::after {
  background-color: #444 !important;
}

/* 弹出框 */
.dark-theme .el-dialog,
.dark-theme .el-dialog__header,
.dark-theme .el-dialog__body,
.dark-theme .el-dialog__footer,
.dark-theme .el-message-box,
.dark-theme .el-message-box__header,
.dark-theme .el-message-box__content,
.dark-theme .el-message-box__container,
.dark-theme .el-message-box__title,
.dark-theme .el-message-box__message,
.dark-theme .el-message-box__footer {
  background-color: #333 !important;
  color: #e0e0e0 !important;
}

.dark-theme .el-overlay {
  background-color: rgba(0, 0, 0, 0.7) !important;
}

/* 聊天界面样式 */
.dark-theme .chat-container {
  background: #333;
  color: #e0e0e0;
}

.dark-theme .assistant .message-content {
  background: #444;
  color: #e0e0e0;
}

.dark-theme .chat-input {
  background: #2c2c2c;
  border-top: 1px solid #444;
}

.dark-theme .chat-input textarea {
  background: #444;
  color: #e0e0e0;
  border-color: #555;
}

.dark-theme .chat-input textarea::placeholder {
  color: #999;
}

.dark-theme .typing-indicator {
  background: #444;
}

.dark-theme .typing-indicator span {
  background: #999;
}
