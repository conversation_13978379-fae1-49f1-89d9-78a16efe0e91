<template>
  <div class="work-order-list-container">
    <work-order-edit @search="search" ref="editDialog" />
    <work-order-detail ref="detailDialog" />

    <!-- 搜索区域 -->
    <div class="card card--search search-flex">
      <div class="search-container">
        <div class="search-form">
          <el-input
            v-model="searchModel.userDescribe"
            placeholder="用户描述关键词"
            clearable
            style="width: 180px; margin-right: 16px;"
          />
          <el-select
            v-model="searchModel.type"
            placeholder="工单类型"
            clearable
            style="width: 150px; margin-right: 16px;"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-model="searchModel.status"
            placeholder="工单状态"
            clearable
            style="width: 150px; margin-right: 16px;"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-model="searchModel.regionType"
            placeholder="区域类型"
            clearable
            style="width: 150px; margin-right: 16px;"
          >
            <el-option
              v-for="item in regionTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-buttons">
          <el-button type="primary" @click="search" class="search-btn">搜索</el-button>
          <el-button @click="resetSearch" class="search-btn">重置</el-button>
        </div>
      </div>
      <div class="add-button-container">
        <el-button type="primary" @click="add" class="add-btn">添加工单</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="card card--table">
      <div class="table-col">
        <el-table
          :data="workOrderList"
          row-key="id"
          style="width: 100%; height: 100%;"
          class="data-table"
          v-loading="loading"
        >
          <el-table-column prop="id" label="工单ID" width="80" align="center" />
          <el-table-column prop="type" label="工单类型" width="80" align="center">
            <template #default="scope">
              <el-tag :type="getTypeTagType(scope.row.type)">
                {{ formatType(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ formatStatus(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="userDescribe" label="用户描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="personDescribe" label="处理描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="regionType" label="区域类型" width="100" align="center">
            <template #default="scope">
              {{ formatRegionType(scope.row.regionType) }}
            </template>
          </el-table-column>
          <el-table-column prop="region" label="区域信息" width="150" show-overflow-tooltip />
          <el-table-column label="媒体文件" width="100" align="center">
            <template #default="scope">
              <div class="media-preview">
                <div v-if="getFirstMediaFile(scope.row.media)" class="media-item" @click="previewMedia(scope.row.media)">
                  <img
                    v-if="isImageFile(getFirstMediaFile(scope.row.media))"
                    :src="getMediaUrl(getFirstMediaFile(scope.row.media))"
                    class="media-thumbnail"
                    alt="预览图"
                  />
                  <div v-else-if="isVideoFile(getFirstMediaFile(scope.row.media))" class="video-thumbnail">
                    <el-icon size="24"><VideoPlay /></el-icon>
                  </div>
                  <div v-else class="file-thumbnail">
                    <el-icon size="24"><Document /></el-icon>
                  </div>
                  <span v-if="getMediaFileCount(scope.row.media) > 1" class="file-count">
                    +{{ getMediaFileCount(scope.row.media) - 1 }}
                  </span>
                </div>
                <span v-else class="no-media">无</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="140" align="center" />
          <el-table-column prop="updateTime" label="更新时间" width="140" align="center" />
          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="showDetail(scope.row)">详情</el-button>
              <el-button type="warning" size="small" @click="edit(scope.row.id)">编辑</el-button>
              <el-button type="danger" size="small" @click="deleted(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            layout="prev, pager, next"
            @current-change="currentChange"
            :total="total"
            :page-size="searchModel.pageSize"
            :current-page="searchModel.pageNum"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listWorkOrder, deleteWorkOrder, getWorkOrder, WORK_ORDER_TYPES, REGION_TYPES, WORK_ORDER_STATUS } from '@/api/workOrder'
import { listDictByNameEn } from '@/api/system/dict'
import { getSelectedCommunityId } from '@/store/modules/options'
import communityMixin from '@/mixins/communityMixin'
import { VideoPlay, Document } from '@element-plus/icons-vue'
import mitt from '@/utils/mitt'
import workOrderEdit from '@/components/workOrder/workOrderEdit.vue'
import workOrderDetail from '@/components/workOrder/workOrderDetail.vue'

export default {
  name: 'WorkOrderList',
  mixins: [communityMixin],
  components: {
    workOrderEdit,
    workOrderDetail,
    VideoPlay,
    Document
  },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        userDescribe: '',
        type: '',
        status: '',
        regionType: ''
      },
      workOrderList: [],
      total: 0,
      loading: false,
      // 媒体文件配置
      imgServer: import.meta.env.VITE_BASE_API + '/common-api/v1/file/',
      // 字典选项
      typeOptions: [],
      statusOptions: [],
      regionTypeOptions: []
    }
  },
  methods: {
    /**
     * 搜索工单列表
     */
    async search() {
      this.loading = true
      try {
        // 获取当前选中的小区ID
        const communityId = getSelectedCommunityId()
        if (!communityId) {
          this.$message.warning('请先选择小区')
          return
        }

        // 清理空值参数
        const params = { ...this.searchModel, communityId }
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null || params[key] === undefined) {
            delete params[key]
          }
        })

        const response = await listWorkOrder(params)
        this.workOrderList = response.data?.data?.list || []
        this.total = response.data?.data?.total || 0
      } catch (error) {
        console.error('查询工单列表失败:', error)
        this.$message.error(error.data?.errorMessage || '查询失败')
      } finally {
        this.loading = false
      }
    },

    /**
     * 重置搜索条件
     */
    resetSearch() {
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        userDescribe: '',
        type: '',
        status: '',
        regionType: ''
      }
      this.search()
    },

    /**
     * 添加工单
     */
    add() {
      mitt.emit('openWorkOrderAdd')
    },

    /**
     * 编辑工单
     */
    edit(id) {
      getWorkOrder(id).then(res => {
        mitt.emit('openWorkOrderEdit', res.data.data)
      }).catch(err => {
        this.$message.error(err.data?.errorMessage || '获取工单详情失败')
      })
    },

    /**
     * 删除工单
     */
    deleted(id) {
      this.$confirm('删除工单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteWorkOrder(id).then(() => {
          this.search()
          this.$message.success('操作成功')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '删除失败')
        })
      }).catch(() => {})
    },

    /**
     * 显示详情
     */
    showDetail(row) {
      getWorkOrder(row.id).then(res => {
        mitt.emit('openWorkOrderDetail', res.data.data)
      }).catch(err => {
        this.$message.error(err.data?.errorMessage || '获取工单详情失败')
      })
    },

    /**
     * 分页变化
     */
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    /**
     * 小区变化监听
     */
    onCommunityChange(community) {
      this.workOrderList = []
      if (community) {
        this.search()
      }
    },

    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载工单类型字典
        const typeRes = await listDictByNameEn('work_order_type')
        this.typeOptions = (typeRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载工单状态字典
        const statusRes = await listDictByNameEn('work_order_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载区域类型字典
        const regionRes = await listDictByNameEn('region_type')
        this.regionTypeOptions = (regionRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 如果字典数据为空，使用本地常量
        if (this.typeOptions.length === 0) {
          this.typeOptions = WORK_ORDER_TYPES
        }
        if (this.statusOptions.length === 0) {
          this.statusOptions = WORK_ORDER_STATUS
        }
        if (this.regionTypeOptions.length === 0) {
          this.regionTypeOptions = REGION_TYPES
        }
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用本地常量作为备选
        this.typeOptions = WORK_ORDER_TYPES
        this.statusOptions = WORK_ORDER_STATUS
        this.regionTypeOptions = REGION_TYPES
      }
    },

    /**
     * 格式化工单类型
     */
    formatType(type) {
      const option = this.typeOptions.find(item => item.value === type)
      return option ? option.label : type
    },

    /**
     * 格式化工单状态
     */
    formatStatus(status) {
      const option = this.statusOptions.find(item => item.value === status)
      return option ? option.label : status
    },

    /**
     * 格式化区域类型
     */
    formatRegionType(regionType) {
      const option = this.regionTypeOptions.find(item => item.value === regionType)
      return option ? option.label : regionType
    },

    /**
     * 获取类型标签样式
     */
    getTypeTagType(type) {
      const typeMap = {
        'repair': 'warning',
        'complaint': 'danger',
        'suggestion': 'success',
        'other': 'info'
      }
      return typeMap[type] || 'info'
    },

    /**
     * 获取状态标签样式
     */
    getStatusTagType(status) {
      const statusMap = {
        'wait_process': 'warning',
        'processing': 'primary',
        'completed': 'success',
        'cancel': 'info'
      }
      return statusMap[status] || 'info'
    },

    /**
     * 获取第一个媒体文件路径
     */
    getFirstMediaFile(media) {
      if (!media || typeof media !== 'string') return null
      const files = media.split(',').map(file => file.trim()).filter(Boolean)
      return files.length > 0 ? files[0] : null
    },

    /**
     * 获取媒体文件数量
     */
    getMediaFileCount(media) {
      if (!media || typeof media !== 'string') return 0
      return media.split(',').map(file => file.trim()).filter(Boolean).length
    },

    /**
     * 构建媒体文件完整URL
     */
    getMediaUrl(filePath) {
      if (!filePath) return ''
      return this.imgServer + filePath
    },

    /**
     * 判断是否为图片文件
     */
    isImageFile(filePath) {
      if (!filePath) return false
      return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(filePath)
    },

    /**
     * 判断是否为视频文件
     */
    isVideoFile(filePath) {
      if (!filePath) return false
      return /\.(mp4|avi|mov|wmv|flv|webm)$/i.test(filePath)
    },

    /**
     * 预览媒体文件
     */
    previewMedia(media) {
      if (!media) return

      const files = media.split(',').map(file => file.trim()).filter(Boolean)
      if (files.length === 0) return

      // 构建完整的文件URL列表
      const fileUrls = files.map(filePath => ({
        url: this.imgServer + filePath,
        isImage: this.isImageFile(filePath),
        isVideo: this.isVideoFile(filePath),
        name: filePath.split('/').pop() || filePath
      }))

      // 过滤出图片文件
      const imageUrls = fileUrls.filter(file => file.isImage).map(file => file.url)

      if (imageUrls.length > 0) {
        // 创建图片预览
        this.showImagePreview(imageUrls)
      } else {
        this.$message.info('该工单包含视频文件，请在详情中查看')
      }
    },

    /**
     * 显示图片预览
     */
    showImagePreview(imageUrls) {
      // 创建图片预览弹窗
      const previewContainer = document.createElement('div')
      previewContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      `

      const img = document.createElement('img')
      img.src = imageUrls[0]
      img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
      `

      previewContainer.appendChild(img)
      document.body.appendChild(previewContainer)

      // 点击关闭预览
      previewContainer.addEventListener('click', () => {
        document.body.removeChild(previewContainer)
      })
    }
  },

  async created() {
    await this.initDictData()
    this.search()
  }
}
</script>

<style scoped>
.work-order-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

/* 搜索区域样式 */
.search-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  gap: 16px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-form {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.search-buttons {
  display: flex;
  gap: 8px;
}

.add-button-container {
  flex-shrink: 0;
}

/* 表格区域样式 */
.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
}

::v-deep .el-table__body-wrapper {
  flex: 1;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.pagination-container .el-pagination {
  margin: 0;
}

/* 媒体文件预览样式 */
.media-preview {
  display: flex;
  justify-content: center;
  align-items: center;
}

.media-item {
  position: relative;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;
}

.media-item:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.media-thumbnail {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.video-thumbnail,
.file-thumbnail {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  color: #909399;
}

.file-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #409eff;
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
  line-height: 1.2;
}

.no-media {
  color: #c0c4cc;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-flex {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-input,
  .search-form .el-select {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  .media-thumbnail {
    width: 32px;
    height: 32px;
  }

  .video-thumbnail,
  .file-thumbnail {
    width: 32px;
    height: 32px;
  }
}
</style>
