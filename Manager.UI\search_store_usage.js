const fs = require('fs');
const path = require('path');

// 搜索所有使用 $store 的文件
function searchStoreUsage(dir) {
  const results = [];
  
  function searchInDirectory(currentDir) {
    const files = fs.readdirSync(currentDir);
    
    for (const file of files) {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        // 跳过 node_modules 和 .git 目录
        if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
          searchInDirectory(filePath);
        }
      } else if (file.endsWith('.vue') || file.endsWith('.js')) {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          const lines = content.split('\n');
          
          lines.forEach((line, index) => {
            // 搜索 $store 使用
            if (line.includes('$store') || line.includes('this.$store')) {
              results.push({
                file: filePath,
                line: index + 1,
                content: line.trim()
              });
            }
          });
        } catch (error) {
          console.error(`Error reading file ${filePath}:`, error.message);
        }
      }
    }
  }
  
  searchInDirectory(dir);
  return results;
}

// 执行搜索
const srcDir = path.join(__dirname, 'src');
const results = searchStoreUsage(srcDir);

console.log('=== $store 使用情况搜索结果 ===\n');

if (results.length === 0) {
  console.log('未找到任何 $store 使用');
} else {
  console.log(`找到 ${results.length} 处 $store 使用:\n`);
  
  // 按文件分组显示结果
  const groupedResults = {};
  results.forEach(result => {
    const relativePath = path.relative(srcDir, result.file);
    if (!groupedResults[relativePath]) {
      groupedResults[relativePath] = [];
    }
    groupedResults[relativePath].push(result);
  });
  
  Object.keys(groupedResults).forEach(file => {
    console.log(`📁 ${file}:`);
    groupedResults[file].forEach(result => {
      console.log(`   第${result.line}行: ${result.content}`);
    });
    console.log('');
  });
}

console.log('=== 搜索完成 ===');
