<template>
  <div class="storage-test">
    <h2>存储测试页面</h2>
    
    <div class="test-section">
      <h3>Pinia 存储状态</h3>
      <div class="info-item">
        <label>用户名:</label>
        <span>{{ userName || '未设置' }}</span>
      </div>
      <div class="info-item">
        <label>Token:</label>
        <span>{{ hasToken ? '已设置' : '未设置' }}</span>
      </div>
      <div class="info-item">
        <label>主题:</label>
        <span>{{ theme }}</span>
      </div>
      <div class="info-item">
        <label>选中小区:</label>
        <span>{{ selectedCommunity ? selectedCommunity.name : '未选择' }}</span>
      </div>
    </div>

    <div class="test-section">
      <h3>操作测试</h3>
      <el-button @click="testSetUserName" type="primary">设置用户名</el-button>
      <el-button @click="testSetToken" type="primary">设置Token</el-button>
      <el-button @click="testToggleTheme" type="primary">切换主题</el-button>
      <el-button @click="testClearAll" type="danger">清空所有</el-button>
    </div>

    <div class="test-section">
      <h3>原始数据</h3>
      <pre>{{ JSON.stringify(allData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StorageTest',
  
  data() {
    return {
      refreshKey: 0
    }
  },

  computed: {
    userName() {
      this.refreshKey; // 触发响应式更新
      return window.$local?.get('userName') || null
    },
    
    hasToken() {
      this.refreshKey;
      return !!window.$local?.get('smartPropertyToken')
    },
    
    theme() {
      this.refreshKey;
      return window.$local?.get('theme') || 'light'
    },
    
    selectedCommunity() {
      this.refreshKey;
      return window.$local?.get('selectedCommunity') || null
    },
    
    allData() {
      this.refreshKey;
      if (!window.$local) return { error: 'Pinia store not available' }
      
      return {
        userName: window.$local.get('userName'),
        smartPropertyToken: window.$local.get('smartPropertyToken') ? '已设置' : null,
        theme: window.$local.get('theme'),
        selectedCommunity: window.$local.get('selectedCommunity'),
        communityList: window.$local.get('communityList'),
        smartPropertyUserInfo: window.$local?.getUserInfo(this.$Base64) ? '已设置' : null,
        frontPermissions: window.$local.get('frontPermissions') ? '已设置' : null,
        dictList: window.$local.get('dictList') ? '已设置' : null
      }
    }
  },

  methods: {
    testSetUserName() {
      const testName = 'test_user_' + Date.now()
      window.$local?.setUserName(testName)
      this.refreshData()
      this.$message.success('用户名设置成功: ' + testName)
    },
    
    testSetToken() {
      const testToken = JSON.stringify({
        access_token: 'test_token_' + Date.now(),
        expires_time: Date.now() + 3600000
      })
      window.$local?.setSmartPropertyToken(testToken)
      this.refreshData()
      this.$message.success('Token设置成功')
    },
    
    testToggleTheme() {
      const currentTheme = window.$local?.get('theme') || 'light'
      const newTheme = currentTheme === 'light' ? 'dark' : 'light'
      window.$local?.setTheme(newTheme)
      this.refreshData()
      this.$message.success('主题切换为: ' + newTheme)
    },
    
    testClearAll() {
      window.$local?.removeAll()
      this.refreshData()
      this.$message.success('所有数据已清空')
    },
    
    refreshData() {
      this.refreshKey++
    }
  },

  mounted() {
    console.log('存储测试页面加载，window.$local:', window.$local)
    if (!window.$local) {
      this.$message.error('Pinia store 未初始化')
    }
  }
}
</script>

<style scoped>
.storage-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.info-item label {
  width: 120px;
  font-weight: bold;
}

.info-item span {
  color: #666;
}

pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
