
import request from '@/utils/request'

// 通过id查询小区访客
export const getVisitor = (id) =>
  request({
    url: '/manage-api/v1/community/visitor',
    method: 'get',
    params: { id }
  })

/*
返回res
{
    "id": 9007199254740991, // 小区访客id
    "visitorName": "string", // 访客姓名
    "phone": "string", // 手机号
    "vehicleNumber": "string", // 车牌号
    "note": "string", // 备注
    "stayDuration": 1073741824, // 停留时长 选择 1 2 4 8 12 24 48
    "timeUnit": "string", // 时间单位  时间单位 hours 小时
    "visitTime": "2025-06-24T06:29:57.841Z", // 来访时间
    "createTime": "2025-06-24T06:29:57.841Z", // 创建时间
    "updateTime": "2025-06-24T06:29:57.841Z", // 更新时间
    "purpose": "string", // 访问目的
    "residentId": 9007199254740991, // 被访住户id
    "communityId": 9007199254740991, // 小区id
    "status": "string", // 访客状态
    "isUsual": true, // 是否常用
    "verifyBy": "string", // 核销码核验人
    "residentName": "string", // 被访住户姓名
    "residentPhone": "string", // 被访住户手机号
    "residentAddress": "string" // 被访访问住址
}
*/



// 分页查询小区访客
export const listVisitor = (data) =>
  request({
    url: '/manage-api/v1/community/visitor/page',
    method: 'get',
    params: data
  })

// 添加小区访客
export const addVisitor = (data) =>
  request({
    url: '/manage-api/v1/community/visitor',
    method: 'post',
    data
  })

/*
入参{
  "visitorName": "string", // 访客姓名
  "phone": "string", // 手机号
  "vehicleNumber": "string", // 车牌号
  "note": "string", // 备注
  "stayDuration": 1073741824, // 停留时长
  "timeUnit": "string", // 时间单位 hours 小时
  "purpose": "string", // 访问目的
  "visitTime": "2025-06-24T06:42:43.039Z", // 来访时间
  "residentId": 9007199254740991, // 住户id
  "communityId": 9007199254740991 // 小区id
}
*/

// 修改小区访客
export const editVisitor = (data) =>
  request({
    url: '/manage-api/v1/community/visitor',
    method: 'put',
    data
  })

// 删除小区访客
export const deleteVisitor = (id) =>
  request({
    url: '/manage-api/v1/community/visitor',
    method: 'delete',
    params: { id }
  })

/*
返回res{
    "total": 9007199254740991,
    "list": [
      {
        "id": 9007199254740991, // id
        "visitorName": "string", // 访客姓名
        "phone": "string", // 手机号
        "vehicleNumber": "string", // 车牌号
        "note": "string", // 备注
        "stayDuration": 1073741824, // 停留时长
        "timeUnit": "string", // 时间单位
        "purpose": "string", // 访问目的
        "visitTime": "2025-06-24T06:42:43.052Z", // 来访时间
        "createTime": "2025-06-24T06:42:43.052Z", // 创建时间
        "updateTime": "2025-06-24T06:42:43.052Z", // 更新时间
        "residentId": 9007199254740991, // 被访住户id
        "communityId": 9007199254740991, // 小区id
        "status": "string", // 访客状态
        "isUsual": true, // 是否常用
        "verifyBy": "string" // 核验人
      }
    ],
    "pageNum": 1073741824,
    "pageSize": 1073741824,
    "size": 1073741824,
    "startRow": 9007199254740991,
    "endRow": 9007199254740991,
    "pages": 1073741824,
    "prePage": 1073741824,
    "nextPage": 1073741824,
    "isFirstPage": true,
    "isLastPage": true,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 1073741824,
    "navigatepageNums": [
      1073741824
    ],
    "navigateFirstPage": 1073741824,
    "navigateLastPage": 1073741824
}
*/