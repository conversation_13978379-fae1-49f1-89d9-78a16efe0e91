/**
 * 小区住户房产管理API
 */
import request from '@/utils/request'

/**
 * 分页查询住户房产列表
 * @param {Object} params - 查询参数
 * @param {number} params.residentId - 住户ID
 * @param {number} params.communityId - 小区ID
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise} 房产列表
 */
export function listResidentProperty(params) {
  return request({
    url: '/manage-api/v1/community/resident/room/page',
    method: 'get',
    params
  })
}


/**
 * 新增住户房产
 * @param {Object} data - 房产数据
 * @param {number} data.residentId - 住户ID
 * @param {number} data.communityId - 小区ID
 * @param {number} data.buildingId - 楼栋ID
 * @param {number} data.roomId - 房间ID
 * @param {string} data.residentType - 住户类型（owner:业主, tenant:租户, family:家属）
 * @param {string} data.relationshipType - 关系类型（self:本人, spouse:配偶, child:子女, parent:父母, other:其他）
 * @param {string} data.startDate - 开始日期
 * @param {string} data.endDate - 结束日期（租户必填）
 * @param {string} data.status - 状态（normal:正常, expired:过期, disabled:禁用）
 * @param {string} data.note - 备注
 * @returns {Promise} 新增结果
 */
export function addResidentProperty(data) {
  return request({
    url: '/manage-api/v1/community/resident/room',
    method: 'post',
    data
  })
}

/**
 * 编辑住户房产
 * @param {Object} data - 房产数据（包含id）
 * @returns {Promise} 编辑结果
 */
export function editResidentProperty(data) {
  return request({
    url: '/manage-api/v1/community/resident/room',
    method: 'put',
    data
  })
}

/**
 * 删除住户房产
 * @param {number} id - 房产ID
 * @returns {Promise} 删除结果
 */
export function deleteResidentProperty(params) {
  return request({
    url: '/manage-api/v1/community/resident/room',
    method: 'delete',
    params: params
  })
}




/**
 * 关系类型字典
 */
export const RELATIONSHIP_TYPES = [
  { value: 'self', label: '本人' },
  { value: 'spouse', label: '配偶' },
  { value: 'child', label: '子女' },
  { value: 'parent', label: '父母' },
  { value: 'other', label: '其他' }
]

/**
 * 状态字典
 */
export const PROPERTY_STATUS = [
  { value: 'normal', label: '正常' },
  { value: 'expired', label: '过期' },
  { value: 'disabled', label: '禁用' }
]
