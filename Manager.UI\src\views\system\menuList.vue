<template>
	<div class="menu-list-container">
		<div class="menu-list-content">
			<menu-edit :statusList="statusList" :typeList="typeList" :appTypeList="appTypeList" @search="search"></menu-edit>
			<div class="card card--search search-flex" style="align-items: center;">
				<el-input v-model="searchModel.menuName" placeholder="菜单名" clearable style="width: 200px; margin-right: 16px;" />
				

						<el-select style="width: 200px;" v-model="searchModel.appType" clearable placeholder="应用类型">
							<el-option v-for="item in appTypeList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
				

				<el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
				<el-button type="primary" @click="add(0)">添加</el-button>
			</div>
			<div class="card card--table">
				<div class="table-col">
					<el-table :data="menuList" row-key="id" style="width: 100%; height: 100%;" class="data-table">
						<el-table-column prop="menuName" header-align="center" label="菜单名称" width="180" />
						<el-table-column prop="sort" header-align="center" label="排序" width="60" />
						<el-table-column prop="path" align="center" label="路径名" width="180" />
						<el-table-column prop="permission" align="center" label="权限" />
						<el-table-column prop="status" align="center" :formatter="formatStatus" label="状态" />
						<el-table-column prop="menuType" align="center" :formatter="formatType" label="类型" />
						<el-table-column prop="appType" align="center" :formatter="formatAppType" label="应用类型" width="100" />
						
						<el-table-column align="center" width="200" label="操作">
							<template #default="scope">
								<el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
								<el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
								<el-button v-show="scope.row.menuType == 'menu'" :span="4" :push="6" type="text" size="mini"
									@click="add(scope.row.id)">添加</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="pagination-col">
					<el-pagination background layout="prev, pager, next" @current-change="currentChange" @prev-click="prevClick"
						@next-click="nextClick" :total="total"></el-pagination>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { listMenu, deleteMenu, getMenu } from "@/api/system/menu"
import { listDictByNameEn } from "@/api/system/dict"
import mitt from "@/utils/mitt";
import menuEdit from "@/components/system/menuEdit.vue"
export default {
	components: { menuEdit },
	data() {
		return {
			searchModel: {
				pageNum: 1,
				pageSize: 10
			},
			menuList: [],
			statusList: [],
			typeList: [],
			appTypeList: [],
			total: 0
		}
	},
	methods: {
		search() {
			listMenu(this.searchModel)
				.then(res => {
					this.menuList = res.data.data.list
					this.total = res.data.data.total
				}).catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		edit(id) {
			getMenu(id)
				.then(res => {
					mitt.emit('openMenuEdit', res.data.data)
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		add(id) {
			mitt.emit('openMenuAdd', id)
		},
		deleted(id) {
			this.$confirm('删除用户, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deleteMenu(id)
					.then(res => {
						this.search()
						this.$message.success("操作成功")
					}).catch((res) => {
						this.$message.error(err.data.errorMessage)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		formatStatus(row, column, cellValue, index) {
			let result = ''
			for (let item of this.statusList) {
				if (item.nameEn == cellValue) {
					result = item.nameCn
				}
			}
			return result
		},
		formatType(row, column, cellValue, index) {
			let result = ''
			for (let item of this.typeList) {
				if (item.nameEn == cellValue) {
					result = item.nameCn
				}
			}
			return result
		},
		formatAppType(row, column, cellValue, index) {
			let result = ''
			for (let item of this.appTypeList) {
				if (item.nameEn == cellValue) {
					result = item.nameCn
				}
			}
			return result
		},
		async init() {
			mitt.off('openMenuEdit')
			mitt.off('openMenuAdd')
			try {
				const [menu_res, status_res, type_res, appType_res] = await Promise.all([
					listMenu(this.searchModel),
					listDictByNameEn('menu_status'),
					listDictByNameEn('menu_type'),
					listDictByNameEn('app_type')
				])
				this.menuList = menu_res.data.data.list
				this.total = menu_res.data.data.total
				this.statusList = status_res.data.data
				this.typeList = type_res.data.data
				this.appTypeList = appType_res.data.data
			} catch (err) {
				this.$message.error(err.data.errorMessage)
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
.menu-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.menu-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

/* 暗色主题样式 */
.dark-theme .card {
  background-color: var(--card-background);
  border: 1px solid #444;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}
</style> 