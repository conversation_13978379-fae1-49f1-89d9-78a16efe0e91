import request from '@/utils/request'
//物业缴费项目-----------------------------------------------------------------------------
// 分页查询物业缴费项目
export function listPropertyPaymentItems(params) {
  return request({
    url: '/manage-api/v1/property/payment/items/page',
    method: 'get',
    params
  })
}

// 入参
// {
//   "pageNum": 1,
//   "pageSize": 10
//   "paymentItemName": "string",
//   "paymentItemDescribe": "string"
// }
// 返回
// {
//   "errorMessage": "string",
//   "code": 1073741824,
//   "data": {
//     "total": 9007199254740991,
//     "list": [
//       {
//         "id": 9007199254740991,//物业缴费项目ID
//         "paymentItemName": "string",//缴费项目名
//         "paymentItemDescribe": "string",//缴费项目描述
//         "communityId": 9007199254740991,//小区id
//         "createTime": "2025-07-08T12:58:36.737Z",//创建时间
//         "updateTime": "2025-07-08T12:58:36.737Z",//修改时间
//         "note": "string",//备注
//         "isActive": true,//是否启用
//         "billingCycle": "string",//计费周期（月度，季度，年度）
//         "unitPrice": 0.1,//单价
//         "unit": "string"//单位
//       }
//     ],
//     "pageNum": 1073741824,
//     "pageSize": 1073741824,
//     "size": 1073741824,
//     "startRow": 9007199254740991,
//     "endRow": 9007199254740991,
//     "pages": 1073741824,
//     "prePage": 1073741824,
//     "nextPage": 1073741824,
//     "isFirstPage": true,
//     "isLastPage": true,
//     "hasPreviousPage": true,
//     "hasNextPage": true,
//     "navigatePages": 1073741824,
//     "navigatepageNums": [
//       1073741824
//     ],
//     "navigateFirstPage": 1073741824,
//     "navigateLastPage": 1073741824
//   }
// }



// 通过ID查询物业缴费项目
export function getPropertyPaymentItems(id) {
  return request({
    url: '/manage-api/v1/property/payment/items',
    method: 'get',
    params: { id }
  })
}

// 新增物业缴费项目
export function addPropertyPaymentItems(data) {
  return request({
    url: '/manage-api/v1/property/payment/items',
    method: 'post',
   data
  })
}

// 编辑物业缴费项目
export function editPropertyPaymentItems(data) {
  return request({
    url: '/manage-api/v1/property/payment/items',
    method: 'put',
    data
  })
}

// 删除物业缴费项目
export function deletePropertyPaymentItems(id) {
  return request({
    url: '/manage-api/v1/property/payment/items',
    method: 'delete',
    params: { id }
  })
} 



//缴费对象-----------------------------------------------------------------------
//分页查询缴费对象
export function listPropertyPaymentObject(data) {
  return request({
    url: '/manage-api/v1/property/payment/object/page',
    method: 'get',
    params: data
  })
}
// 接口描述
// 入参:
// {
//    "pageNum": 1,
//   "pageSize": 10
//   "moniker": "string",
//   "phone": "string",
//   "communityId": 9007199254740991,
//   "email": "string",
//   "residentId": 9007199254740991
// }
// 返回
// {
//   "errorMessage": "string",
//   "code": 1073741824,
//   "data": {
//     "total": 9007199254740991,
//     "list": [
//       {
//         "id": 9007199254740991,//缴费对象ID
//         "moniker": "string",//名称
//         "phone": "string",//手机号
//         "address": "string",//地址
//         "communityId": 9007199254740991,//小区ID
//         "email": "string",//邮箱
//         "residentId": 9007199254740991,//住户ID
//         "createTime": "2025-07-08T12:53:05.205Z",//创建时间
//         "updateTime": "2025-07-08T12:53:05.205Z"//更新时间
//       }
//     ],
//     "pageNum": 1073741824,
//     "pageSize": 1073741824,
//     "size": 1073741824,
//     "startRow": 9007199254740991,
//     "endRow": 9007199254740991,
//     "pages": 1073741824,
//     "prePage": 1073741824,
//     "nextPage": 1073741824,
//     "isFirstPage": true,
//     "isLastPage": true,
//     "hasPreviousPage": true,
//     "hasNextPage": true,
//     "navigatePages": 1073741824,
//     "navigatepageNums": [
//       1073741824
//     ],
//     "navigateFirstPage": 1073741824,
//     "navigateLastPage": 1073741824
//   }
// }



//通过id查询缴费对象
export function getPropertyPaymentObject(id) {
  return request({
    url: '/manage-api/v1/property/payment/object',
    method: 'get',
    params: { id }
  })
}

// 返回
// {
//   "errorMessage": "string",
//   "code": 1073741824,
//   "data": {
//     "id": 9007199254740991,
//     "moniker": "string",
//     "phone": "string",
//     "address": "string",
//     "communityId": 9007199254740991,
//     "email": "string",
//     "residentId": 9007199254740991,
//     "createTime": "2025-07-08T12:55:13.066Z",
//     "updateTime": "2025-07-08T12:55:13.066Z"
//   }
// }


//编辑缴费对象
export function editPropertyPaymentObject(data) {
  return request({
    url: '/manage-api/v1/property/payment/object',
    method: 'put',
    data
  })
}

//新增缴费对象
export function addPropertyPaymentObject(data) {
  return request({
    url: '/manage-api/v1/property/payment/object',
    method: 'post',
    data
  })
}

//如果选择住户,则弹出住户选择列表窗口,单选,residentId字段保存住户的residentId,如果不选择住户,moniker需要填写对象名称,residentId设为空字符串
// 入参
// {
//   "moniker": "string",
//   "phone": "string",
//   "address": "string",
//   "communityId": 9007199254740991,
//   "email": "string",
//   "residentId": 9007199254740991
// }



//删除缴费对象
export function deletePropertyPaymentObject(id) {
  return request({
    url: '/manage-api/v1/property/payment/object',
    method: 'delete',
    params: { id }
  })
}



//缴费明细
//分页查询物业缴费明细
export function listPropertyPaymentDetail(params) {
  return request({
    url: '/manage-api/v1/property/payment/detail/page',
    method: 'get',
    params
  })
}

// 入参{
//   "pageNum": 1073741824,
//   "pageSize": 500,
//   "paymentObjectId": 9007199254740991,
//   "communityId": 9007199254740991
// }

// 返回
// {
//   "errorMessage": "string",
//   "code": 1073741824,
//   "data": {
//     "total": 9007199254740991,
//     "list": [
//       {
//         "id": 9007199254740991,//ID
//         "paymentItemSnapshot": "string",//缴费项目快照
//         "unitPrice": 0.1,//单价
//         "unit": "string",//单位
//         "quantity": 0.1,//数量（多少度电，多少面积）
//         "discount": 0.1,//折扣（0-10）
//         "status": "string",//状态
//         "createTime": "2025-07-09T01:04:35.391Z",//创建时间
//         "updateTime": "2025-07-09T01:04:35.391Z",//修改时间
//         "note": "string",//备注
//         "communityId": 9007199254740991,//小区ID
//         "paymentObjectId": 9007199254740991,//缴费对象ID
//         "billingCycle": "string",//计费周期（月度，季度，年度）
//         "billDay": "string",//账单日（0_6_18:00，1_16_18:00，7_30_18:00）
//         "effectiveDate": "2025-07-09T01:04:35.391Z",//生效日期
//         "expiringDate": "2025-07-09T01:04:35.391Z"//失效日期
//       }
//     ],
//     "pageNum": 1073741824,
//     "pageSize": 1073741824,
//     "size": 1073741824,
//     "startRow": 9007199254740991,
//     "endRow": 9007199254740991,
//     "pages": 1073741824,
//     "prePage": 1073741824,
//     "nextPage": 1073741824,
//     "isFirstPage": true,
//     "isLastPage": true,
//     "hasPreviousPage": true,
//     "hasNextPage": true,
//     "navigatePages": 1073741824,
//     "navigatepageNums": [
//       1073741824
//     ],
//     "navigateFirstPage": 1073741824,
//     "navigateLastPage": 1073741824
//   }
// }


//添加物业缴费明细
export function addPropertyPaymentDetail(data) { 
  return request({
    url: '/manage-api/v1/property/payment/detail',
    method: 'post',
    data
  })
}

// 入参
// {
//   "id": 9007199254740991,//ID
//   "paymentItemSnapshot": "string",//缴费项目快照
//   "unitPrice": 0.1,//单价
//   "unit": "string",//单位
//   "quantity": 0.1,//数量（多少度电，多少面积）
//   "discount": 0.1,//折扣（0-10）
//   "status": "string",//状态
//   "note": "string",//备注
//   "communityId": 9007199254740991,//小区ID 当前小区id
//   "paymentObjectId": 9007199254740991,//缴费对象ID 显示缴费对象列表,选择缴费对象,保存缴费对象的id
//   "billingCycle": "string",//计费周期（月度，季度，年度）
//   "billDay": "string",//账单日（0_6_18:00，1_16_18:00，7_30_18:00 月度:每月6号18:00 季度:每季度开始第一个月的16号 18:00 年度:每年7月30号 18:00）
//   "effectiveDate": "2025-07-09T01:08:14.471Z",//生效日期
//   "expiringDate": "2025-07-09T01:08:14.471Z"//失效日期
// }


//编辑物业缴费明细
export function editPropertyPaymentDetail(data) { 
  return request({
    url: '/manage-api/v1/property/payment/detail',
    method: 'put',
    data
  })
}

//删除物业缴费明细
export function deletePropertyPaymentDetail(id) { 
  return request({
    url: '/manage-api/v1/property/payment/detail?id='+id,
    method: 'delete',
  })
}


//通过id查询物业缴费明细
export function getPropertyPaymentDetail(id) { 
  return request({
    url: '/manage-api/v1/property/payment/detail?id='+id,
    method: 'get',
  })
}



//缴费账单列表页-----------------------------------------------------------------------
//分页查询缴费账单
export function listPropertyPaymentBills(data) {
  return request({
    url: '/manage-api/v1/property/payment/bill/page',
    method: 'get',
    params: data
  })
}


// 入参
// {
//   "pageNum": 1,
//   "pageSize": 10
// }

// 返回
// {
//   "errorMessage": "string",
//   "code": 1073741824,
//   "data": [
//     {
//       "id": 9007199254740991,//缴费账单ID
//       "paymentObjectId": 9007199254740991,//缴费对象ID
//       "unitPrice": 0.1,//单价
//       "unit": "string",//单位
//       "quantity": 1073741824,//数量
//       "status": "string",//状态
//       "communityId": 9007199254740991,//小区ID
//       "payNo": "string",//支付单号
//       "refundNo": "string",//退款单号
//       "payTime": "2025-07-08T23:35:55.182Z",//支付时间
//       "refundTime": "2025-07-08T23:35:55.182Z",//退款时间
//       "payType": "string",//支付类型
//       "billDate": "2025-07-08T23:35:55.182Z",//账单日期
//       "paymentDetailId": 9007199254740991,//缴费明细ID（合同ID）
//       "lastQuantity": 1073741824,//上期示数
//       "createTime": "2025-07-08T23:35:55.182Z",//创建时间
//       "updateTime": "2025-07-08T23:35:55.182Z",//修改时间
//       "payAmount": 0.1,//支付金额
//       "discountAmount": 0.1,//优惠金额
//       "totalAmount": 0.1//总金额
//     }
//   ]
// }



//通过id查询缴费账单
export function getPropertyPaymentBill(id) {
  return request({
    url: '/manage-api/v1/property/payment/bill',
    method: 'get',
    params: { id }
  })
}

//编辑缴费账单
export function editPropertyPaymentBill(data) {
  return request({
    url: '/manage-api/v1/property/payment/bill',
    method: 'put',
    data
  })
}

//新增缴费账单
export function addPropertyPaymentBill(data) {
  return request({
    url: '/manage-api/v1/property/payment/bill',
    method: 'post',
    data
  })
}

// 新增物业账单入参{
//   "paymentObjectId": 9007199254740991,    // 缴费对象ID（必填）
//   "unitPrice": 0.1,                      // 单价（从缴费明细获取）
//   "unit": "string",                      // 单位（从缴费明细获取）
//   "quantity": 1073741824,                // 数量（必填）
//   "status": "string",                    // 状态（必填，使用字典）
//   "communityId": 9007199254740991,       // 小区ID（必填）
//   "billDate": "2025-07-09T11:07:11.557Z", // 账单日期（必填）
//   "paymentDetailId": 9007199254740991,   // 缴费明细ID（必填）
//   "lastQuantity": 1073741824,            // 上次数量（从缴费明细获取）
//   "payAmount": 0.1,                      // 支付金额（可手动修改）
//   "discountAmount": 0.1,                 // 优惠金额（自动计算）
//   "totalAmount": 0.1                     // 总金额（数量×单价）
// }
//
// 计算逻辑：
// 1. 总金额 = 数量 × 单价
// 2. 选择缴费明细后：优惠金额 = 总金额 - 总金额 × 折扣%，支付金额 = 总金额 - 优惠金额
// 3. 手动修改支付金额后：优惠金额 = 总金额 - 支付金额


//删除缴费账单
export function deletePropertyPaymentBill(id) {
  return request({
    url: '/manage-api/v1/property/payment/bill',
    method: 'delete',
    params: { id }
  })
}

