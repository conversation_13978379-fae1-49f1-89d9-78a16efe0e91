<template>
  <el-dialog
    title="定时任务详情"
    v-model="dialog.show"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="job-detail" v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="任务ID">
          {{ jobDetail.id }}
        </el-descriptions-item>
        <el-descriptions-item label="任务名称">
          {{ jobDetail.jobName }}
        </el-descriptions-item>
        <el-descriptions-item label="任务分组">
          {{ getJobGroupLabel(jobDetail.jobGroup) }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTagType(jobDetail.status)">
            {{ getStatusLabel(jobDetail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="调用目标" :span="2">
          <div class="code-block">{{ jobDetail.invokeTarget }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="Cron表达式" :span="2">
          <div class="code-block">{{ jobDetail.cronExpression }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="错失执行策略">
          {{ getMisfirePolicyLabel(jobDetail.misfirePolicy) }}
        </el-descriptions-item>
        <el-descriptions-item label="是否并发">
          {{ getConcurrentLabel(jobDetail.concurrent) }}
        </el-descriptions-item>
        <el-descriptions-item label="日志保留天数">
          {{ jobDetail.logSaveDay }} 天
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ jobDetail.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人">
          {{ jobDetail.createBy }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ jobDetail.updateTime }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          {{ jobDetail.remark || '无' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  getJob,
  MISFIRE_POLICY
} from '@/api/job'
import { listDictByNameEn } from '@/api/system/dict'

export default {
  name: 'JobDetail',
  data() {
    return {
      dialog: {
        show: false
      },
      loading: false,
      jobDetail: {},
      // 字典数据
      statusOptions: [],
      jobGroupOptions: []
    }
  },

  methods: {
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载任务状态字典
        const statusRes = await listDictByNameEn('job_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载任务组字典
        const groupRes = await listDictByNameEn('jobGroups')
        this.jobGroupOptions = (groupRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用默认数据
        this.statusOptions = [
          { value: 'run', label: '运行中' },
          { value: 'stop', label: '已停止' }
        ]
        this.jobGroupOptions = [
          { value: 'default', label: '默认组' },
          { value: 'system', label: '系统组' },
          { value: 'business', label: '业务组' }
        ]
      }
    },

    /**
     * 打开对话框
     */
    open(job) {
      this.dialog.show = true
      this.loading = true
      
      // 初始化字典数据
      this.initDictData()

      // 获取任务详情
      getJob(job.id)
        .then(res => {
          this.jobDetail = res.data.data || {}
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || '获取任务详情失败')
          this.jobDetail = job // 使用传入的数据作为备选
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * 获取任务组标签文本
     */
    getJobGroupLabel(group) {
      const item = this.jobGroupOptions.find(item => item.value === group)
      return item ? item.label : group
    },

    /**
     * 获取状态标签类型
     */
    getStatusTagType(status) {
      const tagMap = {
        'run': 'success',
        'stop': 'danger'
      }
      return tagMap[status] || ''
    },

    /**
     * 获取状态标签文本
     */
    getStatusLabel(status) {
      const item = this.statusOptions.find(item => item.value === status)
      return item ? item.label : status
    },

    /**
     * 获取错失执行策略标签文本
     */
    getMisfirePolicyLabel(policy) {
      const item = MISFIRE_POLICY.find(item => item.value === policy)
      return item ? item.label : policy
    },

    /**
     * 获取并发执行标签文本
     */
    getConcurrentLabel(concurrent) {
      return concurrent === true ? '允许' : '禁止'
    }
  }
}
</script>

<style scoped>
.job-detail {
  padding: 0 10px;
}

.code-block {
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  word-break: break-all;
  white-space: pre-wrap;
}

.dialog-footer {
  text-align: right;
}
</style>
