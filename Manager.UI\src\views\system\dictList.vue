<template>
	<div class="dict-list-container">
		<div class="dict-list-content">
			<dict-edit @search="search"></dict-edit>
			<div class="card card--search search-flex">
				<div class="search-container">
					<div class="search-form">
						<el-input
							v-model="searchModel.nameCn"
							placeholder="中文名称"
							clearable
							style="width: 180px;" />
						<el-input
							v-model="searchModel.nameEn"
							placeholder="英文名称"
							clearable
							style="width: 180px;" />
					</div>
					<div class="search-buttons">
						<el-button type="primary" @click="search"  class="search-btn">搜索</el-button>
						<el-button @click="resetSearch"  class="search-btn">重置</el-button>
					</div>
				</div>
				<div class="add-button-container">
					<el-button type="primary" @click="add(0)"  class="add-btn">添加根字典</el-button>
				</div>
			</div>
			<div class="card card--table">
				<div class="table-col">
					<el-table :data="dictList" row-key="id" style="width: 100%; height: 100%;" class="data-table" stripe>
						<el-table-column prop="id" align="center" width="80" label="ID" sortable />
						<el-table-column prop="nameCn" align="center" min-width="150" label="中文名称" sortable show-overflow-tooltip />
						<el-table-column prop="nameEn" align="center" min-width="150" label="英文名称" sortable show-overflow-tooltip />
						<el-table-column prop="cssClass" align="center" min-width="180" label="CSS类名/颜色">
							<template #default="scope">
								<div class="css-class-display">
									<el-tag
										v-if="scope.row.cssClass"
										:style="getCssStyle(scope.row.cssClass)"
										:type="getTagType(scope.row.cssClass)"
										size="small"
										class="css-preview">
										{{ scope.row.cssClass }}
									</el-tag>
									<span v-else class="no-css">-</span>
								</div>
							</template>
						</el-table-column>
						<el-table-column prop="parentId" align="center" width="100" label="父级ID" sortable />
						<el-table-column prop="sort" align="center" width="80" label="排序" sortable />
						<el-table-column prop="note" align="center" min-width="200" label="备注" show-overflow-tooltip>
							<template #default="scope">
								<span v-if="scope.row.note">{{ scope.row.note }}</span>
								<span v-else class="no-note">-</span>
							</template>
						</el-table-column>
						<el-table-column prop="createTime" align="center" width="160" label="创建时间" sortable />
						<el-table-column prop="updateTime" align="center" width="160" label="更新时间" sortable />
						<el-table-column align="center" width="220" label="操作" fixed="right">
							<template #default="scope">
								<el-button type="text"  @click="edit(scope.row.id)"  class="action-btn">
									编辑
								</el-button>
								<el-button type="text" @click="add(scope.row.id)" class="action-btn">
									添加子项
								</el-button>
								<el-button type="text"   @click="deleted(scope.row.id)"  class="action-btn">
									删除
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="pagination-col">
					<el-pagination
						background
						layout="total, sizes, prev, pager, next, jumper"
						:page-sizes="[10, 20, 50, 100]"
						:page-size="searchModel.pageSize"
						:current-page="searchModel.pageNum"
						:total="total"
						@current-change="currentChange"
						@size-change="handleSizeChange"
					></el-pagination>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { listDict,deleteDict,getDict } from "@/api/system/dict"
import mitt from "@/utils/mitt"
import dictEdit from "@/components/system/dictEdit.vue"
export default {
	components:{ dictEdit },
	data() {
		return {
			searchModel: {
				pageNum: 1,
				pageSize: 10, // 最低10条，确保充满高度
				nameCn: '',
				nameEn: ''
			},
			dictList: [],
			total: 0
		}
	},
	methods: {
		/**
		 * 搜索字典列表
		 */
		search() {
			// 清理空值参数
			const params = { ...this.searchModel };
			Object.keys(params).forEach(key => {
				if (params[key] === '' || params[key] === null || params[key] === undefined) {
					delete params[key];
				}
			});

			listDict(params)
				.then(res => {
					this.dictList = res.data.data.list || [];
					this.total = res.data.data.total || 0;
				})
				.catch(err => {
					this.$message.error(err.data?.errorMessage || '查询失败');
				});
		},

		/**
		 * 重置搜索条件
		 */
		resetSearch() {
			this.searchModel = {
				pageNum: 1,
				pageSize: 10,
				nameCn: '',
				nameEn: ''
			};
			this.search();
		},

		/**
		 * 获取CSS样式（用于表格中的预览）
		 */
		getCssStyle(cssClass) {
			if (!cssClass) return {};

			// 如果是十六进制颜色
			if (cssClass.startsWith('#')) {
				return {
					backgroundColor: cssClass,
					color: '#fff',
					border: 'none'
				};
			}

			// 如果是RGB颜色
			if (cssClass.startsWith('rgb')) {
				return {
					backgroundColor: cssClass,
					color: '#fff',
					border: 'none'
				};
			}

			// 如果是预定义的Element Plus类型，返回空对象让Element Plus处理
			const elementTypes = ['primary', 'success', 'info', 'warning', 'danger'];
			if (elementTypes.includes(cssClass)) {
				return {};
			}

			// 其他情况作为CSS类名处理
			return {
				backgroundColor: cssClass,
				color: '#fff',
				border: 'none'
			};
		},

		/**
		 * 获取标签类型（用于Element Plus的el-tag）
		 */
		getTagType(cssClass) {
			const elementTypes = ['primary', 'success', 'info', 'warning', 'danger'];
			return elementTypes.includes(cssClass) ? cssClass : '';
		},

		/**
		 * 获取对比色（用于文字颜色）
		 */
		getContrastColor(hexColor) {
			if (!hexColor || !hexColor.startsWith('#')) return '#000';

			// 移除#号
			const hex = hexColor.replace('#', '');

			// 处理3位和6位十六进制
			let r, g, b;
			if (hex.length === 3) {
				r = parseInt(hex[0] + hex[0], 16);
				g = parseInt(hex[1] + hex[1], 16);
				b = parseInt(hex[2] + hex[2], 16);
			} else if (hex.length === 6) {
				r = parseInt(hex.substr(0, 2), 16);
				g = parseInt(hex.substr(2, 2), 16);
				b = parseInt(hex.substr(4, 2), 16);
			} else {
				return '#000';
			}

			// 计算亮度
			const brightness = (r * 299 + g * 587 + b * 114) / 1000;

			// 根据亮度返回黑色或白色
			return brightness > 128 ? '#000' : '#fff';
		},
		edit(id){
			getDict(id)
			.then(res => {
				mitt.emit('openDictEdit',res.data.data)
			})
			.catch(err => {
				this.$message.error(err.data.errorMessage)
			})
		},
		add(id){
			mitt.emit('openDictAdd',id)
		},
		/**
		 * 删除字典
		 */
		deleted(id) {
			this.$confirm('确定要删除这个字典项吗？删除后不可恢复！', '删除确认', {
				confirmButtonText: '确定删除',
				cancelButtonText: '取消',
				type: 'warning',
				dangerouslyUseHTMLString: false
			}).then(() => {
				deleteDict(id)
					.then(() => {
						this.search();
						this.$message.success("删除成功");
					})
					.catch(err => {
						this.$message.error(err.data?.errorMessage || '删除失败');
					});
			}).catch(() => {
				// 用户取消删除，不需要处理
			});
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(size) {
			this.searchModel.pageSize = size
			this.searchModel.pageNum = 1
			this.search()
		},
		async init(){
			mitt.off('openDictEdit')
			mitt.off('openDictAdd')
			try{
				const [dict_res] = await Promise.all([
					listDict(this.searchModel)
				])
				this.dictList = dict_res.data.data.list
				this.total = dict_res.data.data.total
			}catch(err){
				this.$message.error(err.data.errorMessage)
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
.dict-list-container {
	display: flex;
	flex-direction: column;
	height: 100%;
	box-sizing: border-box;
}

.dict-list-content {
	display: flex;
	flex-direction: column;
	height: 100%;
}

/* 搜索区域样式 */
.search-flex {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px;
	gap: 16px;
}

.search-container {
	display: flex;
	align-items: center;
	gap: 16px;
}

.search-form {
	display: flex;
	align-items: center;
	gap: 12px;
}

.search-buttons {
	display: flex;
	align-items: center;
	gap: 8px;
}

.add-button-container {
	margin-left: auto;
}

.search-btn,
.add-btn {
	padding: 8px 16px;
	text-align: center;
	min-width: 80px;
	display: flex; justify-content: center; align-items: center;
}

.action-btn {
	text-align: center;
}

/* 表格区域样式 */
.table-col {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
}

.data-table {
	flex: 1;
	display: flex;
	flex-direction: column;
	height: 100% !important;
}

.pagination-col {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
}

/* CSS类名/颜色显示样式 */
.css-class-display {
	display: flex;
	justify-content: center;
	align-items: center;
}

.css-preview {
	min-width: 60px;
	text-align: center;
	font-size: 12px;
	max-width: 150px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.no-css,
.no-note {
	color: #ccc;
	font-style: italic;
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.search-form {
		flex-direction: column;
		align-items: stretch;
	}

	.search-form .el-input,
	.search-form .el-input-number {
		width: 100% !important;
		margin-right: 0 !important;
		margin-bottom: 8px;
	}
}

@media (max-width: 768px) {
	.search-buttons {
		flex-direction: column;
		align-items: stretch;
	}

	.search-buttons .el-button {
		width: 100%;
	}

	.css-class-display {
		flex-direction: row;
		justify-content: center;
	}

	.css-text {
		max-width: 80px;
	}
}

/* 暗色主题样式 */
.dark-theme .card {
  background-color: var(--card-background);
  border: 1px solid #444;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.dark-theme .css-text {
	color: #ccc;
}

.dark-theme .no-css,
.dark-theme .no-note {
	color: #666;
}

/* 表格样式优化 */
.data-table :deep(.el-table__header-wrapper) {
	background-color: #f5f7fa;
}

.dark-theme .data-table :deep(.el-table__header-wrapper) {
	background-color: #2d3748;
}

.data-table :deep(.el-table__row:hover) {
	background-color: #f0f9ff;
}

.dark-theme .data-table :deep(.el-table__row:hover) {
	background-color: #374151;
}

/* 操作按钮样式 */
.data-table :deep(.el-button + .el-button) {
	margin-left: 8px;
}

/* 标签预览样式 */
.css-preview.el-tag {
	border-radius: 4px;
	font-weight: 500;
}


.card--table {
	background-color: #fff;
	border-radius: 5px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	flex: 1;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: auto;
	margin-top: 0;
}

.table-col {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
}

.data-table {
	flex: 1;
	display: flex;
	flex-direction: column;
	height: 100% !important;
}

.pagination-col {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
}

.pagination-container {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
	padding: 0 20px 20px;
}

.search-flex {
	display: flex;
	align-items: center;
}

.card--search {
	margin-bottom: 20px;
	flex: none;
	height: auto;
	padding: 20px 20px;
	display: flex;
	align-items: center;
}

</style>