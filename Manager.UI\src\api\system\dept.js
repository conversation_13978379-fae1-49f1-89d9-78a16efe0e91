import request from '@/utils/request'

// 分页查询部门列表
export const listDept = (data) =>
	request({
		url: '/manage-api/v1/dept/page',
		method: 'get',
		params: data
	})

// 获取部门详情
export const getDept = (id) =>
	request({
		url: '/manage-api/v1/dept',
		method: 'get',
		params: { id }
	})

// 添加部门
export const addDept = (data) =>
	request({
		url: '/manage-api/v1/dept',
		method: 'post',
		data
	})

// 编辑部门
export const editDept = (data) =>
	request({
		url: '/manage-api/v1/dept',
		method: 'put',
		data
	})

// 删除部门
export const deleteDept = (id) =>
	request({
		url: '/manage-api/v1/dept',
		method: 'delete',
		params: { id }
	})

// 获取部门树形结构（用于下拉选择）
export const getDeptTree = () =>
	request({
		url: '/manage-api/v1/dept/page',
		method: 'get',
		params: { pageNum: 1, pageSize: 500 }
	}) 