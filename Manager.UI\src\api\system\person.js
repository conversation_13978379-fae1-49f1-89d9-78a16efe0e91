
import request from '@/utils/request'

// 分页查询员工
export function listPerson(params) {
  return request({
    url: '/manage-api/v1/person/page',
    method: 'get',
    params
  })
}

// 通过ID查询员工
export function getPerson(id) {
  return request({
    url: '/manage-api/v1/person',
    method: 'get',
    params: { id }
  })
}

// 新增员工
export function addPerson(data) {
  return request({
    url: '/manage-api/v1/person',
    method: 'post',
    data
  })
}

// 编辑员工
export function editPerson(data) {
  return request({
    url: '/manage-api/v1/person',
    method: 'put',
    data
  })
}

// 删除员工
export function deletePerson(id) {
  return request({
    url: '/manage-api/v1/person',
    method: 'delete',
    params: { id }
  })
}