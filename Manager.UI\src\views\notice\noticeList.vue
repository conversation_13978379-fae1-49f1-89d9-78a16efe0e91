<template>
  <div class="menu-list-container">
    <div class="menu-list-content">
      <notice-edit @search="search" ref="editDialog" />
      <div class="card card--search search-flex">
        <div class="search-container">
          <div class="search-form">
            <el-input
              v-model="searchModel.title"
              placeholder="标题"
              clearable
              style="width: 180px;" />
            <el-select
              v-model="searchModel.type"
              placeholder="通知类型"
              clearable
              style="width: 150px;">
              <el-option
                v-for="item in noticeTypeList"
                :key="item.nameEn"
                :label="item.nameCn"
                :value="item.nameEn" />
            </el-select>
            <el-select
              v-model="searchModel.targetType"
              placeholder="目标类型"
              clearable
              style="width: 150px;">
              <el-option
                v-for="item in noticeTargetList"
                :key="item.nameEn"
                :label="item.nameCn"
                :value="item.nameEn" />
            </el-select>
            <el-select
              v-model="searchModel.top"
              placeholder="是否置顶"
              clearable
              style="width: 120px;">
              <el-option label="置顶" :value="true" />
              <el-option label="不置顶" :value="false" />
            </el-select>
          </div>
          <div class="search-buttons">
            <el-button type="primary" @click="search" class="search-btn">搜索</el-button>
            <el-button @click="resetSearch" class="search-btn">重置</el-button>
          </div>
        </div>
        <div class="add-button-container">
          <el-button type="primary" @click="add" class="add-btn">添加通知</el-button>
        </div>
      </div>
      <div class="card card--table">
        <div class="table-col">
          <el-table :data="noticeList" row-key="id" style="width: 100%; height: 100%;" class="data-table" stripe>
            <el-table-column prop="id" label="ID" width="80" align="center" sortable />
            <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip />
            <el-table-column prop="type" label="通知类型" width="120" align="center">
              <template #default="scope">
                <el-tag v-if="scope.row.type" size="small">
                  {{ formatDictValue(scope.row.type, noticeTypeList) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="targetType" label="目标类型" width="120" align="center">
              <template #default="scope">
                <el-tag v-if="scope.row.targetType" type="info" size="small">
                  {{ formatDictValue(scope.row.targetType, noticeTargetList) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="imageUrl" label="图片" width="100" align="center">
              <template #default="scope">
                <el-image  :preview-teleported="true" v-if="scope.row.imageUrl" :src="imgServer + scope.row.imageUrl"
                 style="width: 60px; height: 40px;" fit="cover" :preview-src-list="[imgServer + scope.row.imageUrl]"/>
                <span v-else class="text-muted">无图片</span>
              </template>
            </el-table-column>
            <el-table-column prop="top" label="置顶" width="80" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.top ? 'danger' : 'info'" size="small">
                  {{ scope.row.top ? '置顶' : '普通' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="sort" label="排序" width="80" align="center" sortable />
            <el-table-column prop="createTime" label="创建时间" width="160" align="center" sortable />
            <el-table-column label="操作" width="180" align="center">
              <template #default="scope">
                <el-button type="text" size="small" @click="edit(scope.row.id)">编辑</el-button>
                <el-button type="text" size="small" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination background layout="prev, pager, next" @current-change="currentChange" :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listNotice, deleteNotice, getNotice } from '@/api/notice'
import { listDictByNameEn } from '@/api/system/dict'
import { getSelectedCommunityId } from '@/store/modules/options'
import mitt from '@/utils/mitt'
import noticeEdit from '@/components/notice/noticeEdit.vue'

export default {
  components: { noticeEdit },
  data() {
    return {
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        title: '',
        type: '',
        targetType: '',
        top: null
      },
      noticeList: [],
      noticeTypeList: [],
      noticeTargetList: [],
      total: 0
    }
  },
  methods: {
    /**
     * 搜索通知列表
     */
    search() {
      // 获取当前选中的小区ID
      const communityId = getSelectedCommunityId();
      if (!communityId) {
        this.$message.warning('请先选择小区');
        return;
      }

      // 清理空值参数
      const params = { ...this.searchModel, communityId };
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      listNotice(params)
        .then(res => {
          this.noticeList = res.data.data.list || [];
          this.total = res.data.data.total || 0;
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || '查询失败');
        });
    },

    /**
     * 重置搜索条件
     */
    resetSearch() {
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        title: '',
        type: '',
        targetType: '',
        top: null
      };
      this.search();
    },

    /**
     * 添加通知
     */
    add() {
      mitt.emit('openNoticeAdd')
    },

    /**
     * 编辑通知
     */
    edit(id) {
      getNotice(id)
        .then(res => {
          mitt.emit('openNoticeEdit', res.data.data)
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || '获取通知详情失败');
        });
    },

    /**
     * 删除通知
     */
    deleted(id) {
      this.$confirm('删除通知, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteNotice(id)
          .then(() => {
            this.search()
            this.$message.success('删除成功')
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '删除失败');
          });
      }).catch(() => {})
    },

    /**
     * 分页变化
     */
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    /**
     * 格式化字典值
     */
    formatDictValue(value, dictList) {
      const item = dictList.find(dict => dict.nameEn === value);
      return item ? item.nameCn : value;
    },

    /**
     * 小区变化监听方法
     */
    onCommunityChange(community) {
      console.log('小区已切换到:', community?.communityName || '无');

      // 清空当前列表
      this.noticeList = [];
      this.total = 0;

      // 重置搜索条件
      this.searchModel.pageNum = 1;

      // 如果有选中的小区，重新加载数据
      if (community) {
        this.search();
      }
    },

    /**
     * 初始化数据
     */
    async init() {
      try {
        const [noticeType_res, noticeTarget_res] = await Promise.all([
          listDictByNameEn('notice_type'),
          listDictByNameEn('notice_target')
        ]);

        this.noticeTypeList = noticeType_res.data.data || [];
        this.noticeTargetList = noticeTarget_res.data.data || [];

        // 加载通知列表
        this.search();
      } catch (err) {
        this.$message.error(err.data?.errorMessage || '初始化失败');
      }
    },

    /**
     * 处理小区变化事件
     */
    handleCommunityChange(event) {
      // 小区变化时自动刷新通知列表
      const community = event.detail || null;
      this.onCommunityChange(community);
    }
  },

  created() {
    this.init()

    // 监听小区变化事件
    window.addEventListener('communityChanged', this.handleCommunityChange)
  },

  beforeDestroy() {
    // 移除事件监听器
    window.removeEventListener('communityChanged', this.handleCommunityChange)
  }
}
</script>

<style scoped>

::v-deep .--el-table-index {
  z-index: 9999 !important;
}

.menu-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.menu-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 搜索区域样式 */
.search-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  gap: 16px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-btn {
  min-width: 80px;
}

.add-button-container {
  display: flex;
  align-items: center;
}

.add-btn {
  min-width: 100px;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.text-muted {
  color: #999;
  font-size: 12px;
}

.dark-theme .card {
  background-color: var(--card-background);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-form {
    flex-wrap: wrap;
  }

  .search-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .search-flex {
    flex-direction: column;
    align-items: stretch;
  }

  .add-button-container {
    justify-content: flex-end;
  }
}
</style>