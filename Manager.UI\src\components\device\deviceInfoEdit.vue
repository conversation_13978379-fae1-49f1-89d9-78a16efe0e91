<template>
  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    width="900px"
    class="device-info-edit-dialog"
  >
    <el-form
      :model="deviceInfoModel"
      :rules="rules"
      ref="formRef"
      label-width="100px"
      class="device-info-edit-form"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input
              v-model="deviceInfoModel.deviceName"
              placeholder="请输入设备名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备编号" prop="deviceNo">
            <el-input
              v-model="deviceInfoModel.deviceNo"
              placeholder="请输入设备编号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="小区ID" prop="communityId">
            <el-input-number
              v-model="deviceInfoModel.communityId"
              :min="0"
              style="width: 100%"
              placeholder="小区ID"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备类型" prop="typeId">
            <el-select v-model="deviceInfoModel.typeId" placeholder="请选择设备类型" style="width: 100%;">
              <el-option 
                v-for="item in deviceTypeList" 
                :key="item.id" 
                :label="item.typeName" 
                :value="item.id" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备型号" prop="model">
            <el-input
              v-model="deviceInfoModel.model"
              placeholder="请输入设备型号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备IP" prop="ip">
            <el-input
              v-model="deviceInfoModel.ip"
              placeholder="请输入设备IP"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备Mac" prop="mac">
            <el-input
              v-model="deviceInfoModel.mac"
              placeholder="请输入设备Mac"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="安装类型" prop="installType">
            <el-select v-model="deviceInfoModel.installType" placeholder="请选择安装类型" style="width: 100%;">
              <el-option 
                v-for="item in installTypeList" 
                :key="item.nameEn" 
                :label="item.nameCn" 
                :value="item.nameEn" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="经度" prop="lng">
            <el-input-number
              v-model="deviceInfoModel.lng"
              :precision="6"
              style="width: 100%"
              placeholder="经度"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="纬度" prop="lat">
            <el-input-number
              v-model="deviceInfoModel.lat"
              :precision="6"
              style="width: 100%"
              placeholder="纬度"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="海拔" prop="alt">
            <el-input-number
              v-model="deviceInfoModel.alt"
              :precision="2"
              style="width: 100%"
              placeholder="海拔"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <el-input
              v-model="deviceInfoModel.address"
              placeholder="请输入地址"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="deviceInfoModel.sort"
              :min="0"
              style="width: 100%"
              placeholder="排序"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-input-number
              v-model="deviceInfoModel.status"
              :min="0"
              style="width: 100%"
              placeholder="状态"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="note">
            <el-input
              v-model="deviceInfoModel.note"
              type="textarea"
              :rows="3"
              placeholder="请输入描述"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地理围栏" prop="polyCoords">
            <el-input
              v-model="deviceInfoModel.polyCoords"
              placeholder="请输入地理围栏坐标"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="扩展参数" prop="expandData">
            <el-input
              v-model="deviceInfoModel.expandData"
              type="textarea"
              :rows="2"
              placeholder="请输入扩展参数"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第三方openid" prop="openid">
            <el-input
              v-model="deviceInfoModel.openid"
              placeholder="请输入第三方平台openid"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { addDevice, editDevice, getDevice, listDeviceType } from '@/api/deviceApi'
import { listDictByNameEn } from '@/api/system/dict'
import mitt from '@/utils/mitt'

export default {
  data() {
    return {
      dialog: {
        show: false,
        title: '',
        type: ''
      },
      deviceInfoModel: {
        id: null,
        deviceName: '',
        deviceNo: '',
        communityId: null,
        typeId: null,
        model: '',
        ip: '',
        mac: '',
        lng: null,
        lat: null,
        alt: null,
        address: '',
        installType: '',
        note: '',
        sort: 0,
        status: 1,
        polyCoords: '',
        expandData: '',
        openid: ''
      },
      deviceTypeList: [],
      installTypeList: [],
      rules: {
        deviceName: [
          { required: true, message: '请输入设备名称', trigger: 'blur' }
        ],
        deviceNo: [
          { required: true, message: '请输入设备编号', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async loadData() {
      try {
        const [deviceTypeRes, installTypeRes] = await Promise.all([
          listDeviceType({ pageNum: 1, pageSize: 1000 }),
          listDictByNameEn('install_type')
        ])
        
        this.deviceTypeList = deviceTypeRes.data.data.list || []
        this.installTypeList = installTypeRes.data.data || []
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      }
    },
    submit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.dialog.type === 'add') {
            addDevice(this.deviceInfoModel).then(() => {
              this.$message.success('操作成功')
              this.dialog.show = false
              this.$emit('search')
            })
          } else {
            editDevice(this.deviceInfoModel).then(() => {
              this.$message.success('操作成功')
              this.dialog.show = false
              this.$emit('search')
            })
          }
        }
      })
    },
    resetForm() {
      this.deviceInfoModel = {
        id: null,
        deviceName: '',
        deviceNo: '',
        communityId: null,
        typeId: null,
        model: '',
        ip: '',
        mac: '',
        lng: null,
        lat: null,
        alt: null,
        address: '',
        installType: '',
        note: '',
        sort: 0,
        status: 1,
        polyCoords: '',
        expandData: '',
        openid: ''
      }
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
    }
  },
  async mounted() {
    await this.loadData()
    
    mitt.on('openDeviceInfoEdit', (data) => {
      this.resetForm()
      this.dialog.type = data.type
      
      if (data.type === 'add') {
        this.dialog.title = '添加设备信息'
      } else {
        this.dialog.title = '编辑设备信息'
        getDevice(data.id).then(res => {
          this.deviceInfoModel = res.data.data
        })
      }
      
      this.dialog.show = true
    })
  }
}
</script>

<style scoped>
.device-info-edit-dialog .el-form {
  padding: 20px;
}

.device-info-edit-form .el-form-item {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
