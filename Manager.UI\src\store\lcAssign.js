
// store.js
import { defineStore } from "pinia";

export const useStore = defineStore('piniaStore', {
  state: () => {
    return {
      smartPropertyUserInfo: null,
      smartPropertyToken: null,
      frontPermissions: null,
      treePermissions: null, // 构建好的菜单树
      dictList: null,
      // 新增的存储字段
      communityList: [],
      selectedCommunity: null,
      userName: null,
      theme: 'light'
    }
  },

  /* 
    添加getters，接收一个对象，该对象里面是各种方法，
    你可以把它想象成Vue中的计算属性，它的作用就是返回一个结果，
    getters也是会被缓存的，
    在getters中的，各个方法之间相互调用采用this关键字即可
  */
  getters: {
    // changeName: (state) => (state.userInfo.changeName = state.userInfo.nickName + "逆天而行"),

  },

  /*
    添加actions，属性值是一个对象，该对象里面是各种方法，包括同步、异步方法
    特殊之处：actions中的方法内部的this指向的是当前store，即：
    在`defineStore`中，state对象中的属性，会被绑定到this上，可以通过this.name来访问和修改name属性，
    这里state对象中定义了name、age、sex属性，因此可以通过this.name、this.age、this.sex 来访问和修改这些属性
  */
  actions: {


    removeAll() {
      this.smartPropertyUserInfo = null
      this.smartPropertyToken = null
      this.frontPermissions = null
      this.treePermissions = null
      this.dictList = null
      this.communityList = []
      this.selectedCommunity = null
      this.userName = null
      this.theme = 'light'
    },

    // 清除用户信息
    clearUserInfo() {
      this.smartPropertyUserInfo = null
      this.userName = null
    },

    // 可以包含异步操作
    saveUserInfo(base64, info) {
       
      if (info) {
        var base64UserInfo1 = base64.encode(JSON.stringify(info))
        var base64UserInfo2 = base64.encode(base64UserInfo1 + 'propertyproaW4S2r6E1DQK5fPWzBA5')

        this.smartPropertyUserInfo = base64UserInfo2
      } else {
        this.smartPropertyUserInfo = info
      }

    },

    getUserInfo(base64) {

      if (this.smartPropertyUserInfo == null)
        return null

      var u2 = base64.decode(this.smartPropertyUserInfo)
      var u1str = base64.decode(u2.split('propertypro')[0])

      return JSON.parse(u1str);
    },

    setSmartPropertyUserInfo(smartPropertyUserInfo) {
      this.smartPropertyUserInfo = smartPropertyUserInfo
    },

    setSmartPropertyToken(smartPropertyToken) {
      this.smartPropertyToken = smartPropertyToken
    },

    setFrontPermissions(frontPermissions) {
      this.frontPermissions = frontPermissions
    },

    setTreePermissions(treePermissions) {
      this.treePermissions = treePermissions
    },

    setDictList(dictList) {
      this.dictList = null
      this.dictList = dictList
    },

    // 新增的设置方法
    setCommunityList(communityList) {
      this.communityList = communityList || []
    },

    setSelectedCommunity(selectedCommunity) {
      this.selectedCommunity = selectedCommunity
    },

    setUserName(userName) {
      this.userName = userName
    },

    setTheme(theme) {
      this.theme = theme
    },

    // 通用的 set 方法
    set(key, value) {
      if (this.hasOwnProperty(key)) {
        this[key] = value
      }
    },

    // 通用的 get 方法
    get(key) {
      return this[key]
    },

    // 检查是否存在某个值
    has(key) {
      return this[key] !== null && this[key] !== undefined
    }

  },


  persist: {
    // 持久化存储
    paths: [
      'smartPropertyUserInfo',
      'smartPropertyToken',
      'frontPermissions',
      'treePermissions',
      'dictList',
      'communityList',
      'selectedCommunity',
      'userName',
      'theme'
    ]
  },

})


