/**
 * Token 工具函数
 * 统一处理 token 过期时间计算和相关逻辑
 */

/**
 * 计算 token 过期时间
 * @param {number} expiresIn - 服务器返回的过期时间（秒）
 * @param {number} safetyMarginMinutes - 安全边距（分钟），默认5分钟
 * @param {number} minExpirationMinutes - 最小过期时间（分钟），默认1分钟
 * @returns {number} - 过期时间戳（毫秒）
 */
export function calculateTokenExpireTime(expiresIn, safetyMarginMinutes = 5, minExpirationMinutes = 1) {
    if (!expiresIn || typeof expiresIn !== 'number' || expiresIn <= 0) {
        console.warn('无效的 expires_in 值:', expiresIn, '使用默认30分钟');
        return new Date().getTime() + (30 * 60 * 1000); // 默认30分钟
    }
    
    const safetyMarginSeconds = safetyMarginMinutes * 60;
    const minExpirationSeconds = minExpirationMinutes * 60;
    
    // 计算实际过期时间（减去安全边距）
    const actualExpireSeconds = Math.max(expiresIn - safetyMarginSeconds, minExpirationSeconds);
    const expireTime = new Date().getTime() + (actualExpireSeconds * 1000);
    
    console.log(`Token过期时间计算: 服务器${expiresIn}秒, 安全边距${safetyMarginMinutes}分钟, 实际${actualExpireSeconds}秒`);
    
    return expireTime;
}

/**
 * 处理登录或刷新响应中的 token 数据
 * @param {Object} tokenData - 服务器返回的 token 数据
 * @param {string} source - 来源标识（'login' 或 'refresh'）
 * @returns {Object} - 处理后的 token 数据
 */
export function processTokenData(tokenData, source = 'unknown') {
    if (!tokenData || typeof tokenData !== 'object') {
        throw new Error('无效的 token 数据');
    }
    
    // 创建副本避免修改原对象
    const processedData = { ...tokenData };
    
    // 设置过期时间
    if (tokenData.expires_in) {
        processedData.expires_time = calculateTokenExpireTime(tokenData.expires_in);
        console.log(`${source} - Token数据处理完成:`, {
            expires_in: tokenData.expires_in,
            expires_time: processedData.expires_time,
            expires_date: new Date(processedData.expires_time).toLocaleString()
        });
    } else {
        // 如果没有 expires_in，设置默认过期时间
        processedData.expires_time = calculateTokenExpireTime(null);
        console.warn(`${source} - 未找到 expires_in，使用默认过期时间`);
    }
    
    return processedData;
}

/**
 * 检查 token 是否即将过期
 * @param {Object|string} token - token 对象或 JSON 字符串
 * @param {number} warningMinutes - 提前多少分钟警告，默认10分钟
 * @returns {Object} - 检查结果
 */
export function checkTokenExpiration(token, warningMinutes = 10) {
    try {
        let tokenObj;
        if (typeof token === 'string') {
            tokenObj = JSON.parse(token);
        } else if (typeof token === 'object') {
            tokenObj = token;
        } else {
            return { valid: false, reason: 'invalid_token_format' };
        }
        
        if (!tokenObj || !tokenObj.access_token) {
            return { valid: false, reason: 'missing_access_token' };
        }
        
        if (!tokenObj.expires_time) {
            return { valid: false, reason: 'missing_expire_time' };
        }
        
        const currentTime = new Date().getTime();
        const expireTime = tokenObj.expires_time;
        const warningTime = expireTime - (warningMinutes * 60 * 1000);
        
        if (currentTime >= expireTime) {
            return { 
                valid: false, 
                reason: 'expired',
                expiredMinutes: Math.floor((currentTime - expireTime) / (60 * 1000))
            };
        }
        
        if (currentTime >= warningTime) {
            return { 
                valid: true, 
                warning: true,
                reason: 'expiring_soon',
                remainingMinutes: Math.floor((expireTime - currentTime) / (60 * 1000))
            };
        }
        
        return { 
            valid: true, 
            warning: false,
            remainingMinutes: Math.floor((expireTime - currentTime) / (60 * 1000))
        };
        
    } catch (error) {
        console.error('检查 token 过期时出错:', error);
        return { valid: false, reason: 'parse_error', error: error.message };
    }
}

/**
 * 格式化 token 信息用于调试
 * @param {Object|string} token - token 对象或 JSON 字符串
 * @returns {Object} - 格式化的信息
 */
export function formatTokenInfo(token) {
    try {
        let tokenObj;
        if (typeof token === 'string') {
            tokenObj = JSON.parse(token);
        } else {
            tokenObj = token;
        }
        
        if (!tokenObj) {
            return { error: 'Token 为空' };
        }
        
        const info = {
            hasAccessToken: !!tokenObj.access_token,
            hasRefreshToken: !!tokenObj.refresh_token,
            hasExpiresIn: !!tokenObj.expires_in,
            hasExpiresTime: !!tokenObj.expires_time
        };
        
        if (tokenObj.expires_in) {
            info.expiresInSeconds = tokenObj.expires_in;
            info.expiresInMinutes = Math.floor(tokenObj.expires_in / 60);
        }
        
        if (tokenObj.expires_time) {
            info.expiresTime = tokenObj.expires_time;
            info.expiresDate = new Date(tokenObj.expires_time).toLocaleString();
            
            const currentTime = new Date().getTime();
            const remainingMs = tokenObj.expires_time - currentTime;
            info.remainingMinutes = Math.floor(remainingMs / (60 * 1000));
            info.isExpired = remainingMs <= 0;
        }
        
        return info;
    } catch (error) {
        return { error: '解析 token 失败: ' + error.message };
    }
}

/**
 * 验证 token 数据的完整性
 * @param {Object} tokenData - token 数据
 * @returns {Object} - 验证结果
 */
export function validateTokenData(tokenData) {
    const errors = [];
    const warnings = [];
    
    if (!tokenData) {
        errors.push('Token 数据为空');
        return { valid: false, errors, warnings };
    }
    
    if (!tokenData.access_token) {
        errors.push('缺少 access_token');
    }
    
    if (!tokenData.refresh_token) {
        warnings.push('缺少 refresh_token，无法自动刷新');
    }
    
    if (!tokenData.expires_in && !tokenData.expires_time) {
        warnings.push('缺少过期时间信息');
    }
    
    if (tokenData.expires_in && typeof tokenData.expires_in !== 'number') {
        warnings.push('expires_in 不是数字类型');
    }
    
    if (tokenData.expires_time && typeof tokenData.expires_time !== 'number') {
        warnings.push('expires_time 不是数字类型');
    }
    
    // 检查过期时间是否合理
    if (tokenData.expires_in) {
        if (tokenData.expires_in < 60) {
            warnings.push('过期时间过短（少于1分钟）');
        } else if (tokenData.expires_in > 24 * 60 * 60) {
            warnings.push('过期时间过长（超过24小时）');
        }
    }
    
    return {
        valid: errors.length === 0,
        errors,
        warnings
    };
}

/**
 * 创建调试用的 token 信息摘要
 * @param {Object|string} token - token 对象或 JSON 字符串
 * @returns {string} - 格式化的摘要字符串
 */
export function createTokenSummary(token) {
    const info = formatTokenInfo(token);
    
    if (info.error) {
        return `Token错误: ${info.error}`;
    }
    
    const parts = [];
    
    if (info.hasAccessToken) {
        parts.push('✅ AccessToken');
    } else {
        parts.push('❌ AccessToken');
    }
    
    if (info.hasRefreshToken) {
        parts.push('✅ RefreshToken');
    } else {
        parts.push('❌ RefreshToken');
    }
    
    if (info.expiresDate) {
        const status = info.isExpired ? '❌ 已过期' : '✅ 有效';
        parts.push(`${status} (${info.expiresDate})`);
        
        if (!info.isExpired && info.remainingMinutes !== undefined) {
            parts.push(`剩余${info.remainingMinutes}分钟`);
        }
    } else {
        parts.push('⚠️ 无过期时间');
    }
    
    return parts.join(' | ');
}

// 导出常量
export const TOKEN_CONSTANTS = {
    DEFAULT_SAFETY_MARGIN_MINUTES: 5,
    DEFAULT_MIN_EXPIRATION_MINUTES: 1,
    DEFAULT_EXPIRATION_MINUTES: 30,
    WARNING_THRESHOLD_MINUTES: 10
};

// 在控制台中提供调试工具
if (typeof window !== 'undefined') {
    window.tokenUtils = {
        calculateTokenExpireTime,
        processTokenData,
        checkTokenExpiration,
        formatTokenInfo,
        validateTokenData,
        createTokenSummary,
        TOKEN_CONSTANTS
    };
    
    // 提供快捷调试方法
    window.debugToken = function() {
        const token = window.$local?.get('smartPropertyToken');
        if (token) {
            console.group('🔍 当前 Token 调试信息');
            console.log('Token 摘要:', createTokenSummary(token));
            console.log('详细信息:', formatTokenInfo(token));
            console.log('过期检查:', checkTokenExpiration(token));
            console.log('数据验证:', validateTokenData(typeof token === 'string' ? JSON.parse(token) : token));
            console.groupEnd();
        } else {
            console.log('❌ 未找到 Token');
        }
    };
    
    console.log('🔧 Token 工具已加载，使用 window.debugToken() 调试当前 token');
}
