import router from '@/router/index'

import {
	currentUser
} from "@/api/system/user"
import {
	loadMenu
} from '@/api/system/menu'
import { tokenMonitor, eventTypes } from '@/utils/tokenMonitor.js'


const modules = import.meta.glob("../../views/**/*.vue")
// 只在需要时显示可用模块
if (window.location.search.includes('debug=true')) {
	console.log('可用的组件模块:', Object.keys(modules))
}

const data = {
	userinfo: {},
	permissions: [],
	treePermissions: [], // 树形权限结构
	logined: false,
	// 小区相关状态
	selectedCommunity: null, // 当前选中的小区
	communityList: [] // 小区列表
}

const reload = async () => {
	return await onLoad(true)
}




const getChildren = (route, routerList) => {
	let list = []
	if (routerList == null || routerList.length == 0) {
		return list
	}
	for (let item of routerList) {
		try {
			// 比较 ID 时考虑字符串和数字类型
			if (String(route.id) === String(item.parentId)) {
				// 验证必要字段
				if (!item.id || !item.menuName) {
					console.warn('⚠️ 跳过无效的子菜单项:', item)
					continue
				}

				list.push({
					id: item.id,
					path: item.path,
					menuName: item.menuName,
					sort: item.sort || 0,
					children: getChildren(item, routerList),
					icon: item.icon,
					type: item.type,
					menuType: item.menuType,
					parentId: item.parentId,
					componentPath: item.componentPath
				})
			}
		} catch (error) {
			console.warn('⚠️ 跳过子菜单构建异常:', item?.menuName, '错误:', error.message)
		}
	}
	// 按 sort 字段排序
	return list.sort((a, b) => (a.sort || 0) - (b.sort || 0))
}


// 加载用户信息和菜单（不处理token，由request.js统一处理）
async function onLoad(toPath) {

	console.log('🔄 onLoad 开始执行，toPath:', toPath, '时间戳:', new Date().toISOString())
	
	try {
		console.log('🌐 开始获取用户信息和权限...')
		const [user_res, permission_res] = await Promise.all([
			currentUser(),
			loadMenu()
		])
		console.log('✅ 用户信息和权限获取成功:', {
			user: user_res.data.data,
			permissions: permission_res.data.data,
			permissionCount: permission_res.data.data?.length || 0,
			timestamp: new Date().toISOString()
		})
		data.logined = true
		data.userinfo = user_res.data.data
		data.permissions = permission_res.data.data
		 
		// 将用户信息和权限存储到 Pinia
		window.$local?.saveUserInfo(window.$Base64, user_res.data.data)
		window.$local?.setFrontPermissions(permission_res.data.data)

		// 筛选菜单类型的权限（menuType: "menu"）
		const menuPermissions = data.permissions.filter(item => item.menuType === 'menu')
		console.log('筛选出的菜单权限数量:', menuPermissions.length)

			// 动态添加路由（只添加有组件路径的菜单，跳过错误的路由）
			const isDebug = window.location.search.includes('debug=true')
			let successCount = 0
			let errorCount = 0

			if (isDebug) {
				console.log('🔧 调试模式：所有菜单权限:', menuPermissions)
			}

			for (let item of menuPermissions) {
				try {
					if (!item.componentPath) {
						console.warn('⚠️ 跳过无组件路径的菜单:', item.menuName)
						continue
					}

					const componentPath = '../../views/' + item.componentPath + '.vue'
					const component = modules[componentPath]

					if (isDebug) {
						console.log('🔧 调试路由:', {
							menuName: item.menuName,
							path: '/' + item.path,
							componentPath: componentPath,
							componentFound: !!component
						})
					}

					if (component) {
						router.addRoute('index', {
							path: '/' + item.path,
							component: component
						})
						console.log('✅ 添加路由:', '/' + item.path, '菜单:', item.menuName)
						successCount++
					} else {
						console.warn('⚠️ 跳过组件未找到的路由:', '/' + item.path, '菜单:', item.menuName, '组件路径:', componentPath)
						errorCount++

						if (isDebug) {
							console.log('🔧 可能的组件路径:', Object.keys(modules).filter(key =>
								key.toLowerCase().includes(item.menuName.toLowerCase()) ||
								key.includes(item.path)
							))
						}
					}
				} catch (error) {
					console.warn('⚠️ 跳过路由添加异常:', '/' + item.path, '菜单:', item.menuName, '错误:', error.message)
					errorCount++
				}
			}

			console.log(`📊 路由添加完成: 成功 ${successCount} 个，跳过 ${errorCount} 个`)

			// 构建菜单树（按 sort 排序，跳过异常数据）
			let list = []
			let menuBuildErrors = 0

			for (let item of menuPermissions) {
				try {
					// 查找顶级菜单（parentId 为 "0" 或 0）
					if (item.parentId == "0" || item.parentId == 0 || item.parentId === null) {
						// 验证必要字段
						if (!item.id || !item.menuName) {
							console.warn('⚠️ 跳过无效的菜单项:', item)
							menuBuildErrors++
							continue
						}

						list.push({
							id: item.id,
							path: item.path,
							menuName: item.menuName,
							sort: item.sort || 0,
							children: getChildren(item, menuPermissions),
							icon: item.icon,
							type: item.type,
							menuType: item.menuType,
							componentPath: item.componentPath
						})
					}
				} catch (error) {
					console.warn('⚠️ 跳过菜单构建异常:', item.menuName, '错误:', error.message)
					menuBuildErrors++
				}
			}

			// 按 sort 字段排序
			data.treePermissions = list.sort((a, b) => (a.sort || 0) - (b.sort || 0))

			if (menuBuildErrors > 0) {
				console.warn(`⚠️ 菜单构建完成，跳过 ${menuBuildErrors} 个异常项`)
			}

			// 将构建好的菜单树也存储到 Pinia
			window.$local?.setTreePermissions(data.treePermissions)

			console.log('构建的菜单树:', data.treePermissions)
			
		// 移除自动跳转逻辑，让调用方控制跳转
		// if(toPath){
		// 	router.push("/index")
		// }

		console.log('onLoad 执行完成，logined:', data.logined)
	} catch(err) {
		console.error('获取用户信息失败:', err)
		data.logined = false
		// 抛出错误让调用方处理，request.js会处理401等token相关错误
		throw err
	}
}


// 简化的路由守卫（只关注路由问题，不涉及token处理）
router.beforeEach(async (to, from, next) => {
	console.log('🔍 路由守卫开始执行:', to.path)

	// 记录路由守卫事件
	tokenMonitor.logEvent(eventTypes.ROUTE_GUARD, {
		toPath: to.path,
		fromPath: from.path,
		isLogined: data.logined
	});

	// 如果访问登录页，直接允许
	if (to.path === "/login") {
		console.log('访问登录页，直接允许')
		return next()
	}

	// 确保 Pinia 数据已经恢复
	if (!window.$local) {
		console.log('Pinia 还未初始化，等待...')
		await new Promise(resolve => setTimeout(resolve, 100))
	}

	// 检查目标路由是否存在，如果不存在尝试恢复
	const targetRoute = router.getRoutes().find(route => route.path === to.path)
	if (!targetRoute) {
		console.warn('⚠️ 目标路由不存在:', to.path)

		// 尝试从 Pinia 恢复路由
		const permissions = window.$local?.get('frontPermissions')
		if (permissions && permissions.length > 0) {
			console.log('🔄 尝试恢复缺失的路由...')
			const menuPermissions = permissions.filter(item => item.menuType === 'menu')

			for (let item of menuPermissions) {
				if (item.componentPath && '/' + item.path === to.path) {
					try {
						const componentPath = '../../views/' + item.componentPath + '.vue'
						const component = modules[componentPath]
						if (component) {
							router.addRoute('index', {
								path: '/' + item.path,
								component: component
							})
							console.log('✅ 成功恢复路由:', '/' + item.path)
							// 路由已添加，重新导航
							return next(to.path)
						} else {
							console.warn('⚠️ 路由恢复失败，组件未找到:', componentPath, '菜单:', item.menuName)
						}
					} catch (error) {
						console.warn('⚠️ 路由恢复异常:', '/' + item.path, '错误:', error.message)
					}
					break // 找到对应的菜单项就退出，不管成功还是失败
				}
			}
		}

		// 如果路由恢复失败，友好地回退到首页
		console.warn('⚠️ 无法恢复路由，回退到首页:', to.path)
		return next('/home')
	}

	console.log('🚀 路由守卫通过，继续导航到:', to.path)
	return next()
})

// 小区相关方法
const setCommunityList = (list) => {
	data.communityList = list
	// 如果有小区数据且没有选中的小区，默认选择第一个
	if (list && list.length > 0 && !data.selectedCommunity) {
		data.selectedCommunity = list[0]
	}
}

const setSelectedCommunity = (community) => {
	data.selectedCommunity = community
}

const clearCommunityData = () => {
	data.selectedCommunity = null
	data.communityList = []
}

// 初始化时重新获取最新的用户信息和菜单（确保每次刷新都是最新数据）
setTimeout(async () => {
	const token = window.$local?.get('smartPropertyToken')

	console.log('🚀 应用启动检查:', { hasToken: !!token })

	// 如果有token，重新获取最新的用户信息和菜单
	if (token) {
		try {
			console.log('🔄 页面刷新，重新获取最新用户信息和菜单...')
			await onLoad(false) // 不跳转，只加载数据
			console.log('✅ 页面刷新时重新加载用户信息和菜单完成')
		} catch (error) {
			console.error('❌ 页面刷新时重新加载失败:', error)
			// 如果重新加载失败，可能是token过期，清除本地数据
			window.$local?.removeAll()
		}
	}
}, 50)

export default data

export {
	reload,
	setCommunityList,
	setSelectedCommunity,
	clearCommunityData
}