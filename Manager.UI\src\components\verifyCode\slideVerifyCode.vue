<template>
	<el-dialog class="verify-dialog" :width="isMobile ? '350px' : '500px'" draggable v-model="dialog.show" title="请先完成验证">
		<div v-if="!isMobile" style="width:470px;height:270px;user-select: none;position: relative">
			<el-image style="position: absolute;" :src="dialog.backgroundImage"></el-image>
			<el-image :style="'position: absolute;left: ' + (styleWidth) + 'px;'"
				:src="dialog.foregroundImage"></el-image>
		</div>
		<div v-else class="mobile-container">
			<el-image style="position: absolute; width: 100%; height: 100%;" :src="dialog.backgroundImage"></el-image>
			<el-image :style="'position: absolute;left: ' + (styleWidth) + 'px; height: 40px; top: 50px;'"
				:src="dialog.foregroundImage"></el-image>
		</div>

		<div v-if="!isMobile" class="auth-control">
			<div class="range-box">
				<div style="user-select: none;" class="range-text">拖动滑块完成拼图</div>
				<div class="range-slider" ref="range-slider-pc"
					:style="'position: absolute;left: ' + (styleWidth) + 'px;'">
					<div :class="['range-btn', { isDown: mouseDown }]" @mousedown="rangeMove" @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd">
						<div></div>
						<div></div>
						<div></div>
					</div>
				</div>
			</div>
		</div>

		<div v-else class="mobile-control">
			<div class="range-box">
				<div style="user-select: none;" class="range-text">拖动滑块完成拼图</div>
				<div class="range-slider" ref="range-slider-mobile"
					:style="'position: absolute;left: ' + (styleWidth) + 'px;'">
					<div :class="['range-btn', { isDown: mouseDown }]" @mousedown="rangeMove" @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd">
						<div></div>
						<div></div>
						<div></div>
					</div>
				</div>
			</div>
		</div>

	</el-dialog>
</template>

<script>
import mitt from "@/utils/mitt"

export default {
	data() {
		return {
			dialog: {
				show: false,
				backgroundImage: null,
				foregroundImage: null,
				value: 0
			},
			startWidth: 0,
			newX: 0,
			startX: 0,
			mouseDown: false
		};
	},
	/** 监听 **/
	watch: {
		show(newV) {
			// 每次出现都应该重新初始化
			if (newV) {
				document.body.classList.add("vue-puzzle-overflow");
				this.reset();
			} else {
				document.body.classList.remove("vue-puzzle-overflow");
			}
		}
	},

	/** 计算属性 **/
	computed: {
		// 是否为移动设备
		isMobile() {
			// 检测是否为真正的移动设备，而不仅仅是窗口大小
			const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
			// 只有在真正的移动设备上且窗口宽度小于768px时才返回true
			return isMobileDevice && window.innerWidth <= 768;
		},

		// 计算滑块位置
		styleWidth() {
			// 移动端使用固定宽度300px，PC端使用原始宽度470px
			const containerWidth = this.isMobile ? 300 : 470;
			// 滑块宽度，移动端40px，PC端64px
			const sliderWidth = this.isMobile ? 40 : 64;
			// 计算最大可滑动宽度
			const maxWidth = containerWidth - sliderWidth;

			const w = this.startWidth + this.newX - this.startX;
			return w < 0 ? 0 : w > maxWidth ? maxWidth : w > this.canvasWidth ? this.canvasWidth : w;
		}
	},
	/** 方法 **/
	methods: {
		// 处理窗口大小变化
		handleResize() {
			// 强制更新组件
			this.$forceUpdate()
		},

		// 获取正确的滑块引用
		getSliderRef() {
			// 确保引用存在
			if (this.isMobile) {
				return this.$refs["range-slider-mobile"] || { clientWidth: 0 };
			} else {
				return this.$refs["range-slider-pc"] || { clientWidth: 0 };
			}
		},

		// 鼠标移动事件
		rangeMove(e) {
			this.startWidth = this.getSliderRef().clientWidth;
			this.newX = e.clientX || e.changedTouches[0].clientX;
			this.startX = e.clientX || e.changedTouches[0].clientX;
			this.mouseDown = true;

			document.onmousemove = (e) => {
				this.newX = e.clientX || e.changedTouches[0].clientX;
				e.preventDefault();
			}
			document.onmouseup = () => {
				document.onmousemove = null;
				document.onmouseup = null;
				this.mouseDown = false;
				this.submit();
			}
		},

		// 触摸开始事件
		touchStart(e) {
			this.startWidth = this.getSliderRef().clientWidth;
			this.newX = e.touches[0].clientX;
			this.startX = e.touches[0].clientX;
			this.mouseDown = true;
		},

		// 触摸移动事件
		touchMove(e) {
			e.preventDefault();
			this.newX = e.touches[0].clientX;
		},

		// 触摸结束事件
		touchEnd() {
			this.mouseDown = false;
			this.submit();
		},

		// 开始判定
		submit() {
			// 移动端使用固定宽度300px，PC端使用原始宽度470px
			const containerWidth = this.isMobile ? 300 : 470;
			// 滑块宽度，移动端40px，PC端64px
			const sliderWidth = this.isMobile ? 40 : 64;

			//把当前位置偏移传入后端判断
			this.$emit('onSubmit', (this.styleWidth + sliderWidth / 2) / containerWidth * 100);
		},

		// 重置滑块位置
		reset() {
			this.startWidth = 0;
			this.newX = 0;
			this.startX = 0;
			this.mouseDown = false;
		}
	},

	mounted() {
		this.$nextTick(function () {
			mitt.on('openVerifyCode', (res) => {
				this.dialog.backgroundImage = "data:image/png;base64," + res.oriImage
				this.dialog.foregroundImage = "data:image/png;base64," + res.blockImage
				this.reset() // 重置滑块位置
				this.dialog.show = true
			})

			// 刷新验证码（不关闭弹窗，只更新图片和重置滑块）
			mitt.on('refreshVerifyCode', (res) => {
				this.dialog.backgroundImage = "data:image/png;base64," + res.oriImage
				this.dialog.foregroundImage = "data:image/png;base64," + res.blockImage
				this.reset() // 重置滑块位置
				// 不关闭弹窗，保持 this.dialog.show = true
			})

			mitt.on('closeVerifyCode', () => {
				this.dialog.show = false
				this.reset() // 关闭时重置滑块位置
			})

			// 添加窗口大小变化监听
			window.addEventListener('resize', this.handleResize)
		})
	},

	beforeUnmount() {
		// 移除窗口大小变化监听
		window.removeEventListener('resize', this.handleResize)
		// 清理事件监听器
		mitt.off('openVerifyCode')
		mitt.off('refreshVerifyCode')
		mitt.off('closeVerifyCode')
	}
}
</script>

<style lang="less">
.auth-control {
	width: 470px;

	.range-box {
		position: relative;
		width: 100%;
		height: 50px;
		background-color: #eef1f8;
		margin-top: 20px;
		border-radius: 3px;
		box-shadow: 0 0 8px rgba(240, 240, 240, 0.6) inset;

		.range-text {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-size: 14px;
			color: #b7bcd1;
		}

		.range-slider {
			position: absolute;
			height: 100%;
			background-color: rgba(106, 160, 255, 0.8);
			border-radius: 3px;

			.range-btn {
				position: absolute;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 64px;
				height: 50px;
				background-color: #fff;
				border-radius: 3px;
				box-shadow: 0 0 4px #ccc;
				cursor: pointer;

				&>div {
					width: 0;
					height: 40%;
					transition: all 200ms;

					&:nth-child(2) {
						margin: 0 4px;
					}

					border: solid 1px #6aa0ff;
				}

				&:hover,
				&.isDown {
					&>div:first-child {
						border: solid 4px transparent;
						height: 0;
						border-right-color: #6aa0ff;
					}

					&>div:nth-child(2) {
						border-width: 3px;
						height: 0;
						border-radius: 3px;
						margin: 0 6px;
						border-right-color: #6aa0ff;
					}

					&>div:nth-child(3) {
						border: solid 4px transparent;
						height: 0;
						border-left-color: #6aa0ff;
					}
				}
			}
		}
	}
}

/* 移动端适配 */
.verify-dialog {
	.el-dialog__body {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 15px !important;
	}

	.el-dialog__header {
		padding: 15px !important;
		margin-right: 0 !important;
	}

	.el-dialog {
		margin: 0 auto !important;
	}
}

.mobile-container {
	width: 300px;
	height: 170px;
	position: relative;
	user-select: none;
	margin: 0 auto;
	overflow: hidden;
	border-radius: 4px;
	margin-bottom: 10px;
}

.mobile-control {
	width: 300px;
	margin: 0 auto;
	z-index: 10;

	.range-box {
		position: relative;
		width: 100%;
		height: 40px;
		background-color: #eef1f8;
		margin-top: 0;
		border-radius: 4px;
		box-shadow: 0 0 8px rgba(240, 240, 240, 0.6) inset;

		.range-text {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-size: 12px;
			color: #b7bcd1;
		}

		.range-slider {
			position: absolute;
			height: 100%;
			background-color: rgba(106, 160, 255, 0.8);
			border-radius: 3px;

			.range-btn {
				position: absolute;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 40px;
				height: 40px;
				background-color: #fff;
				border-radius: 3px;
				box-shadow: 0 0 4px #ccc;
				cursor: pointer;

				&>div {
					width: 0;
					height: 30%;
					transition: all 200ms;

					&:nth-child(2) {
						margin: 0 4px;
					}

					border: solid 1px #6aa0ff;
				}

				&:hover,
				&.isDown {
					&>div:first-child {
						border: solid 4px transparent;
						height: 0;
						border-right-color: #6aa0ff;
					}

					&>div:nth-child(2) {
						border-width: 3px;
						height: 0;
						border-radius: 3px;
						margin: 0 6px;
						border-right-color: #6aa0ff;
					}

					&>div:nth-child(3) {
						border: solid 4px transparent;
						height: 0;
						border-left-color: #6aa0ff;
					}
				}
			}
		}
	}
}
</style>