<template>
  <div class="test-container">
    <div class="test-header">
      <h2>组织管理功能测试页面</h2>
      <p>测试关联小区和组织详情功能</p>
    </div>

    <div class="test-section">
      <h3>功能测试</h3>
      <div class="test-buttons">
        <el-button type="primary" @click="testCommunitySelector">
          测试关联小区功能
        </el-button>
        <el-button type="success" @click="testOrgDetail">
          测试组织详情功能
        </el-button>
      </div>
    </div>

    <div class="test-section">
      <h3>功能说明</h3>
      <div class="feature-list">
        <div class="feature-item">
          <h4>🏘️ 关联小区功能</h4>
          <ul>
            <li>支持多选小区</li>
            <li>支持搜索过滤</li>
            <li>支持分页浏览</li>
            <li>支持回显已关联的小区</li>
            <li>支持移除已选择的小区</li>
          </ul>
        </div>
        
        <div class="feature-item">
          <h4>📋 组织详情功能</h4>
          <ul>
            <li>基本信息：组织编码、地址、备注、排序</li>
            <li>法人信息：法人姓名、法人电话</li>
            <li>联系信息：邮箱地址</li>
            <li>媒体文件：支持图片和视频上传</li>
            <li>扩展数据：JSON格式的扩展信息</li>
            <li>时间信息：创建时间、更新时间（只读）</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>API接口信息</h3>
      <div class="api-list">
        <div class="api-item">
          <strong>关联小区：</strong>
          <code>POST /manage-api/v1/org/relev-community</code>
          <pre>{ "orgId": "组织ID", "communityIds": ["小区ID1", "小区ID2"] }</pre>
        </div>
        
        <div class="api-item">
          <strong>组织详情：</strong>
          <code>GET/PUT/POST /manage-api/v1/org/detail</code>
          <pre>支持查询、新增、修改组织详细信息</pre>
        </div>
        
        <div class="api-item">
          <strong>获取小区列表：</strong>
          <code>GET /manage-api/v1/community/page</code>
          <pre>分页获取小区列表，支持名称搜索</pre>
        </div>
        
        <div class="api-item">
          <strong>获取已关联小区：</strong>
          <code>GET /manage-api/v1/org/communities</code>
          <pre>获取组织已关联的小区列表（用于回显）</pre>
        </div>
      </div>
    </div>

    <!-- 组件引入 -->
    <community-selector @success="handleCommunitySuccess"></community-selector>
    <org-detail @success="handleDetailSuccess"></org-detail>
  </div>
</template>

<script>
import communitySelector from '@/components/system/communitySelector.vue'
import orgDetail from '@/components/system/orgDetail.vue'
import mitt from '@/utils/mitt'

export default {
  name: 'OrgFunctionTest',
  components: {
    communitySelector,
    orgDetail
  },
  methods: {
    // 测试关联小区功能
    testCommunitySelector() {
      // 使用测试组织ID（实际使用时应该是真实的组织ID）
      const testOrgId = 1
      mitt.emit('openCommunitySelector', testOrgId)
    },

    // 测试组织详情功能
    testOrgDetail() {
      // 使用测试组织ID（实际使用时应该是真实的组织ID）
      const testOrgId = 1
      mitt.emit('openOrgDetail', testOrgId)
    },

    // 小区关联成功回调
    handleCommunitySuccess() {
      this.$message.success('小区关联成功！')
      console.log('小区关联成功回调被触发')
    },

    // 详情保存成功回调
    handleDetailSuccess() {
      this.$message.success('组织详情保存成功！')
      console.log('组织详情保存成功回调被触发')
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.test-header h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
}

.test-header p {
  margin: 0;
  opacity: 0.9;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.test-section h3 {
  margin-top: 0;
  color: #495057;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.test-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.feature-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.feature-item {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.feature-item h4 {
  margin-top: 0;
  color: #28a745;
}

.feature-item ul {
  margin: 0;
  padding-left: 20px;
}

.feature-item li {
  margin-bottom: 4px;
}

.api-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.api-item {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.api-item strong {
  color: #dc3545;
}

.api-item code {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  margin-left: 8px;
}

.api-item pre {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  margin: 8px 0 0 0;
  font-size: 12px;
  overflow-x: auto;
}

@media (max-width: 768px) {
  .feature-list {
    grid-template-columns: 1fr;
  }
  
  .test-buttons {
    flex-direction: column;
  }
}
</style>
