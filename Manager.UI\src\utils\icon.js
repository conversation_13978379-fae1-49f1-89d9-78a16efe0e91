// Element Plus 图标系统 - Vue3 专用
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// Element Plus 图标列表 - 专为系统菜单优化
export const elementPlusIcons = [
  // 🏠 系统管理类
  { name: '首页', component: 'House' },
  { name: '系统管理', component: 'Setting' },
  { name: '用户管理', component: 'User' },
  { name: '角色管理', component: 'UserFilled' },
  { name: '菜单管理', component: 'Menu' },
  { name: '权限管理', component: 'Key' },
  { name: '数据字典', component: 'Collection' },
  { name: '组织管理', component: 'OfficeBuilding' },
  { name: '部门管理', component: 'Coordinate' },
  { name: '员工管理', component: 'Avatar' },
  { name: '职位管理', component: 'Postcard' },
  { name: '微信用户', component: 'ChatDotRound' },

  // 🏢 小区物业类
  { name: '小区管理', component: 'School' },
  { name: '小区信息', component: 'HomeFilled' },
  { name: '楼房管理', component: 'House' },
  { name: '住户管理', component: 'UserFilled' },
  { name: '车辆管理', component: 'Van' },
  { name: '访客管理', component: 'User' },
  { name: '物业缴费', component: 'Money' },
  { name: '缴费项目', component: 'List' },
  { name: '物业账单', component: 'Document' },

  // 🛒 商务购物类
  { name: '好物管理', component: 'ShoppingCart' },
  { name: '商品列表', component: 'Goods' },
  { name: '订单管理', component: 'Tickets' },
  { name: '购物车', component: 'ShoppingCartFull' },
  { name: '商品分类', component: 'Grid' },
  { name: '价格管理', component: 'PriceTag' },

  // 🔧 工单服务类
  { name: '工单管理', component: 'Tools' },
  { name: '工单列表', component: 'List' },
  { name: '服务管理', component: 'CustomerService' },
  { name: '维修管理', component: 'Tools' },
  { name: '投诉建议', component: 'ChatLineRound' },

  // 📢 消息通知类
  { name: '消息中心', component: 'Bell' },
  { name: '消息通知', component: 'Message' },
  { name: '公告管理', component: 'Promotion' },
  { name: '图文管理', component: 'Picture' },

  // 🎯 活动管理类
  { name: '活动管理', component: 'Present' },
  { name: '活动列表', component: 'Calendar' },
  { name: '报名管理', component: 'DocumentAdd' },

  // ⏰ 任务调度类
  { name: '定时任务', component: 'Timer' },
  { name: '任务调度', component: 'Clock' },
  { name: '任务监控', component: 'Monitor' },

  // 📊 统计分析类
  { name: '数据统计', component: 'DataAnalysis' },
  { name: '报表管理', component: 'DataLine' },
  { name: '图表分析', component: 'TrendCharts' },
  { name: '财务统计', component: 'Coin' },

  // 🔒 安全管理类
  { name: '安全管理', component: 'Lock' },
  { name: '日志管理', component: 'Document' },
  { name: '监控管理', component: 'VideoCamera' },
  { name: '备份管理', component: 'FolderAdd' },

  // 📁 文件管理类
  { name: '文件管理', component: 'Folder' },
  { name: '文档管理', component: 'Files' },
  { name: '图片管理', component: 'Picture' },
  { name: '视频管理', component: 'VideoPlay' },
  { name: '附件管理', component: 'Paperclip' },

  // ⚙️ 系统配置类
  { name: '系统配置', component: 'Setting' },
  { name: '参数配置', component: 'Tools' },
  { name: '接口配置', component: 'Connection' },
  { name: '缓存管理', component: 'Refresh' },

  // 📍 位置服务类
  { name: '位置管理', component: 'Location' },
  { name: '地图服务', component: 'MapLocation' },
  { name: '导航服务', component: 'Guide' },

  // 📞 通讯联系类
  { name: '通讯录', component: 'Phone' },
  { name: '联系人', component: 'User' },
  { name: '群组管理', component: 'UserFilled' },

  // 🎨 界面元素类
  { name: '主题配置', component: 'Brush' },
  { name: '布局管理', component: 'Grid' },
  { name: '样式管理', component: 'MagicStick' },

  // 🔄 操作功能类
  { name: '添加', component: 'Plus' },
  { name: '编辑', component: 'Edit' },
  { name: '删除', component: 'Delete' },
  { name: '查看', component: 'View' },
  { name: '搜索', component: 'Search' },
  { name: '刷新', component: 'Refresh' },
  { name: '下载', component: 'Download' },
  { name: '上传', component: 'Upload' },
  { name: '导入', component: 'Upload' },
  { name: '导出', component: 'Download' },
  { name: '打印', component: 'Printer' },
  { name: '分享', component: 'Share' },
  { name: '复制', component: 'CopyDocument' },
  { name: '排序', component: 'Sort' },

  // 📋 列表视图类
  { name: '列表视图', component: 'List' },
  { name: '表格视图', component: 'Grid' },
  { name: '卡片视图', component: 'Collection' },
  { name: '树形视图', component: 'Files' }
]

// 获取图标组件
export function getIconComponent(iconName) {
  return ElementPlusIconsVue[iconName] || null
}

// 导出所有图标组件
export { ElementPlusIconsVue }
