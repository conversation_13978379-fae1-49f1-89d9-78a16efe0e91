import request from '@/utils/request'
import { listCommunityBuilding } from '@/api/community/communityBuilding'
import { listCommunityRoom } from '@/api/community/communityRoom'
import { listCommunityResident } from '@/api/community/communityResident'

// 分页查询小区通知
export const listNotice = (data) =>
  request({
    url: '/manage-api/v1/notice/page',
    method: 'get',
    params: data
  })

// 通过id查询小区通知
export const getNotice = (id) =>
  request({
    url: '/manage-api/v1/notice',
    method: 'get',
    params: { id: id }
  })

// 添加通知
export const addNotice = (data) =>
  request({
    url: '/manage-api/v1/notice',
    method: 'post',
    data: data
  })

// 修改通知
export const editNotice = (data) =>
  request({
    url: '/manage-api/v1/notice',
    method: 'put',
    data: data
  })

// 删除通知
export const deleteNotice = (id) =>
  request({
    url: '/manage-api/v1/notice',
    method: 'delete',
    params: { id: id }
  })

/**
 * 目标ID到名称转换工具类
 */
export class TargetIdConverter {
  constructor(communityId) {
    this.communityId = communityId
    this.buildingCache = new Map() // 楼栋缓存
    this.roomCache = new Map() // 房间缓存
    this.residentCache = new Map() // 住户缓存
    this.buildingDataLoaded = false
    this.roomDataLoaded = false
    this.residentDataLoaded = false
    this.loadingPromises = new Map() // 防止重复请求的Promise缓存
  }

  /**
   * 加载楼栋数据到缓存（防重复请求）
   */
  async loadBuildingData() {
    if (this.buildingDataLoaded) return

    // 防止重复请求
    const cacheKey = 'buildings'
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey)
    }

    const loadingPromise = this._loadBuildingDataInternal()
    this.loadingPromises.set(cacheKey, loadingPromise)

    try {
      await loadingPromise
    } finally {
      this.loadingPromises.delete(cacheKey)
    }
  }

  async _loadBuildingDataInternal() {
    try {
      const response = await listCommunityBuilding({
        pageNum: 1,
        pageSize: 500,
        communityId: this.communityId
      })

      const buildings = response.data.data.list || []
      buildings.forEach(building => {
        // 优先使用楼栋编号，其次楼栋号
        let displayName = building.buildingName || building.buildingNumber
        if (!displayName) {
          displayName = `楼栋${building.id}`
        }

        this.buildingCache.set(building.id, {
          id: building.id,
          buildingName: building.buildingName,
          buildingNumber: building.buildingNumber,
          displayName: displayName
        })
      })

      this.buildingDataLoaded = true
    } catch (error) {
      console.error('加载楼栋数据失败:', error)
      throw error
    }
  }

  /**
   * 加载房间数据到缓存（防重复请求）
   */
  async loadRoomData() {
    if (this.roomDataLoaded) return

    // 防止重复请求
    const cacheKey = 'rooms'
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey)
    }

    const loadingPromise = this._loadRoomDataInternal()
    this.loadingPromises.set(cacheKey, loadingPromise)

    try {
      await loadingPromise
    } finally {
      this.loadingPromises.delete(cacheKey)
    }
  }

  async _loadRoomDataInternal() {
    try {
      // 先确保楼栋数据已加载
      await this.loadBuildingData()

      // 一次性加载所有房间数据，而不是按楼栋分别加载
      const response = await listCommunityRoom({
        pageNum: 1,
        pageSize: 500, // 增加页面大小以获取更多数据
        communityId: this.communityId
      })

      const rooms = response.data.data.list || []

      rooms.forEach(room => {
        const building = this.buildingCache.get(room.buildingId)

        // 房间名称格式：buildingName-unitNumber(如果不为空)-roomNumber
        let buildingIdentifier = building?.buildingName || building?.buildingNumber || `楼栋${room.buildingId}`
        let roomDisplayName = buildingIdentifier

        // 如果有单元号，添加单元号
        if (room.unitNumber && room.unitNumber.trim() !== '') {
          roomDisplayName += `-${room.unitNumber}`
        }

        // 添加房间号
        const roomIdentifier = room.roomNumber || room.roomName || `房间${room.id}`
        roomDisplayName += `-${roomIdentifier}`

        this.roomCache.set(room.id, {
          id: room.id,
          buildingId: room.buildingId,
          buildingNumber: building?.buildingNumber,
          buildingName: building?.buildingName,
          unitNumber: room.unitNumber,
          roomNumber: room.roomNumber,
          roomName: room.roomName,
          displayName: roomDisplayName
        })
      })

      this.roomDataLoaded = true
    } catch (error) {
      console.error('加载房间数据失败:', error)
      throw error
    }
  }

  /**
   * 加载住户数据到缓存（防重复请求）
   */
  async loadResidentData() {
    if (this.residentDataLoaded) return

    // 防止重复请求
    const cacheKey = 'residents'
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey)
    }

    const loadingPromise = this._loadResidentDataInternal()
    this.loadingPromises.set(cacheKey, loadingPromise)

    try {
      await loadingPromise
    } finally {
      this.loadingPromises.delete(cacheKey)
    }
  }

  async _loadResidentDataInternal() {
    try {
      const response = await listCommunityResident({
        pageNum: 1,
        pageSize: 100,
        communityId: this.communityId
      })

      const residents = response.data.data.list || []
      residents.forEach(resident => {
        this.residentCache.set(resident.id, {
          id: resident.id,
          residentName: resident.residentName,
          phone: resident.phone,
          idCardNumber: resident.idCardNumber,
          displayName: resident.residentName
        })
      })

      this.residentDataLoaded = true
    } catch (error) {
      console.error('加载住户数据失败:', error)
      throw error
    }
  }

  /**
   * 将楼栋ID转换为楼栋编号
   * @param {string|number} buildingId 楼栋ID
   * @returns {string} 楼栋编号
   */
  async convertBuildingId(buildingId) {
    await this.loadBuildingData()

    const building = this.buildingCache.get(parseInt(buildingId))
    return building ? building.displayName : `楼栋${buildingId}`
  }

  /**
   * 将房间ID转换为房间名称
   * @param {string|number} roomId 房间ID
   * @returns {string} 房间名称
   */
  async convertRoomId(roomId) {
    await this.loadRoomData()

    const room = this.roomCache.get(parseInt(roomId))
    return room ? room.displayName : `房间${roomId}`
  }

  /**
   * 将住户ID转换为住户名称
   * @param {string|number} residentId 住户ID
   * @returns {string} 住户名称
   */
  async convertResidentId(residentId) {
    await this.loadResidentData()

    const resident = this.residentCache.get(parseInt(residentId))
    return resident ? resident.displayName : `住户${residentId}`
  }

  /**
   * 批量转换楼栋IDs
   * @param {string} buildingIds 逗号分隔的楼栋ID字符串
   * @returns {Promise<string>} 转换后的名称字符串
   */
  async convertBuildingIds(buildingIds) {
    if (!buildingIds || buildingIds.trim() === '') return ''

    const ids = buildingIds.split(',').map(id => id.trim()).filter(id => id)
    const names = await Promise.all(ids.map(id => this.convertBuildingId(id)))
    return names.join(', ')
  }

  /**
   * 批量转换房间IDs
   * @param {string} roomIds 逗号分隔的房间ID字符串
   * @returns {Promise<string>} 转换后的名称字符串
   */
  async convertRoomIds(roomIds) {
    if (!roomIds || roomIds.trim() === '') return ''

    const ids = roomIds.split(',').map(id => id.trim()).filter(id => id)
    const names = await Promise.all(ids.map(id => this.convertRoomId(id)))
    return names.join(', ')
  }

  /**
   * 批量转换住户IDs
   * @param {string} residentIds 逗号分隔的住户ID字符串
   * @returns {Promise<string>} 转换后的名称字符串
   */
  async convertResidentIds(residentIds) {
    if (!residentIds || residentIds.trim() === '') return ''

    const ids = residentIds.split(',').map(id => id.trim()).filter(id => id)
    const names = await Promise.all(ids.map(id => this.convertResidentId(id)))
    return names.join(', ')
  }

  /**
   * 根据目标类型转换IDs
   * @param {string} targetIds 目标IDs字符串
   * @param {string} targetType 目标类型
   * @returns {Promise<string>} 转换后的名称字符串
   */
  async convertTargetIds(targetIds, targetType) {
    if (!targetIds || targetIds.trim() === '') return ''

    try {
      if (targetType === 'building') {
        return await this.convertBuildingIds(targetIds)
      } else if (targetType === 'user' || targetType === 'room') {
        return await this.convertRoomIds(targetIds)
      } else if (targetType === 'resident') {
        return await this.convertResidentIds(targetIds)
      } else {
        return targetIds // 其他类型直接返回原始值
      }
    } catch (error) {
      console.error('转换目标IDs失败:', error)
      return targetIds // 转换失败时返回原始值
    }
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.buildingCache.clear()
    this.roomCache.clear()
    this.residentCache.clear()
    this.loadingPromises.clear()
    this.buildingDataLoaded = false
    this.roomDataLoaded = false
    this.residentDataLoaded = false
  }
}
