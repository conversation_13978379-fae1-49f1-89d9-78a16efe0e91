<template>
  <div class="payment-detail-list-container">
    <div class="payment-detail-list-content">
      <payment-detail-edit @search="search" ref="editDialog" />
      <div class="card card--search search-flex">
        <el-select v-model="searchModel.paymentObjectId" placeholder="选择缴费对象" clearable style="width: 200px; margin-right: 16px;" filterable>
          <el-option 
            v-for="item in paymentObjectList" 
            :key="item.id" 
            :label="item.moniker" 
            :value="item.id" />
        </el-select>
        <el-select v-model="searchModel.communityId" placeholder="选择小区" clearable style="width: 200px; margin-right: 16px;" filterable>
          <el-option 
            v-for="item in communityList" 
            :key="item.id" 
            :label="item.communityName" 
            :value="item.id" />
        </el-select>
        <el-select v-model="searchModel.status" placeholder="状态" clearable style="width: 150px; margin-right: 16px;">
          <el-option 
            v-for="item in statusOptions" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value" />
        </el-select>
        <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
        <el-button type="primary" @click="add">添加</el-button>
      </div>
      <div class="card card--table">
        <div class="table-col">
          <el-table :data="paymentDetailList" row-key="id" style="width: 100%; height: 100%;" class="data-table">
            <el-table-column prop="id" label="ID" width="80" align="center"/>
            <el-table-column prop="paymentItemSnapshot" label="缴费项目" align="center" min-width="150" show-overflow-tooltip/>
            <el-table-column prop="paymentObjectName" label="缴费对象" align="center" width="120"/>
            <el-table-column prop="unitPrice" label="单价" align="center" width="100">
              <template #default="scope">
                <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.unitPrice }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" align="center" width="80"/>
            <el-table-column prop="quantity" label="数量" align="center" width="100"/>
            <el-table-column prop="discount" label="折扣" align="center" width="80">
              <template #default="scope">
                <span>{{ scope.row.discount }}折</span>
              </template>
            </el-table-column>
            <el-table-column prop="totalAmount" label="总金额" align="center" width="120">
              <template #default="scope">
                <span style="color: #f56c6c; font-weight: bold;">¥{{ calculateAmount(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" align="center" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="small">
                  {{ getStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="billingCycle" label="计费周期" align="center" width="100">
              <template #default="scope">
                <span>{{ getBillingCycleLabel(scope.row.billingCycle) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="effectiveDate" label="生效日期" align="center" width="120"/>
            <el-table-column prop="expiringDate" label="失效日期" align="center" width="120"/>
            <el-table-column prop="createTime" label="创建时间" align="center" width="160"/>
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="scope">
                <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
                <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination background layout="prev, pager, next" @current-change="currentChange" 
            :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listPropertyPaymentDetail, deletePropertyPaymentDetail, getPropertyPaymentDetail } from '@/api/property/paymentItems'
import { listPropertyPaymentObject } from '@/api/property/paymentItems'
import { listCommunity } from '@/api/community/community'
import { listDictByNameEn } from '@/api/system/dict'
import mitt from '@/utils/mitt'
import paymentDetailEdit from '@/components/property/paymentDetailEdit.vue'

export default {
  components: { paymentDetailEdit },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        paymentObjectId: '',
        communityId: '',
        status: ''
      },
      paymentDetailList: [],
      paymentObjectList: [],
      communityList: [],
      statusOptions: [],
      total: 0
    }
  },
  
  methods: {
    search() {
      // 过滤空值参数
      const params = {}
      Object.keys(this.searchModel).forEach(key => {
        if (this.searchModel[key] !== '' && this.searchModel[key] !== null && this.searchModel[key] !== undefined) {
          params[key] = this.searchModel[key]
        }
      })
      
      listPropertyPaymentDetail(params).then(res => {
        this.paymentDetailList = res.data.data.list || []
        this.total = res.data.data.total || 0
        
        // 补充缴费对象名称显示
        this.paymentDetailList.forEach(item => {
          const paymentObject = this.paymentObjectList.find(obj => obj.id === item.paymentObjectId)
          item.paymentObjectName = paymentObject ? paymentObject.moniker : '--'
        })
      }).catch(err => {
        this.$message.error(err.data?.errorMessage || '查询失败')
      })
    },
    
    /**
     * 计算总金额
     */
    calculateAmount(row) {
      const amount = (row.unitPrice || 0) * (row.quantity || 0) * (row.discount || 10) / 10
      return amount.toFixed(2)
    },
    
    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const statusMap = {
        'active': 'success',
        'inactive': 'danger',
        'pending': 'warning',
        'expired': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    /**
     * 获取状态标签
     */
    getStatusLabel(status) {
      const option = this.statusOptions.find(item => item.value === status)
      return option ? option.label : status || '--'
    },
    
    /**
     * 获取计费周期标签
     */
    getBillingCycleLabel(cycle) {
      const cycleMap = {
        'monthly': '月度',
        'quarterly': '季度',
        'yearly': '年度'
      }
      return cycleMap[cycle] || cycle || '--'
    },
    
    /**
     * 加载缴费对象列表
     */
    loadPaymentObjectList() {
      listPropertyPaymentObject({ pageNum: 1, pageSize: 500 }).then(res => {
        this.paymentObjectList = res.data.data.list || []
      }).catch(err => {
        console.error('加载缴费对象列表失败:', err)
      })
    },
    
    /**
     * 加载小区列表
     */
    loadCommunityList() {
      listCommunity({ pageNum: 1, pageSize: 500 }).then(res => {
        this.communityList = res.data.data.list || []
      }).catch(err => {
        console.error('加载小区列表失败:', err)
      })
    },
    
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载缴费明细状态字典
        const statusRes = await listDictByNameEn('property_payment_detail_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))
        
        // 如果字典为空，使用默认选项
        if (this.statusOptions.length === 0) {
          this.statusOptions = [
            { value: 'active', label: '有效' },
            { value: 'inactive', label: '无效' },
            { value: 'pending', label: '待处理' },
            { value: 'expired', label: '已过期' }
          ]
        }
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用默认状态选项
        this.statusOptions = [
          { value: 'active', label: '有效' },
          { value: 'inactive', label: '无效' },
          { value: 'pending', label: '待处理' },
          { value: 'expired', label: '已过期' }
        ]
      }
    },
    
    add() {
      mitt.emit('openPaymentDetailAdd')
    },
    
    edit(id) {
      getPropertyPaymentDetail(id).then(res => {
        mitt.emit('openPaymentDetailEdit', res.data.data)
      }).catch(err => {
        this.$message.error(err.data?.errorMessage || '获取数据失败')
      })
    },
    
    deleted(id) {
      this.$confirm('删除缴费明细, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePropertyPaymentDetail(id).then(() => {
          this.search()
          this.$message.success('操作成功')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '删除失败')
        })
      }).catch(() => {})
    },
    
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    }
  },
  
  async created() {
    await this.initDictData()
    this.loadCommunityList()
    this.loadPaymentObjectList()
    this.search()
  }
}
</script>

<style scoped>
::v-deep .--el-table-index {
  z-index: 9999 !important;
}

.payment-detail-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.payment-detail-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.dark-theme .card {
  background-color: var(--card-background);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.search-flex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
