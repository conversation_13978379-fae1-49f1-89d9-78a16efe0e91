import request from '@/utils/request'

// 分页查询职位
export function listPosition(params) {
  return request({
    url: '/manage-api/v1/position/page',
    method: 'get',
    params
  })
}

// 通过ID查询职位
export function getPosition(id) {
  return request({
    url: '/manage-api/v1/position',
    method: 'get',
    params: { id }
  })
}

// 新增职位
export function addPosition(data) {
  return request({
    url: '/manage-api/v1/position',
    method: 'post',
    data
  })
}

// 编辑职位
export function editPosition(data) {
  return request({
    url: '/manage-api/v1/position',
    method: 'put',
    data
  })
}

// 删除职位
export function deletePosition(id) {
  return request({
    url: '/manage-api/v1/position',
    method: 'delete',
    params: { id }
  })
}