<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="600px" class="building-edit-dialog">
    <el-form :model="buildingModel" :rules="rules" ref="formRef" label-width="100px" class="building-edit-form">
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="楼栋编号" prop="buildingNumber">
            <el-input v-model="buildingModel.buildingNumber" placeholder="请输入楼栋编号" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="经度" prop="lng">
            <el-input v-model="buildingModel.lng" :precision="6" style="width: 100%;" placeholder="经度" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="lat">
            <el-input v-model="buildingModel.lat" :precision="6" style="width: 100%;" placeholder="纬度" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="海拔" prop="alt">
            <el-input v-model="buildingModel.alt" :precision="2" style="width: 100%;" placeholder="海拔" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="buildingModel.sort" :min="0" style="width: 100%;" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="父级ID" prop="parentId">
            <el-input-number v-model="buildingModel.parentId" :min="0" style="width: 100%;" placeholder="父级ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="备注" prop="note">
            <el-input v-model="buildingModel.note" type="textarea" :rows="3" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { addCommunityBuilding, editCommunityBuilding } from '@/api/community/communityBuilding'
import mitt from '@/utils/mitt'
import { getSelectedCommunityId } from "@/store/modules/options";
export default {
  name: 'communityBuildingEdit',
  data() {
    return {
      buildingModel: {
        id: undefined,
        buildingNumber: '',
        communityId: null, // 自动设置为当前选中的小区
        lng: null,
        lat: null,
        alt: null,
        sort: 0,
        parentId: null,
        note: ''
      },
      dialog: {
        show: false,
        title: ''
      },
      rules: {
        buildingNumber: [
          { required: true, message: '请输入建筑编号', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    resetForm() {
      this.$refs.formRef && this.$refs.formRef.resetFields()
      this.buildingModel = {
        id: undefined,
        buildingNumber: '',
        communityId: getSelectedCommunityId(), // 自动设置为当前选中的小区
        lng: null,
        lat: null,
        alt: null,
        sort: 0,
        parentId: null,
        note: ''
      }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.buildingModel.id ? editCommunityBuilding : addCommunityBuilding
        this.buildingModel.communityId = getSelectedCommunityId()
        api(this.buildingModel).then(() => {
          this.$message.success('保存成功')
          this.dialog.show = false
          this.$emit('search')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '保存失败')
        })
      })
    }
  },
  mounted() {
    mitt.on('openCommunityBuildingEdit', (data) => {
      this.resetForm()
      if (data && data.id) {
        this.buildingModel = { ...data }
        this.dialog.title = '编辑楼房信息'
      } else {
        // 新增时确保设置当前选中的小区ID
        this.buildingModel.communityId = getSelectedCommunityId()
        this.dialog.title = '新增楼房信息'
      }
      this.dialog.show = true
    })
  },
  beforeDestroy() {
    mitt.off('openCommunityBuildingEdit')
  }
}
</script>

<style scoped>
.building-edit-dialog >>> .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 0;
}
.building-edit-form {
  padding: 0 10px;
}
.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}
</style>