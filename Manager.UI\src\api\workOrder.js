import request from '@/utils/request'

/**
 * 通过ID查询工单
 * @param {number} id - 工单ID
 * @returns {Promise} 工单详情
 */
export const getWorkOrder = (id) =>
  request({
    url: '/manage-api/v1/community/work-order',
    method: 'get',
    params: { id: id }
  })

/**
 * 添加工单
 * @param {Object} data - 工单数据
 * @returns {Promise} 添加结果
 */
export const addWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/community/work-order',
    method: 'post',
    data
  })

/**
 * 编辑工单
 * @param {Object} data - 工单数据
 * @returns {Promise} 编辑结果
 */
export const editWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/community/work-order',
    method: 'put',
    data
  })

/**
 * 删除工单
 * @param {number} id - 工单ID
 * @returns {Promise} 删除结果
 */
export const deleteWorkOrder = (id) =>
  request({
    url: '/manage-api/v1/community/work-order',
    method: 'delete',
    params: { id: id }
  })

/**
 * 工单分页查询
 * @param {Object} data - 查询参数
 * @returns {Promise} 工单列表
 */
export const listWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/community/work-order/page',
    method: 'get',
    params: data
  })

/**
 * 工单类型字典
 */
export const WORK_ORDER_TYPES = [
  { value: 'repair', label: '维修' },
  { value: 'complaint', label: '投诉' },
  { value: 'suggestion', label: '建议' },
  { value: 'other', label: '其他' }
]

/**
 * 区域类型字典
 */
export const REGION_TYPES = [
  { value: 'house', label: '房屋' },
  { value: 'public_area', label: '公共区域' }
]

/**
 * 受理工单
 * @param {Object} data - 受理数据
 * @returns {Promise} 受理结果
 */
export const acceptWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/community/work-order/accepted',
    method: 'put',
    data
  })

/**
 * 处理工单
 * @param {Object} data - 处理数据
 * @returns {Promise} 处理结果
 */
export const processWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/community/work-order/processing',
    method: 'put',
    data
  })

/**
 * 挂起工单
 * @param {Object} data - 挂起数据
 * @returns {Promise} 挂起结果
 */
export const pendingWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/community/work-order/pending',
    method: 'put',
    data
  })

/**
 * 完成工单
 * @param {Object} data - 完成数据
 * @returns {Promise} 完成结果
 */
export const completeWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/community/work-order/complete',
    method: 'put',
    data
  })

/**
 * 取消工单
 * @param {Object} data - 取消数据
 * @returns {Promise} 取消结果
 */
export const cancelWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/community/work-order/cancel',
    method: 'put',
    data
  })

/**
 * 获取可分配人员列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 人员列表
 */
export const getWorkOrderPersons = (params) =>
  request({
    url: '/manage-api/v1/community/work-order/person',
    method: 'get',
    params
  })

/**
 * 工单状态字典
 */
export const WORK_ORDER_STATUS = [
  { value: 'wait_process', label: '待处理' },
  { value: 'accepted', label: '已受理' },
  { value: 'processing', label: '处理中' },
  { value: 'pending', label: '已挂起' },
  { value: 'completed', label: '已完成' },
  { value: 'cancel', label: '已取消' }
]
