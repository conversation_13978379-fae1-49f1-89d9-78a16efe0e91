import request from '@/utils/request'

/**
 * 通过ID查询工单
 * @param {number} id - 工单ID
 * @returns {Promise} 工单详情
 */
export const getWorkOrder = (id) =>
  request({
    url: '/manage-api/v1/community/work-order',
    method: 'get',
    params: { id: id }
  })

/**
 * 添加工单
 * @param {Object} data - 工单数据
 * @returns {Promise} 添加结果
 */
export const addWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/community/work-order',
    method: 'post',
    data
  })

/**
 * 编辑工单
 * @param {Object} data - 工单数据
 * @returns {Promise} 编辑结果
 */
export const editWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/community/work-order',
    method: 'put',
    data
  })

/**
 * 删除工单
 * @param {number} id - 工单ID
 * @returns {Promise} 删除结果
 */
export const deleteWorkOrder = (id) =>
  request({
    url: '/manage-api/v1/community/work-order',
    method: 'delete',
    params: { id: id }
  })

/**
 * 工单分页查询
 * @param {Object} data - 查询参数
 * @returns {Promise} 工单列表
 */
export const listWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/community/work-order/page',
    method: 'get',
    params: data
  })

/**
 * 工单类型字典
 */
export const WORK_ORDER_TYPES = [
  { value: 'repair', label: '维修' },
  { value: 'complaint', label: '投诉' },
  { value: 'suggestion', label: '建议' },
  { value: 'other', label: '其他' }
]

/**
 * 区域类型字典
 */
export const REGION_TYPES = [
  { value: 'house', label: '房屋' },
  { value: 'public_area', label: '公共区域' }
]

/**
 * 工单状态字典
 */
export const WORK_ORDER_STATUS = [
  { value: 'wait_process', label: '待处理' },
  { value: 'processing', label: '处理中' },
  { value: 'completed', label: '已完成' },
  { value: 'cancel', label: '已取消' }
]
