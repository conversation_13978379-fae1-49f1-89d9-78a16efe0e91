<template>
  <div class="route-test">
    <h2>路由测试页面</h2>
    <p>当前路由: {{ $route.path }}</p>
    <p>当前时间: {{ currentTime }}</p>
    
    <div class="debug-info">
      <h3>调试信息</h3>
      <el-button @click="checkRoutes">检查注册的路由</el-button>
      <el-button @click="checkModules">检查可用组件</el-button>
      <el-button @click="checkPermissions">检查权限数据</el-button>
      
      <div v-if="debugData" class="debug-output">
        <pre>{{ debugData }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RouteTest',
  data() {
    return {
      currentTime: new Date().toLocaleString(),
      debugData: null
    }
  },
  mounted() {
    console.log('路由测试页面已加载:', this.$route.path)
    this.currentTime = new Date().toLocaleString()
  },
  methods: {
    checkRoutes() {
      const routes = this.$router.getRoutes()
      this.debugData = JSON.stringify(routes.map(r => ({
        path: r.path,
        name: r.name,
        component: r.component?.name || 'Unknown'
      })), null, 2)
      console.log('注册的路由:', routes)
    },
    
    checkModules() {
      // 这里无法直接访问 modules，但可以检查其他信息
      this.debugData = '组件模块信息请查看控制台'
      console.log('检查组件模块...')
    },
    
    checkPermissions() {
      const permissions = window.$local?.get('frontPermissions')
      const treePermissions = window.$local?.get('treePermissions')
      this.debugData = JSON.stringify({
        permissions: permissions?.length || 0,
        treePermissions: treePermissions?.length || 0,
        samplePermission: permissions?.[0]
      }, null, 2)
      console.log('权限数据:', { permissions, treePermissions })
    }
  }
}
</script>

<style scoped>
.route-test {
  padding: 20px;
}

.debug-info {
  margin-top: 20px;
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 5px;
}

.debug-output {
  margin-top: 15px;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 3px;
  max-height: 400px;
  overflow-y: auto;
}

pre {
  margin: 0;
  font-size: 12px;
}
</style>
