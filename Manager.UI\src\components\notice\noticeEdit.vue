<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="800px" class="notice-edit-dialog">
    <el-form :model="noticeModel" :rules="rules" ref="formRef" label-width="100px" class="notice-edit-form">
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <el-input v-model="noticeModel.title" maxlength="100" show-word-limit placeholder="请输入标题" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="通知类型" prop="type">
            <el-select v-model="noticeModel.type" placeholder="请选择通知类型" style="width: 100%;" clearable>
              <el-option
                v-for="item in noticeTypeList"
                :key="item.nameEn"
                :label="item.nameCn"
                :value="item.nameEn" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="目标类型" prop="targetType">
            <el-select v-model="noticeModel.targetType" placeholder="请选择目标类型" style="width: 100%;" clearable>
              <el-option
                v-for="item in noticeTargetList"
                :key="item.nameEn"
                :label="item.nameCn"
                :value="item.nameEn" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="通知目标" prop="targetIds">
            <div class="target-ids-container">
              <el-input
                v-model="targetDisplayText"
                :placeholder="getTargetPlaceholder()"
                readonly
                class="target-ids-input" />
              <el-button
                type="primary"
                @click="openTargetSelector"
                :disabled="!noticeModel.targetType || isAllTargetType"
                class="select-btn">
                选择
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="noticeModel.sort" :min="0" :max="9999" style="width: 100%;" placeholder="请输入排序值" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否置顶" prop="top">
            <el-switch v-model="noticeModel.top" active-text="置顶" inactive-text="普通" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="图片" prop="imageUrl">
            <el-upload class="avatar-uploader" :show-file-list="false" :action="uploadUrl" :headers="headers"
              :on-preview="handlePreview" :on-remove="handleRemove" :on-success="uploadSuccess"
              list-type="picture-card">

              <img v-if="imageShow" :src="imageUrl" style="width: 100%;height: 100%;" />
              <el-button v-else size="small" type="primary">点击上传</el-button>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="内容" prop="content">
            <wang-editor ref="editorOne" v-model="noticeModel.content" @change="change" style="min-height: 200px;" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">保存</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 目标选择器弹窗 -->
  <el-dialog
    v-model="targetSelectorDialog.show"
    :title="targetSelectorDialog.title"
    width="800px"
    class="target-selector-dialog">

    <!-- 楼栋选择器 -->
    <div v-if="targetSelectorDialog.type === 'building'" class="building-selector">
      <div class="selector-header">
        <span class="selected-count">已选择 {{ selectedBuildings.length }} 个楼栋</span>
        <el-button @click="clearBuildingSelection" size="small">清空选择</el-button>
      </div>
      <div class="building-tree-container" v-loading="buildingLoading">
        <el-tree
          ref="buildingTreeRef"
          :data="buildingTreeData"
          :props="buildingTreeProps"
          show-checkbox
          node-key="id"
          :default-checked-keys="selectedBuildingIds"
          @check="onBuildingTreeCheck"
          class="building-tree">
          <template #default="{ data }">
            <div class="tree-node">
              <span class="node-label">{{ data.name }}</span>
              <span class="node-type">{{ data.type === 'building' ? '楼栋' : '房间' }}</span>
            </div>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 房间选择器 -->
    <div v-if="targetSelectorDialog.type === 'room'" class="room-selector">
      <div class="selector-header">
        <span class="selected-count">已选择 {{ selectedRooms.length }} 个房间</span>
        <el-button @click="clearRoomSelection" size="small">清空选择</el-button>
      </div>
      <div class="room-tree-container" v-loading="roomTreeLoading">
        <el-tree
          ref="roomTreeRef"
          :data="roomTreeData"
          :props="roomTreeProps"
          show-checkbox
          node-key="id"
          :default-expanded-keys="expandedBuildingIds"
          :default-checked-keys="selectedRoomIds"
          @check="onRoomTreeCheck"
          class="room-tree">
          <template #default="{ data }">
            <div class="tree-node">
              <span class="node-label">{{ data.name }}</span>
              <span class="node-type">{{ data.type === 'building' ? '楼栋' : '房间' }}</span>
            </div>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 小区选择器 -->
    <div v-if="targetSelectorDialog.type === 'community'" class="community-selector">
      <div class="selector-header">
        <span class="selected-count">已选择 {{ selectedCommunities.length }} 个小区</span>
        <el-button @click="clearCommunitySelection" size="small">清空选择</el-button>
      </div>

      <!-- 小区列表 -->
      <div class="community-list-container" v-loading="communityLoading">
        <el-table
          ref="communityTableRef"
          :data="communityList"
          @selection-change="onCommunitySelectionChange"
          row-key="id"
          max-height="400px"
          style="width: 100%;">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="communityName" label="小区名称" width="200" />
          <el-table-column prop="address" label="地址" show-overflow-tooltip />
          <el-table-column prop="note" label="备注" width="120" show-overflow-tooltip />
        </el-table>
      </div>
    </div>

    <!-- 住户选择器 -->
    <div v-if="targetSelectorDialog.type === 'resident'" class="resident-selector">
      <div class="selector-header">
        <span class="selected-count">已选择 {{ selectedResidents.length }} 个住户</span>
        <el-button @click="clearResidentSelection" size="small">清空选择</el-button>
      </div>

      <!-- 搜索框 -->
      <div class="search-container" style="margin-bottom: 16px;">
        <el-input
          v-model="residentSearchName"
          placeholder="请输入住户姓名搜索"
          clearable
          @input="onResidentSearch"
          style="width: 300px;">
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 住户列表 -->
      <div class="resident-list-container" v-loading="residentLoading">
        <el-table
          ref="residentTableRef"
          :data="filteredResidentList"
          @selection-change="onResidentSelectionChange"
          row-key="residentId"
          max-height="400px"
          style="width: 100%;">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="residentName" label="姓名" width="120" />
          <el-table-column prop="phone" label="电话" width="140" />
          <el-table-column prop="idCardNumber" label="身份证号" show-overflow-tooltip />
        </el-table>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="targetSelectorDialog.show = false">取消</el-button>
        <el-button type="primary" @click="confirmTargetSelection">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { addNotice, editNotice, TargetIdConverter } from '@/api/notice'
import { listDictByNameEn } from '@/api/system/dict'
import { listCommunityBuilding } from '@/api/community/communityBuilding'
import { listCommunity } from '@/api/community/community'
import { listCommunityResident } from '@/api/community/communityResident'
import { getSelectedCommunityId } from '@/store/modules/options'
import wangEditor from '@/components/wangEditor/index.vue'
import mitt from '@/utils/mitt'

export default {
  name: 'noticeEdit',
  components: { wangEditor },
  data() {
    return {
      loading: false,
      uploadUrl: import.meta.env.VITE_BASE_API + "/common-api/v1/file/upload",
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      headers: {
        'Authorization': JSON.parse(window.$local?.get('smartPropertyToken')).access_token
      },
      noticeModel: {
        id: null,
        title: '',
        type: '',
        targetType: '',
        targetIds: '',
        imageUrl: '',
        content: '',
        sort: 0,
        top: false
      },
      noticeTypeList: [],
      noticeTargetList: [],
      imageShow: false,
      imageUrl: '',
      dialog: {
        show: false,
        title: '',
        imagePreview: false,
        imagePreviewUrl: ''
      },
      // 目标选择器相关数据
      targetSelectorDialog: {
        show: false,
        title: '',
        type: '' // 'building'、'room'、'resident' 或 'community'
      },
      // 显示文本和ID映射
      targetDisplayText: '', // 用于显示的文本
      targetIdNameMap: new Map(), // ID到名称的映射
      targetIdConverter: null, // ID到名称转换器实例
      // 楼栋选择器数据
      buildingList: [],
      buildingTreeData: [],
      buildingLoading: false,
      selectedBuildingIds: [],
      selectedBuildings: [],
      buildingTreeProps: {
        children: 'children',
        label: 'name'
        // 楼栋选择器中所有节点都可选择（现在只有楼栋节点）
      },

      // 小区选择器数据
      communityList: [],
      communityLoading: false,
      selectedCommunities: [],
      selectedCommunityIds: [],

      // 住户选择器数据
      residentList: [],
      filteredResidentList: [],
      residentLoading: false,
      selectedResidents: [],
      selectedResidentIds: [],
      residentSearchName: '',
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择通知类型', trigger: 'change' }
        ],
        targetType: [
          { required: true, message: '请选择目标类型', trigger: 'change' }
        ]
      },
      uploadData: {}
    }
  },
  computed: {
    /**
     * 是否为全体目标类型
     */
    isAllTargetType() {
      return this.noticeModel.targetType === 'all' || this.noticeModel.targetType === '全体'
    }
  },
  watch: {
    /**
     * 监听目标类型变化
     */
    'noticeModel.targetType': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && oldVal !== undefined) {
          this.handleTargetTypeChange(newVal, oldVal)
        }
      },
      immediate: false
    }
  },
  methods: {
    /**
     * 获取目标输入框占位符文本
     */
    getTargetPlaceholder() {
      if (this.isAllTargetType) {
        return '全体通知，无需选择具体目标'
      }

      const targetType = this.noticeModel.targetType
      if (targetType === 'building') {
        return '请点击选择按钮选择楼栋'
      } else if (targetType === 'resident') {
        return '请点击选择按钮选择住户'
      } else if (targetType === 'community') {
        return '请点击选择按钮选择小区'
      }

      return '请先选择目标类型'
    },

    /**
     * 处理目标类型变化
     */
    handleTargetTypeChange(newTargetType, oldTargetType) {
      // 如果新旧类型不同，清空目标IDs和显示文本
      // 但是在编辑模式下（有ID的情况下），不清空已有的targetIds
      if (newTargetType !== oldTargetType) {
        console.log('目标类型变化:', { newTargetType, oldTargetType, isEdit: !!this.noticeModel.id })

        // 如果是编辑模式且有targetIds，不清空
        if (this.noticeModel.id && this.noticeModel.targetIds) {
          console.log('编辑模式，保留现有targetIds:', this.noticeModel.targetIds)
          return
        }

        // 新增模式或编辑模式下用户主动切换类型时，清空选择
        this.noticeModel.targetIds = ''
        this.targetDisplayText = ''
        this.targetIdNameMap.clear()
        this.clearAllSelections()
      }
    },

    /**
     * 更新目标显示文本（仅在手动调用时使用）
     */
    async updateTargetDisplayText(targetIds, targetType = null) {
      if (!targetIds || this.isAllTargetType) {
        this.targetDisplayText = ''
        return
      }

      const currentTargetType = targetType || this.noticeModel.targetType
      if (!currentTargetType) {
        this.targetDisplayText = targetIds
        return
      }

      // 直接使用转换器进行转换（编辑时优先使用转换器）
      if (this.targetIdConverter) {
        try {
          const displayText = await this.targetIdConverter.convertTargetIds(targetIds, currentTargetType)
          if (displayText && displayText !== targetIds) {
            this.targetDisplayText = displayText
            this.updateLocalIdNameMapping(targetIds, displayText)
            return
          }
        } catch (error) {
          console.warn('转换目标IDs失败:', error)
        }
      }

      // 如果转换器不可用，尝试使用本地映射
      const ids = targetIds.split(',').map(id => id.trim()).filter(id => id)
      const names = []
      let hasAllMappings = true

      for (const id of ids) {
        const name = this.targetIdNameMap.get(id)
        if (name) {
          names.push(name)
        } else {
          hasAllMappings = false
          names.push(`ID:${id}`) // 显示ID格式
        }
      }

      if (hasAllMappings && names.length > 0) {
        this.targetDisplayText = names.join(', ')
      } else {
        // 转换失败时显示原始ID
        this.targetDisplayText = targetIds
      }
    },

    /**
     * 清空所有选择状态
     */
    clearAllSelections() {
      // 清空楼栋选择
      this.selectedBuildingIds = []
      this.selectedBuildings = []

      // 清空小区选择
      this.selectedCommunities = []
      this.selectedCommunityIds = []

      // 清空住户选择
      this.selectedResidents = []
      this.selectedResidentIds = []
      this.residentSearchName = ''

      // 清空控件选中状态
      this.$nextTick(() => {
        if (this.$refs.buildingTreeRef) {
          this.$refs.buildingTreeRef.setCheckedKeys([])
        }
        if (this.$refs.communityTableRef) {
          this.$refs.communityTableRef.clearSelection()
        }
        if (this.$refs.residentTableRef) {
          this.$refs.residentTableRef.clearSelection()
        }
      })
    },

    /**
     * 富文本编辑器内容变化
     */
    change(res) {
      this.noticeModel.content = res
    },

    /**
     * 图片上传成功
     */
    uploadSuccess(res) {
      if (res.code == 0) {
        this.imageShow = true
        this.imageUrl = this.imgServer + res.data
        this.noticeModel.imageUrl = res.data
      } else {
        this.$message.error(res.message)
      }
    },

    /**
     * 图片预览
     */
    handlePreview() {
      // 图片预览逻辑
    },

    /**
     * 移除图片
     */
    handleRemove() {
      this.imageShow = false
      this.imageUrl = ''
      this.noticeModel.imageUrl = ''
    },

    /**
     * 提交表单
     */
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return

        // 获取当前选中的小区ID
        const communityId = getSelectedCommunityId();
        if (!communityId) {
          this.$message.warning('请先选择小区');
          return;
        }

        this.loading = true
        const api = this.noticeModel.id ? editNotice : addNotice

        // 确保数据类型正确，并添加 communityId
        const submitData = {
          ...this.noticeModel,
          communityId: communityId,
          sort: Number(this.noticeModel.sort) || 0,
          top: Boolean(this.noticeModel.top)
        }

        api(submitData)
          .then(() => {
            this.$message.success('操作成功')
            this.$emit('search')
            this.dialog.show = false
            this.resetForm()
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '操作失败')
          })
          .finally(() => {
            this.loading = false
          })
      })
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.noticeModel = {
        id: null,
        title: '',
        type: '',
        targetType: '',
        targetIds: '',
        imageUrl: '',
        content: '',
        sort: 0,
        top: false
      }
      this.imageShow = false
      this.imageUrl = ''
      this.targetDisplayText = ''
      this.targetIdNameMap.clear()

      // 清空所有选择状态
      this.selectedBuildingIds = []
      this.selectedBuildings = []
      this.selectedCommunities = []
      this.selectedCommunityIds = []
      this.selectedResidents = []
      this.selectedResidentIds = []
      this.residentSearchName = ''
      this.buildingList = []
      this.buildingTreeData = []
      this.communityList = []
      this.residentList = []
      this.filteredResidentList = []

      // 重置转换器缓存
      if (this.targetIdConverter) {
        this.targetIdConverter.clearCache()
      }

      // 重置富文本编辑器内容
      this.$nextTick(() => {
        if (this.$refs.editorOne) {
          this.$refs.editorOne.setHtml('')
        }
      })

      // 重置表单验证
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate()
        }
      })
    },

    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        const [noticeType_res, noticeTarget_res] = await Promise.all([
          listDictByNameEn('notice_type'),
          listDictByNameEn('notice_target')
        ])

        this.noticeTypeList = noticeType_res.data.data || []
        this.noticeTargetList = noticeTarget_res.data.data || []
      } catch (err) {
        this.$message.error(err.data?.errorMessage || '加载字典数据失败')
      }
    },

    /**
     * 初始化目标ID转换器
     */
    initTargetIdConverter() {
      const communityId = getSelectedCommunityId()
      if (communityId) {
        this.targetIdConverter = new TargetIdConverter(communityId)
      }
    },



    /**
     * 更新本地ID到名称的映射
     */
    updateLocalIdNameMapping(targetIds, displayText) {
      if (!targetIds || !displayText) return

      const ids = targetIds.split(',').map(id => id.trim()).filter(id => id)
      const names = displayText.split(',').map(name => name.trim()).filter(name => name)

      // 清空现有映射
      this.targetIdNameMap.clear()

      // 建立ID到名称的映射
      ids.forEach((id, index) => {
        if (index < names.length) {
          this.targetIdNameMap.set(id, names[index])
        }
      })
    },

    /**
     * 打开目标选择器
     */
    openTargetSelector() {
      if (!this.noticeModel.targetType) {
        this.$message.warning('请先选择目标类型')
        return
      }

      // 根据目标类型确定选择器类型
      const targetTypeMapping = {
        'building': 'building',   // 楼栋类型 -> 楼栋选择器
        'resident': 'resident',   // 住户类型 -> 住户选择器
        'community': 'community'  // 小区类型 -> 小区选择器
      }

      const selectorType = targetTypeMapping[this.noticeModel.targetType]
      if (!selectorType) {
        this.$message.warning('当前目标类型暂不支持选择器')
        return
      }

      this.targetSelectorDialog.type = selectorType

      // 设置弹窗标题
      const titleMapping = {
        'building': '选择楼栋',
        'resident': '选择住户',
        'community': '选择小区'
      }
      this.targetSelectorDialog.title = titleMapping[selectorType]
      this.targetSelectorDialog.show = true

      // 解析已有的目标IDs
      this.parseExistingTargetIds()

      // 加载数据并设置选中状态
      if (selectorType === 'building') {
        this.loadBuildingList().then(() => {
          // 使用setTimeout确保DOM完全渲染后再设置选中状态
          this.$nextTick(() => {
            if (this.$refs.buildingTreeRef && this.selectedBuildingIds.length > 0) {
              console.log('设置楼栋选中状态:', this.selectedBuildingIds)
              this.$refs.buildingTreeRef.setCheckedKeys(this.selectedBuildingIds)
            }
          })
        })
      } else if (selectorType === 'community') {
        this.loadCommunityList().then(() => {
          // 使用setTimeout确保DOM完全渲染后再设置选中状态
          setTimeout(() => {
            if (this.$refs.communityTableRef && this.selectedCommunityIds.length > 0) {
              console.log('openTargetSelector-设置小区选中状态:', this.selectedCommunityIds)

              // 使用 communityList 中的数据对象来设置选中状态
              this.selectedCommunityIds.forEach(communityId => {
                const community = this.communityList.find(c => c.id === communityId)
                if (community) {
                  console.log('openTargetSelector-设置小区选中:', { communityId, name: community.communityName })
                  this.$refs.communityTableRef.toggleRowSelection(community, true)
                } else {
                  console.warn('openTargetSelector-未找到小区数据:', communityId)
                }
              })
            }
          }, 200) // 增加延迟时间确保DOM完全渲染
        })
      } else if (selectorType === 'resident') {
        this.loadResidentList().then(() => {
          // 使用setTimeout确保DOM完全渲染后再设置选中状态
          setTimeout(() => {
            if (this.$refs.residentTableRef && this.selectedResidentIds.length > 0) {
              console.log('openTargetSelector-设置住户选中状态:', this.selectedResidentIds)

              // 使用 residentList 中的数据对象来设置选中状态
              this.selectedResidentIds.forEach(residentId => {
                const resident = this.residentList.find(r => r.residentId === residentId)
                if (resident) {
                  console.log('openTargetSelector-设置住户选中:', { residentId, name: resident.residentName })
                  this.$refs.residentTableRef.toggleRowSelection(resident, true)
                } else {
                  console.warn('openTargetSelector-未找到住户数据:', residentId)
                }
              })
            }
          }, 200) // 增加延迟时间确保DOM完全渲染
        })
      }
    },

    /**
     * 处理编辑时的目标IDs回显（简化版本）
     */
    async handleEditTargetIds(targetIds, targetType) {
      if (!targetIds || !targetType) return

      console.log('处理编辑目标IDs:', { targetIds, targetType })

      // 根据目标类型处理
      if (targetType === 'building') {
        // 解析楼栋IDs（数字类型）
        const ids = targetIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
        if (ids.length > 0) {
          await this.handleEditBuildingIds(ids)
        }
      } else if (targetType === 'community') {
        // 解析小区IDs（数字类型）
        const ids = targetIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
        if (ids.length > 0) {
          await this.handleEditCommunityIds(ids)
        }
      } else if (targetType === 'resident') {
        // 解析住户IDs（字符串类型，因为使用residentId）
        const ids = targetIds.split(',').map(id => id.trim()).filter(id => id !== '')
        if (ids.length > 0) {
          await this.handleEditResidentIds(ids)
        }
      }
    },

    /**
     * 处理编辑时的楼栋IDs回显
     */
    async handleEditBuildingIds(ids) {
      try {
        const communityId = getSelectedCommunityId()
        if (!communityId) return

        console.log('处理楼栋IDs回显:', ids)

        // 请求楼栋列表
        const response = await listCommunityBuilding({
          pageNum: 1,
          pageSize: 500,
          communityId: communityId
        })

        const buildings = response.data.data.list || []
        const idsNames = []

        // 循环匹配IDs
        buildings.forEach(building => {
          if (ids.includes(parseInt(building.id))) {
            // 匹配成功，保存名称
            const displayName = building.buildingName || building.buildingNumber || `楼栋${building.id}`
            idsNames.push(displayName)

            // 更新楼栋列表的checked状态
            building.checked = true
          } else {
            building.checked = false
          }
        })

        // 更新显示文本
        this.targetDisplayText = idsNames.join(', ')

        // 保存楼栋列表（用于弹窗显示）
        this.buildingList = buildings
        // 将数字ID转换为字符串ID，与楼栋树数据保持一致
        this.selectedBuildingIds = ids.map(id => id.toString())

        // 构建楼栋树数据
        this.buildingTreeData = buildings.map(building => ({
          id: building.id, // 保持原始ID类型（字符串），与API返回保持一致
          name: building.buildingName || building.buildingNumber || `楼栋${building.id}`,
          type: 'building',
          buildingNumber: building.buildingNumber,
          buildingName: building.buildingName
        }))

        console.log('编辑回显-楼栋树数据:', this.buildingTreeData.map(b => ({ id: b.id, name: b.name, type: typeof b.id })))
        console.log('编辑回显-选中的楼栋IDs:', this.selectedBuildingIds)

        // 更新选中的楼栋信息
        this.selectedBuildings = buildings.filter(building => building.checked)

        // 构建ID到名称的映射
        this.buildIdNameMapping()

      } catch (error) {
        console.error('处理楼栋IDs回显失败:', error)
        this.targetDisplayText = `楼栋${ids.join(',楼栋')}`
      }
    },



    /**
     * 处理编辑时的小区IDs回显
     */
    async handleEditCommunityIds(ids) {
      try {
        console.log('处理小区IDs回显:', ids)

        // 请求小区列表
        const response = await listCommunity({
          pageNum: 1,
          pageSize: 500
        })

        const communities = response.data.data.list || []
        const idsNames = []

        // 循环匹配IDs
        communities.forEach(community => {
          if (ids.includes(parseInt(community.id))) {
            // 匹配成功，保存名称
            idsNames.push(community.communityName)

            // 更新小区列表的checked状态
            community.checked = true
          } else {
            community.checked = false
          }
        })

        // 更新显示文本
        this.targetDisplayText = idsNames.join(', ')

        // 保存小区列表（用于弹窗显示）
        this.communityList = communities
        // 将数字ID转换为字符串ID，与小区数据保持一致
        this.selectedCommunityIds = ids.map(id => id.toString())

        console.log('编辑回显-小区数据:', {
          totalCommunities: communities.length,
          matchedIds: ids,
          selectedCommunityIds: this.selectedCommunityIds,
          displayText: this.targetDisplayText
        })

        // 更新选中的小区信息
        this.selectedCommunities = communities.filter(community => community.checked)

        // 构建ID到名称的映射
        this.buildCommunityIdNameMapping()

      } catch (error) {
        console.error('处理小区IDs回显失败:', error)
        this.targetDisplayText = `小区${ids.join(',小区')}`
      }
    },

    /**
     * 处理编辑时的住户IDs回显
     */
    async handleEditResidentIds(ids) {
      try {
        const communityId = getSelectedCommunityId()
        if (!communityId) return

        console.log('处理住户IDs回显:', ids)

        // 请求住户列表
        const response = await listCommunityResident({
          pageNum: 1,
          pageSize: 500,
          communityId: communityId
        })

        const residents = response.data.data.list || []
        const idsNames = []
        const selectedResidents = []

        // 循环匹配住户IDs（使用residentId字段）
        residents.forEach(resident => {
          if (ids.includes(resident.residentId)) {
            // 匹配成功，保存名称
            idsNames.push(resident.residentName)

            // 更新住户的checked状态
            selectedResidents.push({
              ...resident,
              checked: true
            })
          }
        })

        // 更新显示文本
        this.targetDisplayText = idsNames.join(', ')

        // 保存选中状态（保持字符串类型）
        this.selectedResidentIds = [...ids]
        this.selectedResidents = selectedResidents
        this.residentList = residents

        console.log('编辑回显-住户数据:', {
          totalResidents: residents.length,
          matchedIds: ids,
          selectedResidents: selectedResidents.length,
          displayText: this.targetDisplayText,
          residentListSample: residents.slice(0, 3).map(r => ({ id: r.id, residentId: r.residentId, name: r.residentName })),
          selectedResidentsSample: selectedResidents.map(r => ({ id: r.id, residentId: r.residentId, name: r.residentName }))
        })

        // 构建ID到名称的映射
        this.buildResidentIdNameMapping()

      } catch (error) {
        console.error('处理住户IDs回显失败:', error)
        this.targetDisplayText = `住户${ids.join(',住户')}`
      }
    },

    /**
     * 解析已有的目标IDs
     */
    parseExistingTargetIds() {
      const targetIds = this.noticeModel.targetIds
      console.log('解析已有目标IDs:', targetIds)

      if (!targetIds) {
        // 清空选择状态
        this.selectedBuildingIds = []
        this.selectedCommunities = []
        this.selectedCommunityIds = []
        this.selectedResidents = []
        this.selectedResidentIds = []
        return
      }

      const ids = targetIds.split(',').map(id => id.trim()).filter(id => id !== '')
      console.log('解析后的IDs:', ids)

      if (this.targetSelectorDialog.type === 'building') {
        this.selectedBuildingIds = [...ids]
        console.log('设置选中的楼栋IDs:', this.selectedBuildingIds)
      } else if (this.targetSelectorDialog.type === 'community') {
        this.selectedCommunityIds = [...ids]
        console.log('设置选中的小区IDs:', this.selectedCommunityIds)
      } else if (this.targetSelectorDialog.type === 'resident') {
        this.selectedResidentIds = [...ids]
        console.log('设置选中的住户IDs:', this.selectedResidentIds)
      }
    },

    /**
     * 加载楼栋列表（用于楼栋选择器）
     */
    async loadBuildingList() {
      // 如果已经有楼栋数据（编辑时预加载的），直接使用
      if (this.buildingList.length > 0 && this.buildingTreeData.length > 0) {
        this.buildingLoading = false

        // 设置树形控件的选中状态
        setTimeout(() => {
          if (this.$refs.buildingTreeRef && this.selectedBuildingIds.length > 0) {
            console.log('预加载分支-设置选中状态:', this.selectedBuildingIds)
            console.log('预加载分支-楼栋树数据IDs:', this.buildingTreeData.map(b => b.id))
            console.log('预加载分支-选中的IDs类型:', this.selectedBuildingIds.map(id => ({ id, type: typeof id })))

            this.$nextTick(() => {
              this.$refs.buildingTreeRef.setCheckedKeys(this.selectedBuildingIds)
            })
          }
        }, 100)
        return
      }

      const communityId = getSelectedCommunityId()
      if (!communityId) {
        this.$message.warning('请先选择小区')
        return
      }

      this.buildingLoading = true
      try {
        // 加载楼栋列表
        const buildingResponse = await listCommunityBuilding({
          pageNum: 1,
          pageSize: 500,
          communityId: communityId
        })
        const buildings = buildingResponse.data.data.list || []
        this.buildingList = buildings
        this.buildingTreeData = []

        // 构建楼栋扁平列表数据（不加载房间子节点）
        buildings.forEach(building => {
          const buildingNode = {
            id: building.id, // 保持原始ID类型（字符串），与API返回保持一致
            name: building.buildingName || building.buildingNumber || `楼栋${building.id}`,
            type: 'building',
            buildingNumber: building.buildingNumber,
            buildingName: building.buildingName
            // 不添加children属性，保持扁平结构
          }

          this.buildingTreeData.push(buildingNode)
        })

        console.log('楼栋树数据:', this.buildingTreeData.map(b => ({ id: b.id, name: b.name, type: typeof b.id })))

        // 构建ID到名称的映射
        this.buildIdNameMapping()

        // 更新选中的楼栋信息
        this.updateSelectedBuildings()

        // 设置树形控件的选中状态（用于编辑时回显）
        setTimeout(() => {
          if (this.$refs.buildingTreeRef && this.selectedBuildingIds.length > 0) {
            console.log('正常加载后设置选中状态:', this.selectedBuildingIds)
            console.log('楼栋树数据IDs:', this.buildingTreeData.map(b => b.id))
            console.log('选中的IDs类型:', this.selectedBuildingIds.map(id => ({ id, type: typeof id })))

            this.$nextTick(() => {
              this.$refs.buildingTreeRef.setCheckedKeys(this.selectedBuildingIds)
            })
          }
        }, 100)
      } catch (err) {
        this.$message.error(err.data?.errorMessage || '加载楼栋列表失败')
        this.buildingList = []
        this.buildingTreeData = []
      } finally {
        this.buildingLoading = false
      }
    },



    /**
     * 加载小区列表（用于小区选择器）
     */
    async loadCommunityList() {
      // 如果已经有小区数据（编辑时预加载的），直接使用
      if (this.communityList.length > 0) {
        this.communityLoading = false

        console.log('预加载分支-小区数据:', {
          totalCommunities: this.communityList.length,
          selectedCommunityIds: this.selectedCommunityIds,
          selectedCommunities: this.selectedCommunities.length
        })

        // 设置表格的选中状态
        setTimeout(() => {
          if (this.$refs.communityTableRef && this.selectedCommunityIds.length > 0) {
            console.log('预加载分支-设置小区选中状态:', this.selectedCommunityIds)

            // 使用 communityList 中的数据对象来设置选中状态
            this.selectedCommunityIds.forEach(communityId => {
              const community = this.communityList.find(c => c.id === communityId)
              if (community) {
                this.$refs.communityTableRef.toggleRowSelection(community, true)
              }
            })
          }
        }, 100)
        return Promise.resolve()
      }

      this.communityLoading = true
      try {
        const response = await listCommunity({
          pageNum: 1,
          pageSize: 500
        })

        this.communityList = response.data.data.list || []

        // 构建ID到名称的映射
        this.buildCommunityIdNameMapping()

        // 更新选中的小区信息
        this.updateSelectedCommunities()

        // 设置表格的选中状态（用于编辑时回显）
        setTimeout(() => {
          if (this.$refs.communityTableRef && this.selectedCommunityIds.length > 0) {
            console.log('正常加载后设置小区选中状态:', this.selectedCommunityIds)

            // 使用 communityList 中的数据对象来设置选中状态
            this.selectedCommunityIds.forEach(communityId => {
              const community = this.communityList.find(c => c.id === communityId)
              if (community) {
                console.log('设置小区选中:', { communityId, name: community.communityName })
                this.$refs.communityTableRef.toggleRowSelection(community, true)
              } else {
                console.warn('未找到小区数据:', communityId)
              }
            })
          }
        }, 100)
      } catch (err) {
        this.$message.error(err.data?.errorMessage || '加载小区列表失败')
        this.communityList = []
      } finally {
        this.communityLoading = false
      }
    },

    /**
     * 加载住户列表（用于住户选择器）
     */
    async loadResidentList() {
      // 如果已经有住户数据（编辑时预加载的），直接使用
      if (this.residentList.length > 0) {
        this.residentLoading = false
        this.filteredResidentList = [...this.residentList]

        console.log('预加载分支-住户数据:', {
          totalResidents: this.residentList.length,
          selectedResidentIds: this.selectedResidentIds,
          selectedResidents: this.selectedResidents.length
        })

        // 设置表格的选中状态
        setTimeout(() => {
          if (this.$refs.residentTableRef && this.selectedResidentIds.length > 0) {
            console.log('预加载分支-设置住户选中状态:', this.selectedResidentIds)
            console.log('预加载分支-住户数据:', this.selectedResidents.map(r => ({ residentId: r.residentId, name: r.residentName })))

            // 使用 residentList 中的数据对象来设置选中状态
            this.selectedResidentIds.forEach(residentId => {
              const resident = this.residentList.find(r => r.residentId === residentId)
              if (resident) {
                this.$refs.residentTableRef.toggleRowSelection(resident, true)
              }
            })
          }
        }, 100)
        return Promise.resolve()
      }

      const communityId = getSelectedCommunityId()
      if (!communityId) {
        this.$message.warning('请先选择小区')
        return
      }

      this.residentLoading = true
      try {
        const response = await listCommunityResident({
          pageNum: 1,
          pageSize: 500,
          communityId: communityId,
          residentName: this.residentSearchName || undefined
        })

        this.residentList = response.data.data.list || []
        this.filteredResidentList = [...this.residentList]

        // 构建ID到名称的映射
        this.buildResidentIdNameMapping()

        // 更新选中的住户信息
        this.updateSelectedResidents()

        // 设置表格的选中状态（用于编辑时回显）
        setTimeout(() => {
          if (this.$refs.residentTableRef && this.selectedResidentIds.length > 0) {
            console.log('正常加载后设置住户选中状态:', this.selectedResidentIds)
            console.log('正常加载后-住户列表数据:', this.residentList.map(r => ({ residentId: r.residentId, name: r.residentName })))

            // 先更新选中的住户信息，然后设置表格选中状态
            this.updateSelectedResidents()

            // 使用 residentList 中的数据对象来设置选中状态
            this.selectedResidentIds.forEach(residentId => {
              const resident = this.residentList.find(r => r.residentId === residentId)
              if (resident) {
                console.log('设置住户选中:', { residentId, name: resident.residentName })
                this.$refs.residentTableRef.toggleRowSelection(resident, true)
              } else {
                console.warn('未找到住户数据:', residentId)
              }
            })
          }
        }, 100)
      } catch (err) {
        this.$message.error(err.data?.errorMessage || '加载住户列表失败')
        this.residentList = []
        this.filteredResidentList = []
      } finally {
        this.residentLoading = false
      }
    },

    /**
     * 住户搜索
     */
    onResidentSearch() {
      if (!this.residentSearchName.trim()) {
        this.filteredResidentList = [...this.residentList]
      } else {
        this.filteredResidentList = this.residentList.filter(resident =>
          resident.residentName.includes(this.residentSearchName.trim())
        )
      }
    },

    /**
     * 住户选择变化事件
     */
    onResidentSelectionChange(selectedRows) {
      this.selectedResidents = selectedRows
      this.selectedResidentIds = selectedRows.map(resident => resident.residentId)
      console.log('住户选择变化:', {
        selectedRows: selectedRows.length,
        selectedResidentIds: this.selectedResidentIds
      })
    },

    /**
     * 小区选择变化事件
     */
    onCommunitySelectionChange(selectedRows) {
      this.selectedCommunities = selectedRows
      this.selectedCommunityIds = selectedRows.map(community => community.id)
      console.log('小区选择变化:', {
        selectedRows: selectedRows.length,
        selectedCommunityIds: this.selectedCommunityIds
      })
    },

    /**
     * 清空小区选择
     */
    clearCommunitySelection() {
      this.selectedCommunities = []
      this.selectedCommunityIds = []
      // 清空表格的选中状态
      if (this.$refs.communityTableRef) {
        this.$refs.communityTableRef.clearSelection()
      }
    },

    /**
     * 清空住户选择
     */
    clearResidentSelection() {
      this.selectedResidents = []
      this.selectedResidentIds = []
      this.residentSearchName = ''
      // 清空表格的选中状态
      if (this.$refs.residentTableRef) {
        this.$refs.residentTableRef.clearSelection()
      }
    },

    /**
     * 构建楼栋ID到名称的映射
     */
    buildIdNameMapping() {
      this.targetIdNameMap.clear()

      this.buildingTreeData.forEach(buildingNode => {
        // 添加楼栋映射，保持原始ID类型
        this.targetIdNameMap.set(buildingNode.id.toString(), buildingNode.name)
      })

      console.log('楼栋ID到名称映射:', Array.from(this.targetIdNameMap.entries()))
    },



    /**
     * 构建小区ID到名称的映射
     */
    buildCommunityIdNameMapping() {
      this.targetIdNameMap.clear()

      this.communityList.forEach(community => {
        this.targetIdNameMap.set(community.id.toString(), community.communityName)
      })

      console.log('小区ID到名称映射:', Array.from(this.targetIdNameMap.entries()))
    },

    /**
     * 构建住户ID到名称的映射
     */
    buildResidentIdNameMapping() {
      this.targetIdNameMap.clear()

      this.residentList.forEach(resident => {
        this.targetIdNameMap.set(resident.residentId.toString(), resident.residentName)
      })

      console.log('住户ID到名称映射:', Array.from(this.targetIdNameMap.entries()))
    },

    /**
     * 更新选中的小区信息
     */
    updateSelectedCommunities() {
      this.selectedCommunities = this.communityList.filter(community =>
        this.selectedCommunityIds.includes(community.id)
      )
      console.log('更新选中小区信息:', {
        selectedCommunityIds: this.selectedCommunityIds,
        communityListIds: this.communityList.map(c => ({ id: c.id, name: c.communityName })),
        selectedCommunities: this.selectedCommunities.map(c => c.communityName)
      })
    },

    /**
     * 更新选中的住户信息
     */
    updateSelectedResidents() {
      this.selectedResidents = this.residentList.filter(resident =>
        this.selectedResidentIds.includes(resident.residentId)
      )
      console.log('更新选中住户信息:', {
        selectedResidentIds: this.selectedResidentIds,
        residentListIds: this.residentList.map(r => ({ id: r.id, residentId: r.residentId, name: r.residentName })),
        selectedResidents: this.selectedResidents.map(r => r.residentName)
      })
    },

    /**
     * 更新选中的楼栋信息
     */
    updateSelectedBuildings() {
      this.selectedBuildings = this.buildingList.filter(building =>
        this.selectedBuildingIds.includes(building.id)
      )
      console.log('更新选中楼栋信息:', {
        selectedBuildingIds: this.selectedBuildingIds,
        buildingListIds: this.buildingList.map(b => ({ id: b.id, type: typeof b.id })),
        selectedBuildings: this.selectedBuildings.map(b => b.buildingName || b.buildingNumber)
      })
    },



    /**
     * 楼栋选择变化事件（原有方法，保留兼容性）
     */
    onBuildingSelectionChange() {
      this.updateSelectedBuildings()
    },

    /**
     * 楼栋树选择变化事件
     */
    onBuildingTreeCheck(_checkedData, checkedInfo) {
      // 只保留楼栋类型的选中项
      const checkedBuildingIds = checkedInfo.checkedKeys.filter(key => {
        // 查找对应的节点数据
        for (const buildingNode of this.buildingTreeData) {
          if (buildingNode.id === key && buildingNode.type === 'building') {
            return true
          }
        }
        return false
      })

      console.log('楼栋树选择变化:', {
        checkedKeys: checkedInfo.checkedKeys,
        filteredBuildingIds: checkedBuildingIds
      })

      this.selectedBuildingIds = checkedBuildingIds
      this.updateSelectedBuildings()
    },



    /**
     * 清空楼栋选择
     */
    clearBuildingSelection() {
      this.selectedBuildingIds = []
      this.selectedBuildings = []
      // 清空树形控件的选中状态
      if (this.$refs.buildingTreeRef) {
        this.$refs.buildingTreeRef.setCheckedKeys([])
      }
    },



    /**
     * 确认目标选择
     */
    confirmTargetSelection() {
      let selectedIds = []
      let selectedNames = []

      if (this.targetSelectorDialog.type === 'building') {
        selectedIds = this.selectedBuildingIds
        selectedNames = this.selectedBuildings.map(building => building.buildingName || building.buildingNumber || `楼栋${building.id}`)

        // 更新ID到名称的映射
        this.selectedBuildings.forEach(building => {
          const displayName = building.buildingName || building.buildingNumber || `楼栋${building.id}`
          this.targetIdNameMap.set(building.id.toString(), displayName)
        })

        this.$message.success(`已选择 ${selectedIds.length} 个楼栋`)
      } else if (this.targetSelectorDialog.type === 'community') {
        selectedIds = this.selectedCommunityIds
        selectedNames = this.selectedCommunities.map(community => community.communityName)

        // 更新ID到名称的映射
        this.selectedCommunities.forEach(community => {
          this.targetIdNameMap.set(community.id.toString(), community.communityName)
        })

        this.$message.success(`已选择 ${selectedIds.length} 个小区`)
      } else if (this.targetSelectorDialog.type === 'resident') {
        selectedIds = this.selectedResidentIds
        selectedNames = this.selectedResidents.map(resident => resident.residentName)

        // 更新ID到名称的映射
        this.selectedResidents.forEach(resident => {
          this.targetIdNameMap.set(resident.residentId.toString(), resident.residentName)
        })

        this.$message.success(`已选择 ${selectedIds.length} 个住户`)
      }
      // 更新目标IDs字段（存储ID）
      this.noticeModel.targetIds = selectedIds.join(',')

      // 更新显示文本（显示名称）
      this.targetDisplayText = selectedNames.join(', ')

      // 关闭选择器
      this.targetSelectorDialog.show = false
    }
  },
  async mounted() {
    // 初始化字典数据
    await this.initDictData()

    // 初始化ID转换器
    this.initTargetIdConverter()

    mitt.on('openNoticeEdit', async (data) => {
      this.resetForm()

      // 设置表单数据
      this.noticeModel = {
        ...data,
        sort: Number(data.sort) || 0,
        top: Boolean(data.top)
      }

      // 处理图片显示
      if (data.imageUrl) {
        this.imageShow = true
        this.imageUrl = this.imgServer + data.imageUrl
      }

      // 设置富文本编辑器内容
      setTimeout(() => {
        if (data.content && this.$refs.editorOne) {
          this.$refs.editorOne.setHtml(data.content)
        }
      }, 100)

      // 编辑时处理目标IDs回显
      if (data.targetIds && data.targetType) {
        console.log('编辑通知，开始处理目标IDs回显:', { targetIds: data.targetIds, targetType: data.targetType })
        await this.handleEditTargetIds(data.targetIds, data.targetType)
      }

      this.dialog.show = true
      this.dialog.title = '编辑通知信息'
    })

    mitt.on('openNoticeAdd', () => {
      this.resetForm()

      // 确保富文本编辑器被清空
      setTimeout(() => {
        if (this.$refs.editorOne) {
          this.$refs.editorOne.setHtml('')
        }
      }, 100)

      this.dialog.show = true
      this.dialog.title = '新增通知信息'
    })
  },

  beforeUnmount() {
    mitt.off('openNoticeEdit')
    mitt.off('openNoticeAdd')
  }
}
</script>

<style scoped>
.notice-edit-dialog>>>.el-dialog__body {
  padding-top: 10px;
  padding-bottom: 0;
}

.notice-edit-form {
  padding: 0 10px;
}

.avatar-uploader {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-height: 60px;
}

.avatar-uploader .avatar {
  width: 120px;
  height: 80px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.avatar-uploader-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 80px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafbfc;
  color: #bbb;
  cursor: pointer;
  transition: border-color 0.2s;
}

.avatar-uploader-placeholder:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 32px;
}

.avatar-uploader-text {
  font-size: 12px;
  margin-top: 4px;
  color: #888;
}

.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}

.avatar-preview-wrapper {
  position: relative;
  display: inline-block;
}

.avatar-preview-icon {
  position: absolute;
  right: 36px;
  top: 8px;
  font-size: 20px;
  color: #409EFF;
  background: #fff;
  border-radius: 50%;
  padding: 2px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 2;
}

.avatar-delete-icon {
  position: absolute;
  right: 8px;
  top: 8px;
  font-size: 20px;
  color: #F56C6C;
  background: #fff;
  border-radius: 50%;
  padding: 2px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 2;
  display: none;
}

.avatar-preview-wrapper:hover .avatar-delete-icon {
  display: block;
}

.avatar-preview-wrapper:hover .avatar-preview-icon {
  color: #66b1ff;
}

.avatar-preview-wrapper .avatar {
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.avatar-preview-wrapper .avatar:hover {
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
}

/* 目标IDs选择器样式 */
.target-ids-container {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  width: 100%;
}

.target-ids-input {
  flex: 1;
  min-width: 0;
}

.target-ids-input :deep(.el-input__wrapper) {
  min-height: 32px;
  max-height: 120px;
  overflow-y: auto;
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.4;
}

.target-ids-input :deep(.el-input__inner) {
  min-height: 30px;
  max-height: 118px;
  overflow-y: auto;
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.4;
  resize: none;
}

.select-btn {
  min-width: 80px;
  flex-shrink: 0;
  align-self: flex-start;
}

/* 目标选择器弹窗样式 */
.target-selector-dialog {
  border-radius: 8px;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.selected-count {
  font-weight: 500;
  color: #409eff;
}

/* 楼栋选择器样式 */
.building-selector {
  max-height: 500px;
  overflow-y: auto;
}

.building-tree-container {
  min-height: 300px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
}

.building-tree {
  background-color: transparent;
}

/* 保留原有的网格样式（如果需要回退到卡片模式） */
.building-list {
  min-height: 200px;
}

.building-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  padding: 16px;
}

.building-item {
  margin: 0;
}

.building-card {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fff;
  transition: all 0.3s;
  cursor: pointer;
}

.building-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.building-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.building-info {
  font-size: 12px;
  color: #909399;
}



/* 小区选择器样式 */
.community-selector {
  max-height: 500px;
  overflow-y: auto;
}

.community-list-container {
  min-height: 300px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
}

/* 住户选择器样式 */
.resident-selector {
  max-height: 500px;
  overflow-y: auto;
}

.resident-list-container {
  min-height: 300px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .target-ids-container {
    flex-direction: column;
    gap: 12px;
  }

  .target-ids-input {
    width: 100%;
  }

  .select-btn {
    width: 100%;
  }

  .building-grid {
    grid-template-columns: 1fr;
    padding: 12px;
  }

  .target-selector-dialog {
    width: 95% !important;
  }
}
</style>