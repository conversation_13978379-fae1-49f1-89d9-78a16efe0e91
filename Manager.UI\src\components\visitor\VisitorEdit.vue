<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="edit-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="访客姓名" prop="visitorName">
            <el-input v-model="formData.visitorName" placeholder="请输入访客姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="车牌号" prop="vehicleNumber">
            <el-input v-model="formData.vehicleNumber" placeholder="请输入车牌号（可选）" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="访问目的" prop="purpose">
            <el-input v-model="formData.purpose" placeholder="请输入访问目的" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="来访时间" prop="visitTime">
            <el-date-picker
              v-model="formData.visitTime"
              type="datetime"
              placeholder="选择来访时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="停留时长" prop="stayDuration">
            <el-select v-model="formData.stayDuration" placeholder="选择停留时长" style="width: 100%">
              <el-option label="1小时" :value="1" />
              <el-option label="2小时" :value="2" />
              <el-option label="4小时" :value="4" />
              <el-option label="8小时" :value="8" />
              <el-option label="12小时" :value="12" />
              <el-option label="24小时" :value="24" />
              <el-option label="48小时" :value="48" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="被访住户" prop="residentId">
            <el-input
              v-model="residentDisplay"
              placeholder="点击选择住户"
              readonly
              @click="selectResident"
            >
              <template #append>
                <el-button @click="selectResident">选择</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否常用">
            <el-switch v-model="formData.isUsual" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注">
        <el-input
          v-model="formData.note"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ isEdit ? '更新' : '添加' }}
      </el-button>
    </template>

    <!-- 住户选择弹窗 -->
    <resident-select-dialog
      v-model="residentSelectVisible"
      @confirm="handleResidentSelect"
    />
  </el-dialog>
</template>

<script>
import { addVisitor, editVisitor } from '@/api/visitor'
import { getSelectedCommunityId } from '@/store/modules/options'
import ResidentSelectDialog from '@/components/common/ResidentSelectDialog.vue'

export default {
  name: 'VisitorEdit',

  components: {
    ResidentSelectDialog
  },

  emits: ['search'],

  data() {
    return {
      visible: false,
      isEdit: false,
      submitting: false,
      residentSelectVisible: false,
      
      // 表单数据
      formData: {
        id: null,
        visitorName: '',
        phone: '',
        vehicleNumber: '',
        purpose: '',
        visitTime: '',
        stayDuration: 2,
        timeUnit: 'hours',
        residentId: null,
        communityId: null,
        note: '',
        isUsual: false
      },

      // 住户显示信息
      residentDisplay: '',

      // 表单验证规则
      rules: {
        visitorName: [
          { required: true, message: '请输入访客姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '访客姓名长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        purpose: [
          { required: true, message: '请输入访问目的', trigger: 'blur' },
          { max: 100, message: '访问目的不能超过100个字符', trigger: 'blur' }
        ],
        visitTime: [
          { required: true, message: '请选择来访时间', trigger: 'change' }
        ],
        stayDuration: [
          { required: true, message: '请选择停留时长', trigger: 'change' }
        ],
        residentId: [
          { required: true, message: '请选择被访住户', trigger: 'change' }
        ]
      }
    }
  },

  computed: {
    title() {
      return this.isEdit ? '编辑访客' : '添加访客'
    }
  },

  methods: {
    /**
     * 打开弹窗
     */
    open(data = null) {
      this.visible = true
      this.isEdit = !!data

      if (data) {
        // 编辑模式
        this.formData = {
          id: data.id,
          visitorName: data.visitorName || '',
          phone: data.phone || '',
          vehicleNumber: data.vehicleNumber || '',
          purpose: data.purpose || '',
          visitTime: data.visitTime || '',
          stayDuration: data.stayDuration || 2,
          timeUnit: data.timeUnit || 'hours',
          residentId: data.residentId || null,
          communityId: data.communityId || getSelectedCommunityId(),
          note: data.note || '',
          isUsual: data.isUsual || false
        }
        
        // 设置住户显示信息
        if (data.residentName) {
          this.residentDisplay = `${data.residentName} (${data.residentPhone || ''})`
        }
      } else {
        // 新增模式
        this.resetForm()
        this.formData.communityId = getSelectedCommunityId()
      }
    },

    /**
     * 关闭弹窗
     */
    handleClose() {
      this.visible = false
      this.resetForm()
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.formData = {
        id: null,
        visitorName: '',
        phone: '',
        vehicleNumber: '',
        purpose: '',
        visitTime: '',
        stayDuration: 2,
        timeUnit: 'hours',
        residentId: null,
        communityId: null,
        note: '',
        isUsual: false
      }
      this.residentDisplay = ''
      
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate()
        }
      })
    },

    /**
     * 选择住户
     */
    selectResident() {
      this.residentSelectVisible = true
    },

    /**
     * 处理住户选择
     */
    handleResidentSelect(resident) {
      this.formData.residentId = resident.id
      this.residentDisplay = `${resident.name} (${resident.phone || ''})`
    },

    /**
     * 提交表单
     */
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (!valid) {
          return false
        }

        this.submitting = true

        // 构建提交数据
        const submitData = { ...this.formData }
        
        // 移除显示用的字段
        delete submitData.id

        const apiCall = this.isEdit 
          ? editVisitor({ ...submitData, id: this.formData.id })
          : addVisitor(submitData)

        apiCall.then(() => {
          this.$message.success(this.isEdit ? '更新成功' : '添加成功')
          this.handleClose()
          this.$emit('search')
        }).catch(err => {
          console.error('提交失败:', err)
          this.$message.error(err.data?.errorMessage || '操作失败')
        }).finally(() => {
          this.submitting = false
        })
      })
    }
  }
}
</script>

<style scoped>
.edit-form {
  padding: 0 20px;
}

.edit-form .el-form-item {
  margin-bottom: 20px;
}

.edit-form .el-input,
.edit-form .el-select,
.edit-form .el-date-picker {
  width: 100%;
}

/* 暗色主题适配 */
.dark-theme .edit-form {
  color: var(--text-primary);
}
</style>
