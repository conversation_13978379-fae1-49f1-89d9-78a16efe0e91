/**
 * Token 死循环监控和诊断工具
 * 用于检测和防止 token 相关的死循环问题
 */

class TokenMonitor {
    constructor() {
        this.events = [];
        this.maxEvents = 100; // 最多保存100个事件
        this.warningThreshold = 10; // 10秒内超过此次数触发警告
        this.errorThreshold = 20; // 10秒内超过此次数触发错误
        this.timeWindow = 10000; // 时间窗口：10秒
        
        // 监控的事件类型
        this.eventTypes = {
            TOKEN_CHECK: 'token_check',
            TOKEN_REFRESH: 'token_refresh',
            TOKEN_UPDATE: 'token_update',
            ROUTE_GUARD: 'route_guard',
            RELOGIN: 'relogin',
            API_REQUEST: 'api_request'
        };
        
        this.isMonitoring = true;
        this.setupConsoleOverride();
    }
    
    /**
     * 记录事件
     */
    logEvent(type, details = {}) {
        if (!this.isMonitoring) return;
        
        const event = {
            type,
            timestamp: Date.now(),
            details,
            stack: new Error().stack
        };
        
        this.events.push(event);
        
        // 保持事件数量在限制内
        if (this.events.length > this.maxEvents) {
            this.events.shift();
        }
        
        // 检查是否有异常模式
        this.checkForAnomalies(type);
    }
    
    /**
     * 检查异常模式
     */
    checkForAnomalies(currentType) {
        const now = Date.now();
        const recentEvents = this.events.filter(
            event => now - event.timestamp < this.timeWindow
        );
        
        // 检查同类型事件频率
        const sameTypeEvents = recentEvents.filter(event => event.type === currentType);
        
        if (sameTypeEvents.length >= this.errorThreshold) {
            this.handleCriticalLoop(currentType, sameTypeEvents);
        } else if (sameTypeEvents.length >= this.warningThreshold) {
            this.handleWarning(currentType, sameTypeEvents);
        }
        
        // 检查特定的危险模式
        this.checkDangerousPatterns(recentEvents);
    }
    
    /**
     * 处理严重循环
     */
    handleCriticalLoop(type, events) {
        console.error('🚨🚨🚨 检测到严重的死循环！', {
            type,
            count: events.length,
            timeWindow: this.timeWindow / 1000 + '秒',
            events: events.slice(-5) // 显示最近5个事件
        });
        
        // 生成诊断报告
        this.generateDiagnosticReport();
        
        // 尝试自动修复
        this.attemptAutoFix(type);
    }
    
    /**
     * 处理警告
     */
    handleWarning(type, events) {
        console.warn('⚠️ 检测到可能的循环模式', {
            type,
            count: events.length,
            timeWindow: this.timeWindow / 1000 + '秒'
        });
    }
    
    /**
     * 检查危险模式
     */
    checkDangerousPatterns(recentEvents) {
        // 模式1：token检查 -> 刷新 -> 检查 -> 刷新 循环
        const tokenPattern = this.findPattern(recentEvents, [
            this.eventTypes.TOKEN_CHECK,
            this.eventTypes.TOKEN_REFRESH,
            this.eventTypes.TOKEN_CHECK,
            this.eventTypes.TOKEN_REFRESH
        ]);
        
        if (tokenPattern.length >= 3) {
            console.error('🔄 检测到 Token 刷新死循环模式！');
            this.generateDiagnosticReport();
        }
        
        // 模式2：路由守卫 -> 加载用户信息 -> 路由守卫 循环
        const routePattern = this.findPattern(recentEvents, [
            this.eventTypes.ROUTE_GUARD,
            this.eventTypes.API_REQUEST,
            this.eventTypes.ROUTE_GUARD
        ]);
        
        if (routePattern.length >= 4) {
            console.error('🛣️ 检测到路由守卫死循环模式！');
            this.generateDiagnosticReport();
        }
    }
    
    /**
     * 查找特定模式
     */
    findPattern(events, pattern) {
        const matches = [];
        for (let i = 0; i <= events.length - pattern.length; i++) {
            let match = true;
            for (let j = 0; j < pattern.length; j++) {
                if (events[i + j].type !== pattern[j]) {
                    match = false;
                    break;
                }
            }
            if (match) {
                matches.push(events.slice(i, i + pattern.length));
            }
        }
        return matches;
    }
    
    /**
     * 生成诊断报告
     */
    generateDiagnosticReport() {
        const report = {
            timestamp: new Date().toISOString(),
            totalEvents: this.events.length,
            recentEvents: this.events.slice(-20),
            eventCounts: this.getEventCounts(),
            suspiciousPatterns: this.findSuspiciousPatterns(),
            recommendations: this.generateRecommendations()
        };
        
        console.group('📊 Token 死循环诊断报告');
        console.log('报告时间:', report.timestamp);
        console.log('事件统计:', report.eventCounts);
        console.log('可疑模式:', report.suspiciousPatterns);
        console.log('建议措施:', report.recommendations);
        console.log('最近事件:', report.recentEvents);
        console.groupEnd();
        
        // 保存到 sessionStorage 供调试使用
        try {
            sessionStorage.setItem('tokenMonitorReport', JSON.stringify(report));
        } catch (e) {
            console.warn('无法保存诊断报告到 sessionStorage');
        }
        
        return report;
    }
    
    /**
     * 获取事件计数
     */
    getEventCounts() {
        const counts = {};
        this.events.forEach(event => {
            counts[event.type] = (counts[event.type] || 0) + 1;
        });
        return counts;
    }
    
    /**
     * 查找可疑模式
     */
    findSuspiciousPatterns() {
        const patterns = [];
        const now = Date.now();
        const recentEvents = this.events.filter(
            event => now - event.timestamp < this.timeWindow
        );
        
        // 检查高频事件
        const eventCounts = {};
        recentEvents.forEach(event => {
            eventCounts[event.type] = (eventCounts[event.type] || 0) + 1;
        });
        
        Object.entries(eventCounts).forEach(([type, count]) => {
            if (count >= this.warningThreshold) {
                patterns.push({
                    type: 'high_frequency',
                    eventType: type,
                    count,
                    severity: count >= this.errorThreshold ? 'critical' : 'warning'
                });
            }
        });
        
        return patterns;
    }
    
    /**
     * 生成建议
     */
    generateRecommendations() {
        const recommendations = [];
        const eventCounts = this.getEventCounts();
        
        if (eventCounts[this.eventTypes.TOKEN_REFRESH] > 5) {
            recommendations.push('检查 refresh_token 是否有效，可能需要重新登录');
        }
        
        if (eventCounts[this.eventTypes.ROUTE_GUARD] > 10) {
            recommendations.push('检查路由守卫逻辑，可能存在无限重定向');
        }
        
        if (eventCounts[this.eventTypes.TOKEN_CHECK] > 20) {
            recommendations.push('检查 token 检查逻辑，可能存在重复调用');
        }
        
        return recommendations;
    }
    
    /**
     * 尝试自动修复
     */
    attemptAutoFix(type) {
        console.log('🔧 尝试自动修复死循环...');
        
        switch (type) {
            case this.eventTypes.TOKEN_REFRESH:
                // 清除 token 并跳转登录页
                console.log('清除异常 token 并跳转登录页');
                window.$local?.removeAll();
                window.location.href = '/login';
                break;
                
            case this.eventTypes.ROUTE_GUARD:
                // 强制跳转到首页
                console.log('强制跳转到首页');
                window.location.href = '/home';
                break;
                
            default:
                console.log('暂无针对此类型的自动修复方案');
        }
    }
    
    /**
     * 设置控制台重写（可选，用于更好的调试）
     */
    setupConsoleOverride() {
        // 可以在这里重写 console 方法来自动捕获相关日志
        // 但要小心不要影响正常的日志输出
    }
    
    /**
     * 获取监控状态
     */
    getStatus() {
        return {
            isMonitoring: this.isMonitoring,
            totalEvents: this.events.length,
            recentEventCount: this.events.filter(
                event => Date.now() - event.timestamp < this.timeWindow
            ).length
        };
    }
    
    /**
     * 清除事件历史
     */
    clearHistory() {
        this.events = [];
        console.log('✅ Token 监控历史已清除');
    }
    
    /**
     * 停止监控
     */
    stop() {
        this.isMonitoring = false;
        console.log('⏹️ Token 监控已停止');
    }
    
    /**
     * 开始监控
     */
    start() {
        this.isMonitoring = true;
        console.log('▶️ Token 监控已开始');
    }
}

// 创建全局实例
const tokenMonitor = new TokenMonitor();

// 导出监控器和事件类型
export { tokenMonitor, TokenMonitor };
export const { eventTypes } = tokenMonitor;
