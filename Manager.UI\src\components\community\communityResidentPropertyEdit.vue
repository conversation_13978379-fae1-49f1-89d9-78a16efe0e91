<template>
  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="form"
      :model="propertyModel"
      :rules="rules"
      label-width="100px"
      class="property-edit-form"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="住户信息">
            <el-input
              :value="`${residentInfo.residentName} (${residentInfo.phone})`"
              disabled
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="楼栋" prop="buildingId">
            <el-select
              v-model="propertyModel.buildingId"
              placeholder="请选择楼栋"
              filterable
              clearable
              style="width: 100%"
              :loading="buildingLoading"
              @change="onBuildingChange"
            >
              <el-option
                v-for="building in buildingList"
                :key="building.id"
                :label="building.buildingNumber"
                :value="building.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="房间" prop="roomId">
            <el-select
              v-model="propertyModel.roomId"
              placeholder="请选择房间"
              filterable
              clearable
              style="width: 100%"
              :loading="roomLoading"
              @change="onRoomChange"
            >
              <el-option
                v-for="room in roomList"
                :key="room.id"
                :label="`${room.unitNumber?room.unitNumber:''}${room.roomNumber}`"
                :value="room.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="完整地址">
            <el-input
              v-model="propertyModel.address"
              placeholder="选择房间后自动生成"
              disabled
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="住户类型" prop="residentType">
            <el-select
              v-model="propertyModel.residentType"
              placeholder="请选择住户类型"
              style="width: 100%"
            >
              <el-option
                v-for="type in RESIDENT_TYPES"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
    <!-- <el-col :span="12">
          <el-form-item label="房间户型" prop="roomType">
            <el-select
              v-model="propertyModel.roomType"
              placeholder="请选择住户类型"
              style="width: 100%"
            >
              <el-option
                v-for="type in roomTypeList"
                :key="type.nameEn"
                :label="type.nameCn"
                :value="type.nameEn"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="propertyModel.status"
              placeholder="请选择状态"
              style="width: 100%"
            >
              <el-option
                v-for="status in residentStatusList"
                :key="status.nameEn"
                :label="status.nameCn"
                :value="status.nameEn"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="propertyModel.note"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submitting">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  addResidentProperty,
  editResidentProperty
} from '@/api/community/communityResidentProperty'
import { RESIDENT_TYPES } from '@/api/community/communityResident'
import { listCommunityBuilding } from '@/api/community/communityBuilding'
import { listCommunityRoom } from '@/api/community/communityRoom'
import { getSelectedCommunityId } from '@/store/modules/options'
import { listDictByNameEn } from '@/api/system/dict'

export default {
  name: 'CommunityResidentPropertyEdit',
  emits: ['refresh'],
  props: ['roomTypeList'],
  data() {
    return {
      dialog: {
        show: false,
        title: ''
      },
      residentInfo: {
        residentId: null,
        residentName: '',
        phone: ''
      },
      propertyModel: {
        id: null,
        residentId: null,
        communityId: null,
        buildingId: null,
        roomId: null,
        address: '',
        residentType: 'owner',
        status: 'normal',
        note: '',
        roomType: '',
      },
      rules: {
        buildingId: [
          { required: true, message: '请选择楼栋', trigger: 'change' }
        ],
        roomId: [
          { required: true, message: '请选择房间', trigger: 'change' }
        ],
        residentType: [
          { required: true, message: '请选择住户类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      buildingList: [],
      buildingLoading: false,
      roomList: [],
      roomLoading: false,
      submitting: false,
      RESIDENT_TYPES,
      residentStatusList: []
    }
  },

  methods: {
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        const res = await listDictByNameEn('resident_status')
        this.residentStatusList = res.data.data || []
      } catch (err) {
        console.error('加载住户状态字典失败:', err)
        // 如果字典加载失败，使用默认状态
        this.residentStatusList = [
          { nameEn: 'normal', nameCn: '正常' },
          { nameEn: 'pending', nameCn: '待审核' },
          { nameEn: 'move_out', nameCn: '搬出' }
        ]
      }
    },

    /**
     * 打开对话框
     */
    open(residentInfo, property = null) {
      this.residentInfo = { ...residentInfo }

      // 初始化字典数据
      this.initDictData()

      if (property) {
        this.propertyModel = { ...property }
        this.dialog.title = '编辑房产信息'
        // 编辑时需要加载对应的房间列表
        if (property.buildingId) {
          this.loadRoomList(property.buildingId)
        }
      } else {
        this.resetForm()
        this.propertyModel.residentId = residentInfo.id
        this.propertyModel.communityId = getSelectedCommunityId()
        this.dialog.title = '新增房产信息'
      }

      this.dialog.show = true
      this.loadBuildingList()
    },

    /**
     * 加载楼栋列表
     */
    loadBuildingList() {
      this.buildingLoading = true
      const params = {
        pageNum: 1,
        pageSize: 500,
        communityId: getSelectedCommunityId()
      }

      listCommunityBuilding(params)
        .then(res => {
          this.buildingList = res.data.data.list || []
        })
        .catch(err => {
          console.error('加载楼栋列表失败:', err)
          this.$message.error('加载楼栋列表失败')
          this.buildingList = []
        })
        .finally(() => {
          this.buildingLoading = false
        })
    },

    /**
     * 加载房间列表
     */
    loadRoomList(buildingId) {
      if (!buildingId) {
        this.roomList = []
        return
      }

      this.roomLoading = true
      const params = {
        pageNum: 1,
        pageSize: 500,
        buildingId: buildingId,
        communityId: getSelectedCommunityId()
      }

      listCommunityRoom(params)
        .then(res => {
          this.roomList = res.data.data.list || []
        })
        .catch(err => {
          console.error('加载房间列表失败:', err)
          this.$message.error('加载房间列表失败')
          this.roomList = []
        })
        .finally(() => {
          this.roomLoading = false
        })
    },

    /**
     * 楼栋变化处理
     */
    onBuildingChange(buildingId) {
      this.propertyModel.roomId = null
      this.propertyModel.address = ''
      this.roomList = []

      if (buildingId) {
        this.loadRoomList(buildingId)
      }
    },

    /**
     * 房间变化处理
     */
    onRoomChange(roomId) {
      if (roomId) {
        const selectedBuilding = this.buildingList.find(b => b.id === this.propertyModel.buildingId)
        const selectedRoom = this.roomList.find(r => r.id === roomId)

        if (selectedBuilding && selectedRoom) {
          this.propertyModel.address = `${selectedBuilding.buildingNumber}${selectedRoom.unitNumber?selectedRoom.unitNumber:''}${selectedRoom.roomNumber}`
        }
      } else {
        this.propertyModel.address = ''
      }
    },

    /**
     * 提交表单
     */
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return

        this.submitting = true
        const api = this.propertyModel.id ? editResidentProperty : addResidentProperty

        api(this.propertyModel)
          .then(() => {
            this.$message.success('保存成功')
            this.dialog.show = false
            this.$emit('refresh')
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '保存失败')
          })
          .finally(() => {
            this.submitting = false
          })
      })
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.$refs.form && this.$refs.form.resetFields()
      this.propertyModel = {
        id: null,
        residentId: null,
        communityId: null,
        buildingId: null,
        roomId: null,
        address: '',
        residentType: 'owner',
        status: 'normal',
        note: '',
        roomType: '',
      }
      this.roomList = []
    }
  }
}
</script>

<style scoped>
.property-edit-form {
  padding: 0 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
