import request from '@/utils/request'

export const listRole = (data) =>
	request({
		url: '/manage-api/v1/role/page',
		method: 'get',
		params: data
	})
export const getRole = (id) =>
	request({
		url: '/manage-api/v1/role',
		method: 'get',
		params: { id: id }
	})
export const addRole = (data) =>
	request({
		url: '/manage-api/v1/role',
		method: 'post',
		data: data
	})
	export const editRole = (data) =>
		request({
			url: '/manage-api/v1/role',
			method: 'put',
			data: data
		})
export const deleteRole = (id) =>
	request({
		url: '/manage-api/v1/role',
		method: 'delete',
		params: { id: id }
	}) 


export const getAuthority = () =>
	request({
		url: '/manage-api/v1/role/authority',
		method: 'get',
		params: { }
	})