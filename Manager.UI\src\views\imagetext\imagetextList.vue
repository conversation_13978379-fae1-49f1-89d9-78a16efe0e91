<template>
  <div class="menu-list-container">
    <div class="menu-list-content">
      <imagetext-edit @search="search" ref="editDialog" />
      <div class="card card--search search-flex">
        <el-input v-model="searchModel.title" placeholder="标题" clearable style="width: 200px; margin-right: 16px;" />
        <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
        <el-button type="primary" @click="add">添加</el-button>
      </div>
      <div class="card card--table">
        <div class="table-col">
          <el-table :data="imagetextList" row-key="id" style="width: 100%; height: 100%;" class="data-table">
            <el-table-column prop="id" label="ID" width="80" align="center"/>
            <el-table-column prop="title" label="标题" align="center"/>
            <el-table-column prop="imageUrl" label="图片" align="center">
              <template #default="scope">
                <el-image v-if="scope.row.imageUrl" :src="imgServer + scope.row.imageUrl"
                 style="width: 60px; height: 40px;" fit="cover" :preview-src-list="[imgServer + scope.row.imageUrl]"/>
              </template>
            </el-table-column>
            <el-table-column prop="link" label="跳转链接" align="center"/>
            <el-table-column prop="sort" label="排序" width="80" align="center"/>
            <el-table-column prop="type" label="类型" align="center"/>
            <el-table-column prop="createTime" label="创建时间" align="center"/>
            <el-table-column prop="updateTime" label="修改时间" align="center"/>
            <el-table-column label="操作" width="180">
              <template #default="scope">
                <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
                <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination background layout="prev, pager, next" @current-change="currentChange" :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listImagetext, deleteImagetext, getImagetext } from '@/api/system/imagetext'
import mitt from '@/utils/mitt'
import imagetextEdit from '@/components/system/imagetextEdit.vue'
export default {
  components: { imagetextEdit },
  data() {
    return {
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        title: ''
      },
      imagetextList: [],
      total: 0
    }
  },
  methods: {
    search() {
      listImagetext(this.searchModel).then(res => {
        this.imagetextList = res.data.data.list
        this.total = res.data.data.total
      })
    },
    add() {
      mitt.emit('openImagetextAdd')
    },
    edit(id) {
      getImagetext(id).then(res => {
        mitt.emit('openImagetextEdit', res.data.data)
      })
    },
    deleted(id) {
      this.$confirm('删除图文, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteImagetext(id).then(res => {
          this.search()
          this.$message.success('操作成功')
        })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    }
  },
  created() {
    this.search()
  }
}
</script>

<style scoped>

::v-deep .--el-table-index {
  z-index: 9999 !important;
}

.menu-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.menu-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}
.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.dark-theme .card {
  background-color: var(--card-background);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}
</style> 