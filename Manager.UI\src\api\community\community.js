import request from '@/utils/request'

export const listCommunity = (data) =>
	request({
		url: '/manage-api/v1/community/page',
		method: 'get',
		params: data
	})

export const getCommunity = (id) =>
	request({
		url: '/manage-api/v1/community',
		method: 'get',
		params: { id: id }
	})

export const addCommunity = (data) =>
	request({
		url: '/manage-api/v1/community',
		method: 'post',
		data: data
	})

export const editCommunity = (data) =>
	request({
		url: '/manage-api/v1/community',
		method: 'put',
		data: data
	})

export const deleteCommunity = (id) =>
	request({
		url: '/manage-api/v1/community',
		method: 'delete',
		params: {
			id: id
		}
	}) 