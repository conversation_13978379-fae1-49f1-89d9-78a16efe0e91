import request from '@/utils/request'

// 分页查询物业账单
export function listPropertyBill(params) {
  return request({
    url: '/manage-api/v1/property/bill/page',
    method: 'get',
    params
  })
}

// 通过ID查询物业账单
export function getPropertyBill(id) {
  return request({
    url: '/manage-api/v1/property/bill',
    method: 'get',
    params: { id }
  })
}

// 新增物业账单
export function addPropertyBill(data) {
  return request({
    url: '/manage-api/v1/property/bill',
    method: 'post',
    params: data
  })
}

// 编辑物业账单
export function editPropertyBill(data) {
  return request({
    url: '/manage-api/v1/property/bill',
    method: 'put',
    params: data
  })
}

// 删除物业账单
export function deletePropertyBill(id) {
  return request({
    url: '/manage-api/v1/property/bill',
    method: 'delete',
    params: { id }
  })
} 