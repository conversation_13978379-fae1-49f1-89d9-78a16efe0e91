<template>
  <div class="device-type-list-container">
    <device-type-edit @search="search" ref="editDialog" />
    <div class="card card--search search-flex">
      <div class="search-left">
        <el-input v-model="searchModel.typeName" placeholder="类型名称" clearable style="width: 200px; margin-right: 16px;" />
        <el-input v-model="searchModel.typeCode" placeholder="类型代号" clearable style="width: 200px; margin-right: 16px;" />
        <el-button type="primary" @click="search">搜索</el-button>
      </div>
      <div class="search-right">
        <el-button type="primary" @click="add">添加</el-button>
      </div>
    </div>
    <div class="card card--table">
      <div class="table-col">
        <el-table :data="deviceTypeList" row-key="id" align="center" style="width: 100%; height: 100%;" class="data-table">
     
          <el-table-column prop="typeName" label="类型名称" min-width="150" align="center"/>
          <el-table-column prop="typeCode" label="类型代号" min-width="120" align="center"/>
          <el-table-column prop="parentId" label="父级ID" width="100" align="center"/>
          <el-table-column prop="ancestors" label="祖级列表" min-width="150" align="center" show-overflow-tooltip/>
          <el-table-column prop="sort" label="排序" width="80" align="center"/>
          <el-table-column prop="communityId" label="小区ID" width="100" align="center"/>
          <el-table-column label="操作" width="240" align="center">
            <template #default="scope">
              <el-button type="text" size="mini" @click="add(scope.row.id)">添加</el-button>
              <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
              <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-col">
        <el-pagination background layout="prev, pager, next" @current-change="currentChange" :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
      </div>
    </div>
  </div>
</template>

<script>
import { listDeviceType, deleteDeviceType } from '@/api/deviceApi'
import { getSelectedCommunityId, hasSelectedCommunity } from '@/store/modules/options'
import mitt from '@/utils/mitt'
import deviceTypeEdit from '@/components/device/deviceTypeEdit.vue'
import communityMixin from '@/mixins/communityMixin'

export default {
  components: { deviceTypeEdit },
  mixins: [communityMixin],
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        typeName: '',
        typeCode: ''
      },
      deviceTypeList: [],
      total: 0
    }
  },
  computed: {
    hasCurrentCommunity() {
      return hasSelectedCommunity()
    }
  },
  methods: {
    search() {
      // 添加小区ID参数
      const searchParams = {
        ...this.searchModel,
        communityId: getSelectedCommunityId()
      }

      listDeviceType(searchParams).then(res => {
        this.deviceTypeList = res.data.data.list
        this.total = res.data.data.total
      })
    },
    add(parentId = 0) {
      mitt.emit('openDeviceTypeEdit', { type: 'add', parentId: parentId })
    },
    edit(id) {
      mitt.emit('openDeviceTypeEdit', { type: 'edit', id: id })
    },
    deleted(id) {
      this.$confirm('此操作将永久删除该设备类型, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteDeviceType(id).then(() => {
          this.search()
          this.$message.success('操作成功')
        })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    /**
     * 小区变化监听方法
     */
    onCommunityChange(community) {
      // 清空列表数据
      this.deviceTypeList = []
      this.total = 0

      // 如果有选中的小区，重新加载数据
      if (community) {
        this.search()
      }
    }
  },
  created() {
    // 只有在有选中小区时才初始化数据
    if (this.hasCurrentCommunity) {
      this.search()
    }
  }
}
</script>

<style scoped>
.device-type-list-container {
  padding: 20px;
}

.table-col {
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.dark-theme .card {
  background-color: var(--card-background);
  border: 1px solid #444;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.search-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.search-left {
  display: flex;
  align-items: center;
}

.search-right {
  display: flex;
  align-items: center;
}
</style>
