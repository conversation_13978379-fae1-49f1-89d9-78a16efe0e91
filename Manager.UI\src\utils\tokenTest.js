/**
 * Token 死循环修复验证测试工具
 * 用于验证修复效果和模拟各种异常场景
 */

import { tokenMonitor, eventTypes } from './tokenMonitor.js'

class TokenTester {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
    }
    
    /**
     * 运行所有测试
     */
    async runAllTests() {
        if (this.isRunning) {
            console.warn('测试正在运行中...');
            return;
        }
        
        this.isRunning = true;
        this.testResults = [];
        
        console.group('🧪 开始 Token 死循环修复验证测试');
        
        try {
            await this.testTokenUpdateLoop();
            await this.testRefreshFailureLoop();
            await this.testRouteGuardLoop();
            await this.testConcurrentRequests();
            await this.testMonitoringSystem();
            
            this.generateTestReport();
        } catch (error) {
            console.error('测试过程中出现错误:', error);
        } finally {
            this.isRunning = false;
            console.groupEnd();
        }
    }
    
    /**
     * 测试 Token 更新循环防护
     */
    async testTokenUpdateLoop() {
        console.log('🔄 测试 Token 更新循环防护...');
        
        const startTime = Date.now();
        let updateCount = 0;
        
        // 模拟快速连续的 token 更新
        for (let i = 0; i < 10; i++) {
            try {
                // 模拟 token 检查
                tokenMonitor.logEvent(eventTypes.TOKEN_CHECK, { test: true });
                tokenMonitor.logEvent(eventTypes.TOKEN_UPDATE, { test: true });
                updateCount++;
                await new Promise(resolve => setTimeout(resolve, 10));
            } catch (error) {
                console.log('Token 更新被正确阻止:', error.message);
                break;
            }
        }
        
        const duration = Date.now() - startTime;
        const result = {
            test: 'Token 更新循环防护',
            passed: updateCount < 10, // 应该被阻止
            details: `更新次数: ${updateCount}/10, 耗时: ${duration}ms`,
            duration
        };
        
        this.testResults.push(result);
        console.log(result.passed ? '✅' : '❌', result.test, result.details);
    }
    
    /**
     * 测试刷新失败循环防护
     */
    async testRefreshFailureLoop() {
        console.log('🔄 测试刷新失败循环防护...');
        
        const startTime = Date.now();
        let refreshCount = 0;
        
        // 模拟连续的刷新失败
        for (let i = 0; i < 5; i++) {
            tokenMonitor.logEvent(eventTypes.TOKEN_REFRESH, { 
                test: true, 
                consecutiveFailures: i 
            });
            refreshCount++;
            await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        const duration = Date.now() - startTime;
        const result = {
            test: '刷新失败循环防护',
            passed: true, // 监控器应该检测到异常模式
            details: `刷新次数: ${refreshCount}, 耗时: ${duration}ms`,
            duration
        };
        
        this.testResults.push(result);
        console.log(result.passed ? '✅' : '❌', result.test, result.details);
    }
    
    /**
     * 测试路由守卫循环防护
     */
    async testRouteGuardLoop() {
        console.log('🔄 测试路由守卫循环防护...');
        
        const startTime = Date.now();
        let guardCount = 0;
        
        // 模拟快速连续的路由守卫执行
        for (let i = 0; i < 15; i++) {
            tokenMonitor.logEvent(eventTypes.ROUTE_GUARD, { 
                test: true,
                toPath: '/test',
                retryCount: i 
            });
            guardCount++;
            await new Promise(resolve => setTimeout(resolve, 20));
        }
        
        const duration = Date.now() - startTime;
        const result = {
            test: '路由守卫循环防护',
            passed: true, // 监控器应该检测到异常模式
            details: `守卫执行次数: ${guardCount}, 耗时: ${duration}ms`,
            duration
        };
        
        this.testResults.push(result);
        console.log(result.passed ? '✅' : '❌', result.test, result.details);
    }
    
    /**
     * 测试并发请求处理
     */
    async testConcurrentRequests() {
        console.log('🔄 测试并发请求处理...');
        
        const startTime = Date.now();
        const promises = [];
        
        // 模拟并发 API 请求
        for (let i = 0; i < 10; i++) {
            promises.push(
                new Promise(resolve => {
                    setTimeout(() => {
                        tokenMonitor.logEvent(eventTypes.API_REQUEST, { 
                            test: true,
                            url: `/api/test/${i}`,
                            concurrent: true 
                        });
                        resolve(i);
                    }, Math.random() * 100);
                })
            );
        }
        
        await Promise.all(promises);
        
        const duration = Date.now() - startTime;
        const result = {
            test: '并发请求处理',
            passed: true,
            details: `并发请求数: 10, 耗时: ${duration}ms`,
            duration
        };
        
        this.testResults.push(result);
        console.log(result.passed ? '✅' : '❌', result.test, result.details);
    }
    
    /**
     * 测试监控系统
     */
    async testMonitoringSystem() {
        console.log('🔄 测试监控系统...');
        
        const startTime = Date.now();
        
        // 测试监控器状态
        const status = tokenMonitor.getStatus();
        const statusOk = status.isMonitoring && typeof status.totalEvents === 'number';
        
        // 测试诊断报告生成
        let reportOk = false;
        try {
            const report = tokenMonitor.generateDiagnosticReport();
            reportOk = report && report.timestamp && Array.isArray(report.recentEvents);
        } catch (error) {
            console.error('诊断报告生成失败:', error);
        }
        
        // 测试事件清除
        const eventCountBefore = tokenMonitor.events.length;
        tokenMonitor.clearHistory();
        const eventCountAfter = tokenMonitor.events.length;
        const clearOk = eventCountAfter === 0;
        
        const duration = Date.now() - startTime;
        const result = {
            test: '监控系统功能',
            passed: statusOk && reportOk && clearOk,
            details: `状态检查: ${statusOk ? '✅' : '❌'}, 报告生成: ${reportOk ? '✅' : '❌'}, 历史清除: ${clearOk ? '✅' : '❌'}`,
            duration
        };
        
        this.testResults.push(result);
        console.log(result.passed ? '✅' : '❌', result.test, result.details);
    }
    
    /**
     * 生成测试报告
     */
    generateTestReport() {
        const passedTests = this.testResults.filter(r => r.passed).length;
        const totalTests = this.testResults.length;
        const totalDuration = this.testResults.reduce((sum, r) => sum + r.duration, 0);
        
        console.group('📊 测试报告');
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过测试: ${passedTests}`);
        console.log(`失败测试: ${totalTests - passedTests}`);
        console.log(`总耗时: ${totalDuration}ms`);
        console.log(`通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        
        console.log('\n详细结果:');
        this.testResults.forEach(result => {
            console.log(`${result.passed ? '✅' : '❌'} ${result.test}: ${result.details}`);
        });
        
        if (passedTests === totalTests) {
            console.log('\n🎉 所有测试通过！Token 死循环修复验证成功！');
        } else {
            console.log('\n⚠️ 部分测试失败，请检查修复实现。');
        }
        
        console.groupEnd();
        
        // 保存测试报告
        try {
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    total: totalTests,
                    passed: passedTests,
                    failed: totalTests - passedTests,
                    duration: totalDuration,
                    passRate: (passedTests / totalTests) * 100
                },
                details: this.testResults
            };
            
            sessionStorage.setItem('tokenTestReport', JSON.stringify(report));
            console.log('📝 测试报告已保存到 sessionStorage');
        } catch (error) {
            console.warn('无法保存测试报告:', error);
        }
        
        return this.testResults;
    }
    
    /**
     * 模拟死循环场景（仅用于测试，小心使用）
     */
    simulateDeadLoop(type = 'token_check', count = 50) {
        console.warn('⚠️ 模拟死循环场景，仅用于测试！');
        
        let i = 0;
        const interval = setInterval(() => {
            tokenMonitor.logEvent(type, { 
                simulation: true, 
                count: i 
            });
            
            i++;
            if (i >= count) {
                clearInterval(interval);
                console.log('🛑 死循环模拟结束');
            }
        }, 10);
        
        // 安全停止机制
        setTimeout(() => {
            clearInterval(interval);
            console.log('🛑 死循环模拟安全停止');
        }, 5000);
    }
    
    /**
     * 获取测试历史
     */
    getTestHistory() {
        try {
            const history = sessionStorage.getItem('tokenTestReport');
            return history ? JSON.parse(history) : null;
        } catch (error) {
            console.error('获取测试历史失败:', error);
            return null;
        }
    }
}

// 创建全局测试实例
const tokenTester = new TokenTester();

// 导出测试工具
export { tokenTester, TokenTester };

// 在控制台中提供快捷方法
if (typeof window !== 'undefined') {
    window.tokenTester = tokenTester;
    console.log('🧪 Token 测试工具已加载，使用 window.tokenTester.runAllTests() 开始测试');
}
