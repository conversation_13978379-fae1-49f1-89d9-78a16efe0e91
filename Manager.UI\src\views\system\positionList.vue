<template>
	<div class="position-list-container">
		<position-edit ref="positionEdit" :certificateList="certificateList" :statusList="statusList" :orgTreeData="orgTreeData" @search="search"></position-edit>
		<div class="card card--search search-flex">
			<div class="search-left">
				<el-input v-model="searchModel.positionName" placeholder="职位" clearable style="width: 200px; margin-right: 16px;" />
				<el-cascader
					v-model="searchModel.orgId"
					:options="orgTreeData"
					:props="orgCascaderProps"
					placeholder="选择组织"
					clearable
					filterable
					:show-all-levels="false"
					style="width: 200px; margin-right: 16px;"
				/>
				<el-button type="primary" @click="search">搜索</el-button>
			</div>
			<div class="search-right">
				<el-button type="primary" @click="add">添加</el-button>
			</div>
		</div>
		<div class="card card--table">
			<div class="table-col">
				<el-table stripe :data="positionList" style="width: 100%; height: 100%;" class="data-table">
					<el-table-column prop="id" align="center" label="ID" width="80" />
					<el-table-column prop="positionName" align="center" label="职位" min-width="200" show-overflow-tooltip />
					<el-table-column align="center" label="所属组织" min-width="200" show-overflow-tooltip>
						<template #default="scope">
							{{ getOrgName(scope.row.orgId) }}
						</template>
					</el-table-column>
					<el-table-column prop="createTime" align="center" label="创建时间" min-width="180" show-overflow-tooltip />
					<el-table-column align="center" label="操作" width="150" fixed="right">
						<template #default="scope">
							<el-button type="text" size="small" @click="edit(scope.row.id)">编辑</el-button>
							<el-button type="text" size="small" @click="deleted(scope.row.id)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div class="pagination-container">
				<el-pagination v-model:current-page="searchModel.pageNum" v-model:page-size="searchModel.pageSize"
					:page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
					@size-change="search" @current-change="currentChange" />
			</div>
		</div>
	</div>
</template>
<script>
import { listPosition, deletePosition, getPosition } from "@/api/system/position"
import { listOrg } from "@/api/system/org"
import mitt from "@/utils/mitt"
import positionEdit from "@/components/system/positionEdit.vue"
export default {
	components: {
        positionEdit
      },
	data() {
		return {
			searchModel: {
				pageNum: 1,
				pageSize: 10,
				positionName: '',
				orgId: null
			},
			positionList: [],
            certificateList: [],
			statusList: [],
			orgTreeData: [],
			orgMap: new Map(), // 用于存储orgId到orgName的映射
			total: 0,
			orgCascaderProps: {
				value: 'id',
				label: 'orgName',
				children: 'children',
				emitPath: false,
				checkStrictly: true,
				expandTrigger: 'click'
			}
		}
	},
	methods: {
		search() {
			listPosition(this.searchModel)
				.then(res => {
					this.positionList = res.data.data.list
					this.total = res.data.data.total
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		loadOrgList() {
			listOrg({ pageNum: 1, pageSize: 1000 })
				.then(res => {
					this.orgTreeData = res.data.data.list || []
					this.buildOrgMap(this.orgTreeData)
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		buildOrgMap(orgList) {
			// 递归构建orgId到orgName的映射
			const buildMap = (list) => {
				list.forEach(org => {
					this.orgMap.set(org.id, org.orgName)
					if (org.children && org.children.length > 0) {
						buildMap(org.children)
					}
				})
			}
			buildMap(orgList)
		},
		getOrgName(orgId) {
			if (!orgId) return '-'
			return this.orgMap.get(orgId) || '-'
		},
		add() {
			mitt.emit('openPositionAdd');
		},
		edit(id) {
			getPosition(id)
				.then(res => {
					mitt.emit('openPositionEdit', res.data.data)
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		deleted(id) {
			this.$confirm('删除职位, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deletePosition(id)
					.then(() => {
						this.search()
						this.$message.success("操作成功")
					})
					.catch(err => {
						this.$message.error(err.data.errorMessage)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		}
	},
	created() {
		this.loadOrgList()
		this.search()
	},
	unmounted() {
		mitt.off('openPositionAdd')
		mitt.off('openPositionEdit')
	}
}
</script>

<style scoped>
.position-list-container {
	display: flex;
	flex-direction: column;
	height: 100%;
	box-sizing: border-box;
}

.card--table {
	background-color: #fff;
	border-radius: 5px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	flex: 1;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: auto;
	margin-top: 0;
}

.table-col {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
}

.data-table {
	flex: 1;
	display: flex;
	flex-direction: column;
	height: 100% !important;
}

.pagination-col {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
}

.pagination-container {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
	padding: 0 20px 20px;
}

.search-flex {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.search-left {
	display: flex;
	align-items: center;
}

.search-right {
	display: flex;
	align-items: center;
}

.card--search {
	margin-bottom: 20px;
	flex: none;
	height: auto;
	padding: 20px 20px;
	display: flex;
	align-items: center;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
}
</style>