<template>
  <div class="goods-list-container">
    <div class="goods-list-content">
      <!-- 搜索区域 -->
      <div class="card card--search search-flex">
        <el-input
          v-model="searchModel.stuffDescribe"
          placeholder="商品描述"
          clearable
          style="width: 200px; margin-right: 16px;"
        />

        <el-select
          v-model="searchModel.status"
          placeholder="状态"
          clearable
          style="width: 120px; margin-right: 16px;"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select
          v-model="searchModel.type"
          placeholder="好物类型"
          clearable
          style="width: 120px; margin-right: 16px;"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>

      <!-- 表格区域 -->
      <div class="card card--table">
        <div class="table-col">
          <el-table
            :data="goodsList"
            row-key="id"
            style="width: 100%; height: 100%;"
            class="data-table"
            v-loading="loading"
          >
            <el-table-column prop="id" label="ID" width="80" align="center"/>
            <el-table-column prop="title" label="好物标题" align="center" min-width="150" show-overflow-tooltip/>
            <el-table-column prop="media" label="好物图片" align="center" width="100">
              <template #default="scope">
                <el-image
                  v-if="scope.row.media"
                  :src="getFirstImageUrl(scope.row.media)"
                  style="width: 60px; height: 40px;"
                  fit="cover"
                  :preview-src-list="getImageUrlList(scope.row.media)"
               :preview-teleported="true"
                />
                <span v-else class="text-muted">无图片</span>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="价格" width="100" align="center">
              <template #default="scope">
                <span class="price-text">¥{{ scope.row.amount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="好物类型" width="100" align="center">
              <template #default="scope">
                <span>{{ getTypeLabel(scope.row.type) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="categoryCode" label="分类" width="100" align="center">
              <template #default="scope">
                <span>{{ getCategoryLabel(scope.row.categoryCode) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="points" label="积分" width="80" align="center"/>
            <el-table-column prop="stock" label="库存" width="80" align="center"/>
            <el-table-column prop="views" label="浏览量" width="80" align="center"/>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="small" :color="getStatusColor(scope.row.status)" style="color: white;">
                  {{ getStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="发布时间" width="160" align="center"/>
            <el-table-column label="操作" width="280" align="center">
              <template #default="scope">
                <el-button type="text" @click="viewDetail(scope.row)">查看</el-button>

                <!-- 审核操作 -->
                <el-dropdown
                style="margin-top: 3px;"
                  @command="(command) => examineGoods(scope.row, command)"
                  trigger="click"
                >
                  <el-button type="text" size="small">
                    审核<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-if="scope.row.status !== 'pending'"
                        command="pending"
                      >
                        <el-icon><clock /></el-icon>
                        待审核
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="scope.row.status !== 'pass'"
                        command="pass"
                      >
                        <el-icon><check /></el-icon>
                        通过
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="scope.row.status !== 'no_pass'"
                        command="no_pass"
                      >
                        <el-icon><close /></el-icon>
                        不通过
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>

                <!-- 上架下架操作 -->
                <el-button
                  v-if="scope.row.status === 'pass'"
                  type="text"
                  size="small"
                  @click="listGoods(scope.row)"
                >
                  上架
                </el-button>
                <el-button
                  v-if="scope.row.status === 'list'"
                  type="text"
                  size="small"
                  @click="unlistGoods(scope.row)"
                >
                  下架
                </el-button>

                <el-button type="text" size="small" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @current-change="currentChange"
            :current-page="searchModel.pageNum"
            :page-size="searchModel.pageSize"
            layout="total, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialog.visible"
      :title="detailDialog.title"
      width="900px"
      :close-on-click-modal="false"
      class="goods-detail-dialog"
    >
      <div v-if="detailDialog.data" class="detail-content">
        <el-descriptions :column="2" border class="goods-descriptions">
          <el-descriptions-item label="好物标题" label-class-name="fixed-label">
            {{ detailDialog.data.title }}
          </el-descriptions-item>
          <el-descriptions-item label="价格" label-class-name="fixed-label">
            <span class="price-text">¥{{ detailDialog.data.amount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="好物类型" label-class-name="fixed-label">
            {{ getTypeLabel(detailDialog.data.type) }}
          </el-descriptions-item>
          <el-descriptions-item label="分类" label-class-name="fixed-label">
            {{ getCategoryLabel(detailDialog.data.categoryCode) }}
          </el-descriptions-item>
          <el-descriptions-item label="库存" label-class-name="fixed-label">
            {{ detailDialog.data.stock }}
          </el-descriptions-item>
          <el-descriptions-item label="浏览量" label-class-name="fixed-label">
            {{ detailDialog.data.views }}
          </el-descriptions-item>
          <el-descriptions-item label="积分" label-class-name="fixed-label">
            {{ detailDialog.data.points }}
          </el-descriptions-item>
          <el-descriptions-item label="状态" label-class-name="fixed-label">
            <el-tag :type="getStatusType(detailDialog.data.status)" size="small" :color="getStatusColor(detailDialog.data.status)">
              {{ getStatusLabel(detailDialog.data.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发布时间" label-class-name="fixed-label">
            {{ detailDialog.data.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" label-class-name="fixed-label">
            {{ detailDialog.data.updateTime || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="地址信息" :span="2" label-class-name="fixed-label">
            <div v-if="detailDialog.data.address">
              {{ detailDialog.data.address }}
            </div>
            <div v-else>无地址信息</div>
          </el-descriptions-item>
          <el-descriptions-item label="位置坐标" :span="2" label-class-name="fixed-label">
            <div v-if="detailDialog.data.lat && detailDialog.data.lng">
              纬度: {{ detailDialog.data.lat }}, 经度: {{ detailDialog.data.lng }}
            </div>
            <div v-else>无位置坐标</div>
          </el-descriptions-item>
               <el-descriptions-item label="审核备注" label-class-name="fixed-label">
            {{ detailDialog.data.examineNote || '无' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 好物描述 -->
        <div class="description-section">
          <h4>好物描述</h4>
          <div class="rich-text-content" v-html="detailDialog.data.stuffDescribe"></div>
        </div>

        <!-- 好物图片 -->
        <div v-if="detailDialog.data.media" class="image-section">
          <h4>好物图片</h4>
          <div class="image-gallery">
            <el-image
              v-for="(imageUrl, index) in getImageUrlList(detailDialog.data.media)"
              :key="index"
              :src="imageUrl"
              style="width: 120px; height: 90px; margin-right: 10px; margin-bottom: 10px;"
              fit="cover"
              :preview-src-list="getImageUrlList(detailDialog.data.media)"
              :initial-index="index"    :preview-teleported="true"
            />
          </div>
        </div>


      </div>

      <!-- 固定在底部的审核操作 -->
      <template #footer>
        <div class="dialog-footer">
          <div class="audit-actions">
            <!-- 审核操作 -->
            <div class="audit-group">
              <span class="action-label">审核操作：</span>
              <el-button
                v-if="detailDialog.data.status !== 'pending'"
                type="warning"
                @click="examineGoods(detailDialog.data, 'pending')"
              >
                <el-icon><clock /></el-icon>
                待审核
              </el-button>
              <el-button
                v-if="detailDialog.data.status !== 'pass'"
                type="success"
                @click="examineGoods(detailDialog.data, 'pass')"
              >
                <el-icon><check /></el-icon>
                通过
              </el-button>
              <el-button
                v-if="detailDialog.data.status !== 'no_pass'"
                type="danger"
                @click="examineGoods(detailDialog.data, 'no_pass')"
              >
                <el-icon><close /></el-icon>
                不通过
              </el-button>
            </div>

            <!-- 上架下架操作 -->
            <div class="listing-group" v-if="detailDialog.data.status === 'pass' || detailDialog.data.status === 'list'">
              <span class="action-label">上架操作：</span>
              <el-button
                v-if="detailDialog.data.status === 'pass'"
                type="primary"
                @click="listGoods(detailDialog.data)"
              >
                <el-icon><check /></el-icon>
                上架
              </el-button>
              <el-button
                v-if="detailDialog.data.status === 'list'"
                type="warning"
                @click="unlistGoods(detailDialog.data)"
              >
                <el-icon><close /></el-icon>
                下架
              </el-button>
            </div>
          </div>

          <div class="close-actions">
            <el-button @click="detailDialog.visible = false">关闭</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listGoodStuff, getGoodStuff, deleteGoodStuff, examineGoodStuff } from '@/api/goods/manageGoodStuff'
import { listDictByNameEn } from '@/api/system/dict'
import { ArrowDown, Check, Close, Clock } from '@element-plus/icons-vue'

export default {
  name: 'GoodsList',

  components: {
    ArrowDown,
    Check,
    Close,
    Clock
  },

  data() {
    return {
      // 搜索条件
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        stuffDescribe: '',
        status: '',
        type: ''
      },
      // 好物列表
      goodsList: [],
      // 总数
      total: 0,
      // 加载状态
      loading: false,
      // 图片服务器地址
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      // 详情弹窗
      detailDialog: {
        visible: false,
        title: '好物详情',
        data: null
      },
      // 数据字典选项
      statusOptions: [],        // 好物状态字典
      typeOptions: [],
      categoryOptions: []
    }
  },

  methods: {
    /**
     * 初始化数据字典
     */
    async initDictData() {
      try {
        // 加载好物状态字典
        const statusRes = await listDictByNameEn('good_stuff_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn,
          cssClass: item.cssClass // 保存颜色信息
        }))

        // 加载好物类型字典
        const typeRes = await listDictByNameEn('good_stuff_type')
        this.typeOptions = (typeRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载好物分类字典
        const categoryRes = await listDictByNameEn('good_stuff_category')
        this.categoryOptions = (categoryRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))
      } catch (err) {
        console.error('加载数据字典失败:', err)
      }
    },

    /**
     * 搜索好物列表
     */
    search() {
      this.loading = true

      // 构建查询参数，过滤空值
      const params = {}
      Object.keys(this.searchModel).forEach(key => {
        if (this.searchModel[key] !== '' && this.searchModel[key] !== null && this.searchModel[key] !== undefined) {
          params[key] = this.searchModel[key]
        }
      })

      listGoodStuff(params).then(res => {
        this.goodsList = res.data.data.list || []
        this.total = res.data.data.total || 0
      }).catch(err => {
        console.error('获取好物列表失败:', err)
        this.$message.error(err.data?.errorMessage || '获取好物列表失败')
      }).finally(() => {
        this.loading = false
      })
    },

    /**
     * 重置搜索条件
     */
    resetSearch() {
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        stuffDescribe: '',
        status: '',
        type: ''
      }
      this.search()
    },

    /**
     * 查看详情
     */
    viewDetail(row) {
      getGoodStuff(row.id).then(res => {
        this.detailDialog.data = res.data.data
        this.detailDialog.visible = true
      }).catch(err => {
        console.error('获取好物详情失败:', err)
        this.$message.error(err.data?.errorMessage || '获取好物详情失败')
      })
    },

    /**
     * 删除好物
     */
    deleted(id) {
      this.$confirm('确定要删除这个好物吗？删除后无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteGoodStuff(id).then(() => {
          this.search()
          this.$message.success('删除成功')
        }).catch(err => {
          console.error('删除好物失败:', err)
          this.$message.error(err.data?.errorMessage || '删除失败')
        })
      }).catch(() => {
        // 用户取消删除
      })
    },

    /**
     * 分页变化
     */
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    /**
     * 审核操作
     */
    examineGoods(row, newStatus) {
      // 使用字典获取状态名称
      const currentStatusText = this.getStatusLabel(row.status)
      const newStatusText = this.getStatusLabel(newStatus)
      const actionText = `从"${currentStatusText}"变更为"${newStatusText}"`

      // 判断是否需要填写原因（审核不通过和下架）
      const needReason = newStatus === 'no_pass' || newStatus === 'un_list'
      const promptTitle = needReason ? '审核确认（必须填写原因）' : '审核确认'
      const placeholder = needReason ? '请输入原因（必填）' : '请输入备注（可选）'

      // 验证函数
      const validator = needReason ? (value) => {
        if (!value || value.trim() === '') {
          return '请输入原因'
        }
        return true
      } : null

      this.$prompt(`确定要将这个好物${actionText}吗？`, promptTitle, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: placeholder,
        inputValidator: validator,
        dangerouslyUseHTMLString: true,
        message: `
          <div>
            <p><strong>商品：</strong>${row.stuffDescribe}</p>
            <p><strong>当前状态：</strong><span style="color: #E6A23C;">${currentStatusText}</span></p>
            <p><strong>变更为：</strong><span style="color: #67C23A;">${newStatusText}</span></p>
            <p style="color: #E6A23C; margin-top: 10px;">此操作将改变商品审核状态，请谨慎操作！</p>
            ${needReason ? '<p style="color: #F56C6C; margin-top: 5px;">注意：此操作需要填写原因！</p>' : ''}
          </div>
        `
      }).then(({ value: examineNote }) => {
        // 构建审核数据
        const examineData = {
          id: row.id,
          status: newStatus,
          examineNote: examineNote || ''
        }

        console.log('审核数据:', examineData)

        examineGoodStuff(examineData).then(res => {
          console.log('审核成功响应:', res)
          this.search()
          this.detailDialog.visible = false
          this.$message.success(`商品审核状态已${actionText}`)
        }).catch(err => {
          console.error(`审核失败:`, err)
          const errorMsg = err.response?.data?.errorMessage || err.data?.errorMessage || `审核失败`
          this.$message.error(errorMsg)
        })
      }).catch(() => {
        // 用户取消操作
      })
    },

    /**
     * 上架操作
     */
    listGoods(row) {
      const currentStatusText = this.getStatusLabel(row.status)
      const newStatusText = this.getStatusLabel('list')

      this.$confirm(`确定要将商品从"${currentStatusText}"变更为"${newStatusText}"吗？`, '上架确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
        dangerouslyUseHTMLString: true,
        message: `
          <div>
            <p><strong>商品：</strong>${row.stuffDescribe}</p>
            <p><strong>当前状态：</strong><span style="color: #E6A23C;">${currentStatusText}</span></p>
            <p><strong>变更为：</strong><span style="color: #67C23A;">${newStatusText}</span></p>
            <p style="color: #67C23A; margin-top: 10px;">此操作将上架商品，用户可以购买！</p>
          </div>
        `
      }).then(() => {
        const listData = {
          id: row.id,
          status: 'list',
          examineNote: '商品上架'
        }

        examineGoodStuff(listData).then(res => {
          this.search()
          if (this.detailDialog.visible) {
            this.detailDialog.visible = false
          }
          this.$message.success('商品已成功上架')
        }).catch(err => {
          console.error('上架失败:', err)
          const errorMsg = err.response?.data?.errorMessage || err.data?.errorMessage || '上架失败'
          this.$message.error(errorMsg)
        })
      }).catch(() => {
        // 用户取消操作
      })
    },

    /**
     * 下架操作
     */
    unlistGoods(row) {
      const currentStatusText = this.getStatusLabel(row.status)
      const newStatusText = this.getStatusLabel('un_list')

      this.$prompt(`确定要将商品从"${currentStatusText}"变更为"${newStatusText}"吗？请输入下架原因：`, '下架确认（必须填写原因）', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入下架原因（必填）',
        inputValidator: (value) => {
          if (!value || value.trim() === '') {
            return '请输入下架原因'
          }
          return true
        },
        dangerouslyUseHTMLString: true,
        message: `
          <div>
            <p><strong>商品：</strong>${row.stuffDescribe}</p>
            <p><strong>当前状态：</strong><span style="color: #67C23A;">${currentStatusText}</span></p>
            <p><strong>变更为：</strong><span style="color: #E6A23C;">${newStatusText}</span></p>
            <p style="color: #E6A23C; margin-top: 10px;">此操作将下架商品，请谨慎操作！</p>
            <p style="color: #F56C6C; margin-top: 5px;">注意：此操作需要填写下架原因！</p>
          </div>
        `
      }).then(({ value: examineNote }) => {
        const unlistData = {
          id: row.id,
          status: 'un_list',
          examineNote: examineNote
        }

        examineGoodStuff(unlistData).then(() => {
          this.search()
          if (this.detailDialog.visible) {
            this.detailDialog.visible = false
          }
          this.$message.success('商品已成功下架')
        }).catch(err => {
          console.error('下架失败:', err)
          const errorMsg = err.response?.data?.errorMessage || err.data?.errorMessage || '下架失败'
          this.$message.error(errorMsg)
        })
      }).catch(() => {
        // 用户取消操作
      })
    },

    /**
     * 获取状态标签
     */
    getStatusLabel(status) {
      const option = this.statusOptions.find(item => item.value === status)
      return option ? option.label : status
    },

    /**
     * 获取状态颜色（十六进制）
     */
    getStatusColor(status) {
      const option = this.statusOptions.find(item => item.value === status)
      return option && option.cssClass ? option.cssClass : null
    },

    /**
     * 获取状态类型（用于Element Plus的tag组件）
     */
    getStatusType(status) {
      // 当使用自定义颜色时，返回空字符串让Element Plus使用默认样式
      const option = this.statusOptions.find(item => item.value === status)
      if (option && option.cssClass) {
        return '' // 使用自定义颜色时不设置type
      }

      // 默认映射，基于实际数据中的status值
      const statusMap = {
        'list': 'success',      // 上架
        'un_list': 'warning',   // 下架
        'pending': 'info',      // 待审核
        'pass': 'success',      // 通过
        'no_pass': 'danger'     // 不通过
      }
      return statusMap[status] || 'info'
    },

    /**
     * 获取类型标签
     */
    getTypeLabel(type) {
      const option = this.typeOptions.find(item => item.value === type)
      return option ? option.label : type
    },

    /**
     * 获取分类标签
     */
    getCategoryLabel(categoryCode) {
      const option = this.categoryOptions.find(item => item.value === categoryCode)
      return option ? option.label : categoryCode
    },

    /**
     * 获取第一张图片URL（用于列表显示）
     */
    getFirstImageUrl(mediaString) {
      if (!mediaString) return ''
      const imageUrls = this.getImageUrlList(mediaString)
      return imageUrls.length > 0 ? imageUrls[0] : ''
    },

    /**
     * 获取图片URL列表（用于预览）
     */
    getImageUrlList(mediaString) {
      if (!mediaString) return []

      // 分割逗号分隔的图片路径
      const imagePaths = mediaString.split(',').filter(path => path.trim())

      return imagePaths.map(path => {
        const trimmedPath = path.trim()
        // 如果已经是完整URL，直接返回
        if (trimmedPath.startsWith('http')) {
          return trimmedPath
        }
        // 否则拼接服务器地址
        return this.imgServer + trimmedPath
      })
    },

    /**
     * 获取图片URL（兼容方法）
     */
    getImageUrl(imageUrl) {
      if (!imageUrl) return ''
      // 如果已经是完整URL，直接返回
      if (imageUrl.startsWith('http')) {
        return imageUrl
      }
      // 否则拼接服务器地址
      return this.imgServer + imageUrl
    }
  },

  async created() {
    await this.initDictData()
    this.search()

  }
}
</script>

<style scoped>
.goods-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.goods-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
}

.card--search {
  flex-shrink: 0;
}

.card--table {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.search-flex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

.price-text {
  color: #e74c3c;
  font-weight: bold;
}

.text-muted {
  color: #999;
  font-size: 12px;
}

.detail-content {
  max-height: 60vh;
  overflow-y: auto;
  padding-bottom: 20px;
}

/* 好物详情弹窗样式 */
.goods-detail-dialog {
  --el-dialog-padding-primary: 20px;
}

/* 固定标题宽度 */
.goods-descriptions :deep(.el-descriptions__label.fixed-label) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  text-align: right;
  padding-right: 12px;
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 富文本内容样式 */
.rich-text-content {
  line-height: 1.6;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.rich-text-content p {
  margin: 0 0 8px 0;
}

.rich-text-content p:last-child {
  margin-bottom: 0;
}

/* 底部操作区域样式 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0 0 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 16px;
}

.audit-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.audit-group, .listing-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.action-label {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
  min-width: 80px;
  text-align: right;
}

.close-actions {
  margin-left: 20px;
}

.description-text {
  max-height: 100px;
  overflow-y: auto;
  line-height: 1.5;
  word-break: break-all;
}

.description-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.description-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.image-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.image-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-gallery .el-image {
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.image-gallery .el-image:hover {
  transform: scale(1.05);
}

.audit-section, .listing-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.audit-section h4, .listing-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 14px;
}

.audit-buttons, .listing-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.audit-buttons .el-button, .listing-buttons .el-button {
  min-width: 140px;
  padding: 12px 20px;
}

.audit-buttons .el-button .el-icon, .listing-buttons .el-button .el-icon {
  margin-right: 5px;
}

/* 暗色主题适配 */
.dark-theme .card {
  background: var(--card-background);
  color: var(--text-primary);
}

.dark-theme .price-text {
  color: #ff6b6b;
}

.dark-theme .image-section {
  border-top-color: #444;
}

.dark-theme .image-section h4 {
  color: var(--text-primary);
}

.dark-theme .audit-section, .dark-theme .listing-section {
  border-top-color: #444;
}

.dark-theme .audit-section h4, .dark-theme .listing-section h4 {
  color: var(--text-primary);
}

.dark-theme .audit-buttons .el-button, .dark-theme .listing-buttons .el-button {
  border-color: var(--border-color);
}

/* 图片预览层级设置 - 确保在最顶层 */
:deep(.el-image-viewer__wrapper) {
  z-index: 9999 !important;
}

:deep(.el-image-viewer__mask) {
  z-index: 9998 !important;
}

:deep(.el-image-viewer__btn) {
  z-index: 10000 !important;
}

:deep(.el-image-viewer__canvas) {
  z-index: 9999 !important;
}

/* 确保图片预览工具栏在最顶层 */
:deep(.el-image-viewer__actions) {
  z-index: 10001 !important;
}

/* 确保图片预览关闭按钮在最顶层 */
:deep(.el-image-viewer__close) {
  z-index: 10002 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-flex {
    flex-direction: column;
    align-items: stretch;
  }

  .search-flex > * {
    width: 100%;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  .search-flex > *:last-child {
    margin-bottom: 0;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .audit-buttons, .listing-buttons {
    flex-direction: column;
    align-items: center;
  }

  .audit-buttons .el-button, .listing-buttons .el-button {
    width: 100%;
    max-width: 280px;
  }
}

.el-table .el-tag {
  border-color: transparent !important;

}
</style>