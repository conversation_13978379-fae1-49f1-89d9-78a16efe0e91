<template>
  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    width="1200px"
    top="5vh"
    :close-on-click-modal="false"
    class="payment-detail-dialog"
  >
    <div class="detail-container">
      <!-- 缴费对象信息 -->
      <div class="object-info">
        <el-descriptions :column="4" border>
          <el-descriptions-item label="对象名称">{{ currentObject.moniker }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ currentObject.phone || '--' }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ currentObject.email || '--' }}</el-descriptions-item>
          <el-descriptions-item label="地址">{{ currentObject.address || '--' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-bar">
        <el-button type="primary" @click="addPaymentDetail">新增缴费明细</el-button>
      </div>
      
      <!-- 缴费明细列表 -->
      <div class="detail-table">
        <el-table :data="paymentDetailList" style="width: 100%;" v-loading="loading">
          <el-table-column prop="id" label="ID" width="80" align="center"/>
          <el-table-column prop="paymentItemSnapshot" label="缴费项目" align="center" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ getItemSnapshotDisplay(scope.row.paymentItemSnapshot) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="unitPrice" label="单价" align="center" width="100">
            <template #default="scope">
              <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.unitPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="unit" label="单位" align="center" width="80"/>
          <el-table-column prop="quantity" label="数量" align="center" width="100"/>
          <el-table-column prop="discount" label="折扣" align="center" width="80">
            <template #default="scope">
              <span>{{ scope.row.discount }}折</span>
            </template>
          </el-table-column>
          <el-table-column prop="totalAmount" label="总金额" align="center" width="120">
            <template #default="scope">
              <span style="color: #f56c6c; font-weight: bold;">¥{{ calculateAmount(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="billingCycle" label="计费周期" align="center" width="100">
            <template #default="scope">
              <span>{{ getBillingCycleLabel(scope.row.billingCycle) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="effectiveDate" label="生效日期" align="center" width="120"/>
          <el-table-column prop="expiringDate" label="失效日期" align="center" width="120"/>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button type="text" size="mini" @click="editPaymentDetail(scope.row)">编辑</el-button>
              <el-button type="text" size="mini" @click="deletePaymentDetail(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-col">
          <el-pagination 
            background 
            layout="prev, pager, next" 
            @current-change="currentChange" 
            :total="total" 
            :page-size="searchModel.pageSize" 
            :current-page="searchModel.pageNum" />
        </div>
      </div>
    </div>
    
    <!-- 缴费明细编辑弹窗 -->
    <payment-detail-edit-dialog 
      ref="paymentDetailEditDialog" 
      :payment-object="currentObject"
      @search="searchPaymentDetails" />
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialog.show = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { listPropertyPaymentDetail, deletePropertyPaymentDetail } from '@/api/property/paymentItems'
import { listDictByNameEn } from '@/api/system/dict'
import mitt from '@/utils/mitt'
import paymentDetailEditDialog from '@/components/property/paymentDetailEditDialog.vue'

export default {
  name: 'PaymentDetailDialog',
  components: {
    'payment-detail-edit-dialog': paymentDetailEditDialog
  },
  
  data() {
    return {
      dialog: {
        show: false,
        title: '缴费明细管理'
      },
      loading: false,
      currentObject: {},
      paymentDetailList: [],
      total: 0,
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        paymentObjectId: null
      },
      statusOptions: []
    }
  },
  
  methods: {
    /**
     * 搜索缴费明细
     */
    searchPaymentDetails() {
      if (!this.currentObject.id) return
      
      this.loading = true
      const params = {
        ...this.searchModel,
        paymentObjectId: this.currentObject.id
      }
      
      listPropertyPaymentDetail(params).then(res => {
        this.paymentDetailList = res.data.data.list || []
        this.total = res.data.data.total || 0
      }).catch(err => {
        this.$message.error(err.data?.errorMessage || '查询失败')
      }).finally(() => {
        this.loading = false
      })
    },
    
    /**
     * 计算总金额
     */
    calculateAmount(row) {
      const amount = (row.unitPrice || 0) * (row.quantity || 0) * (row.discount || 10) / 10
      return amount.toFixed(2)
    },
    
    /**
     * 获取项目快照显示
     */
    getItemSnapshotDisplay(snapshot) {
      if (!snapshot) return '--'
      
      try {
        const item = JSON.parse(snapshot)
        return item.paymentItemName || snapshot
      } catch (error) {
        return snapshot
      }
    },
    
    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const statusMap = {
        'active': 'success',
        'inactive': 'danger',
        'pending': 'warning',
        'expired': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    /**
     * 获取状态标签
     */
    getStatusLabel(status) {
      const option = this.statusOptions.find(item => item.value === status)
      return option ? option.label : status || '--'
    },
    
    /**
     * 获取计费周期标签
     */
    getBillingCycleLabel(cycle) {
      const cycleMap = {
        'monthly': '月度',
        'quarterly': '季度',
        'yearly': '年度'
      }
      return cycleMap[cycle] || cycle || '--'
    },
    
    /**
     * 新增缴费明细
     */
    addPaymentDetail() {
      mitt.emit('openPaymentDetailEditDialogAdd', this.currentObject)
    },
    
    /**
     * 编辑缴费明细
     */
    editPaymentDetail(row) {
      mitt.emit('openPaymentDetailEditDialogEdit', { ...row, paymentObject: this.currentObject })
    },
    
    /**
     * 删除缴费明细
     */
    deletePaymentDetail(id) {
      this.$confirm('删除缴费明细, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePropertyPaymentDetail(id).then(() => {
          this.searchPaymentDetails()
          this.$message.success('删除成功')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '删除失败')
        })
      }).catch(() => {})
    },
    
    /**
     * 分页变化
     */
    currentChange(num) {
      this.searchModel.pageNum = num
      this.searchPaymentDetails()
    },
    
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载缴费明细状态字典
        const statusRes = await listDictByNameEn('property_payment_detail_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))
        
        // 如果字典为空，使用默认选项
        if (this.statusOptions.length === 0) {
          this.statusOptions = [
            { value: 'active', label: '有效' },
            { value: 'inactive', label: '无效' },
            { value: 'pending', label: '待处理' },
            { value: 'expired', label: '已过期' }
          ]
        }
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用默认状态选项
        this.statusOptions = [
          { value: 'active', label: '有效' },
          { value: 'inactive', label: '无效' },
          { value: 'pending', label: '待处理' },
          { value: 'expired', label: '已过期' }
        ]
      }
    }
  },
  
  async mounted() {
    await this.initDictData()
    
    mitt.on('openPaymentDetailDialog', (paymentObject) => {
      this.currentObject = paymentObject
      this.dialog.title = `${paymentObject.moniker} - 缴费明细管理`
      this.searchModel.pageNum = 1
      this.dialog.show = true
      this.searchPaymentDetails()
    })
  },
  
  beforeUnmount() {
    mitt.off('openPaymentDetailDialog')
  }
}
</script>

<style scoped>
.payment-detail-dialog {
  border-radius: 8px;
}

.detail-container {
  padding: 0 16px;
}

.object-info {
  margin-bottom: 20px;
}

.action-bar {
  margin-bottom: 16px;
  text-align: right;
}

.detail-table {
  margin-bottom: 20px;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

/* 深色主题适配 */
.dark-theme .payment-detail-dialog {
  background-color: var(--card-background);
}
</style>
