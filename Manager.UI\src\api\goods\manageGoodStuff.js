import request from '@/utils/request'

// 分页查询好物
export const listGoodStuff = (data) =>
  request({
    url: '/manage-api/v1/good-stuff/page',
    method: 'get',
    params: data
  })

// 通过ID查询好物
export const getGoodStuff = (id) =>
  request({
    url: '/manage-api/v1/good-stuff',
    method: 'get',
    params: { id: id }
  })

// 删除好物
export const deleteGoodStuff = (id) =>
  request({
    url: '/manage-api/v1/good-stuff',
    method: 'delete',
    params: { id: id }
  })

// 通过ID查询好物订单
export const getGoodStuffOrder = (id) =>
  request({
    url: '/manage-api/v1/good-stuff/order',
    method: 'get',
    params: { id: id }
  })

// 删除好物订单
export const deleteGoodStuffOrder = (id) =>
  request({
    url: '/manage-api/v1/good-stuff/order',
    method: 'delete',
    params: { id: id }
  })

// 分页查询好物订单
export const listGoodStuffOrder = (data) =>
  request({
    url: '/manage-api/v1/good-stuff/order/page',
    method: 'get',
    params: data
  })

// 修改好物信息（包括审核状态）
export const updateGoodStuff = (data) =>
  request({
    url: '/manage-api/v1/good-stuff',
    method: 'put',
    data: data
  })

// 审核好物（审核和上架下架）
export const examineGoodStuff = (data) =>
  request({
    url: '/manage-api/v1/good-stuff/examine',
    method: 'put',
    data: data
  })
