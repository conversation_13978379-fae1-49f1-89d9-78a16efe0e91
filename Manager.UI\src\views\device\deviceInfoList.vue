<template>
  <div class="device-info-list-container">
    <device-info-edit @search="search" ref="editDialog" />
    <div class="card card--search search-flex">
      <div class="search-left">
        <el-input v-model="searchModel.deviceName" placeholder="设备名称" clearable style="width: 200px; margin-right: 16px;" />
        <el-input v-model="searchModel.deviceNo" placeholder="设备编号" clearable style="width: 200px; margin-right: 16px;" />
        <el-input v-model="searchModel.ip" placeholder="设备IP" clearable style="width: 150px; margin-right: 16px;" />
        <el-button type="primary" @click="search">搜索</el-button>
      </div>
      <div class="search-right">
        <el-button type="primary" @click="add">添加</el-button>
      </div>
    </div>
    <div class="card card--table">
      <div class="table-col">
        <el-table :data="deviceInfoList" row-key="id" style="width: 100%; height: 100%;" class="data-table">
          <el-table-column prop="id" label="ID" width="80" align="center"/>
          <el-table-column prop="deviceName" label="设备名称" min-width="150" align="center"/>
          <el-table-column prop="deviceNo" label="设备编号" min-width="120" align="center"/>
          <el-table-column prop="communityId" label="小区ID" width="100" align="center"/>
          <el-table-column prop="typeId" label="设备类型ID" width="120" align="center"/>
          <el-table-column prop="model" label="设备型号" min-width="120" align="center"/>
          <el-table-column prop="ip" label="设备IP" min-width="120" align="center"/>
          <el-table-column prop="mac" label="设备Mac" min-width="150" align="center" show-overflow-tooltip/>
          <el-table-column prop="address" label="地址" min-width="200" align="center" show-overflow-tooltip/>
          <el-table-column prop="installType" label="安装类型" width="100" align="center"/>
          <el-table-column prop="status" label="状态" width="80" align="center"/>
          <el-table-column prop="createTime" label="创建时间" width="160" align="center"/>
          <el-table-column label="操作" width="180" align="center">
            <template #default="scope">
              <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
              <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-col">
        <el-pagination background layout="prev, pager, next" @current-change="currentChange" :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
      </div>
    </div>
  </div>
</template>

<script>
import { listDevice, deleteDevice } from '@/api/deviceApi'
import { getSelectedCommunityId, hasSelectedCommunity } from '@/store/modules/options'
import mitt from '@/utils/mitt'
import deviceInfoEdit from '@/components/device/deviceInfoEdit.vue'
import communityMixin from '@/mixins/communityMixin'

export default {
  components: { deviceInfoEdit },
  mixins: [communityMixin],
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        deviceName: '',
        deviceNo: '',
        ip: ''
      },
      deviceInfoList: [],
      total: 0
    }
  },
  computed: {
    hasCurrentCommunity() {
      return hasSelectedCommunity()
    }
  },
  methods: {
    search() {
      // 添加小区ID参数
      const searchParams = {
        ...this.searchModel,
        communityId: getSelectedCommunityId()
      }

      listDevice(searchParams).then(res => {
        this.deviceInfoList = res.data.data.list
        this.total = res.data.data.total
      })
    },
    add() {
      mitt.emit('openDeviceInfoEdit', { type: 'add' })
    },
    edit(id) {
      mitt.emit('openDeviceInfoEdit', { type: 'edit', id: id })
    },
    deleted(id) {
      this.$confirm('此操作将永久删除该设备信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteDevice(id).then(() => {
          this.search()
          this.$message.success('操作成功')
        })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    /**
     * 小区变化监听方法
     */
    onCommunityChange(community) {
      // 清空列表数据
      this.deviceInfoList = []
      this.total = 0

      // 如果有选中的小区，重新加载数据
      if (community) {
        this.search()
      }
    }
  },
  created() {
    // 只有在有选中小区时才初始化数据
    if (this.hasCurrentCommunity) {
      this.search()
    }
  }
}
</script>

<style scoped>
.device-info-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.dark-theme .card {
  background-color: var(--card-background);
  border: 1px solid #444;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.search-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.search-left {
  display: flex;
  align-items: center;
}

.search-right {
  display: flex;
  align-items: center;
}
</style>
