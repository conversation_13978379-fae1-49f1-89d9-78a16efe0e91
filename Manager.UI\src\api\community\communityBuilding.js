import request from '@/utils/request'

export const listCommunityBuilding = (data) =>
	request({
		url: '/manage-api/v1/community/building/page',
		method: 'get',
		params: data
	})

export const getCommunityBuilding = (id) =>
	request({
		url: '/manage-api/v1/community/building',
		method: 'get',
		params: { id: id }
	})

export const addCommunityBuilding = (data) =>
	request({
		url: '/manage-api/v1/community/building',
		method: 'post',
		data: data
	})

export const editCommunityBuilding = (data) =>
	request({
		url: '/manage-api/v1/community/building',
		method: 'put',
		data: data
	})

export const deleteCommunityBuilding = (id) =>
	request({
		url: '/manage-api/v1/community/building',
		method: 'delete',
		params: {
			id: id
		}
	}) 