import request from '@/utils/request'

// 分页查询组织列表
export const listOrg = (data) =>
	request({
		url: '/manage-api/v1/org/page',
		method: 'get',
		params: data
	})

// 获取组织详情
export const getOrg = (id) =>
	request({
		url: '/manage-api/v1/org',
		method: 'get',
		params: { id: id }
	})

// 添加组织
export const addOrg = (data) =>
	request({
		url: '/manage-api/v1/org',
		method: 'post',
		data: data
	})

// 编辑组织
export const editOrg = (data) =>
	request({
		url: '/manage-api/v1/org',
		method: 'put',
		data: data
	})

// 删除组织
export const deleteOrg = (id) =>
	request({
		url: '/manage-api/v1/org',
		method: 'delete',
		params: { id: id }
	})

// 获取组织树形结构（用于下拉选择）
export const getOrgTree = () =>
	request({
		url: '/manage-api/v1/org/page',
		method: 'get',
		params: { pageNum: 1, pageSize: 500 } // 获取大量数据用于构建树形结构
	})

// 关联小区
export const relevCommunity = (data) =>
	request({
		url: '/manage-api/v1/org/relev-community',
		method: 'post',
		data: data
	})

// 获取组织详情（包含完整信息）
export const getOrgDetail = (id) =>
	request({
		url: '/manage-api/v1/org/detail',
		method: 'get',
		params: { id: id }
	})

// 更新组织详情
export const updateOrgDetail = (data) =>
	request({
		url: '/manage-api/v1/org/detail',
		method: 'put',
		data: data
	})

// 新增组织详情
export const addOrgDetail = (data) =>
	request({
		url: '/manage-api/v1/org/detail',
		method: 'post',
		data: data
	})

// 获取组织已关联的小区列表
export const getOrgCommunities = (orgId) =>
	request({
		url: '/manage-api/v1/org/relev-community?id=' + orgId,
		method: 'get',
		params: {  }
	})