import request from '@/utils/request'

export const listCommunityVehicle = (data) =>
	request({
		url: '/manage-api/v1/community/vehicle/page',
		method: 'get',
		params: data
	})

export const getCommunityVehicle = (id) =>
	request({
		url: '/manage-api/v1/community/vehicle',
		method: 'get',
		params: { id: id }
	})

export const addCommunityVehicle = (data) =>
	request({
		url: '/manage-api/v1/community/vehicle',
		method: 'post',
		data: data
	})

export const editCommunityVehicle = (data) =>
	request({
		url: '/manage-api/v1/community/vehicle',
		method: 'put',
		data: data
	})

export const deleteCommunityVehicle = (id) =>
	request({
		url: '/manage-api/v1/community/vehicle',
		method: 'delete',
		params: {
			id: id
		}
	}) 