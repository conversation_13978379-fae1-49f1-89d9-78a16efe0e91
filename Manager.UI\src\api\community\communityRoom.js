import request from '@/utils/request'

// 分页查询小区房间
export function listCommunityRoom(params) {
  return request({
    url: '/manage-api/v1/community/room/page',
    method: 'get',
    params: params
  })
}

// 通过ID查询小区房间
export function getCommunityRoom(id) {
  return request({
    url: '/manage-api/v1/community/room',
    method: 'get',
    params: { id: id }
  })
}

// 新增小区房间
export function addCommunityRoom(data) {
  return request({
    url: '/manage-api/v1/community/room',
    method: 'post',
    data: data
  })
}

// 编辑小区房间
export function editCommunityRoom(data) {
  return request({
    url: '/manage-api/v1/community/room',
    method: 'put',
    data: data
  })
}

// 删除小区房间
export function deleteCommunityRoom(id) {
  return request({
    url: '/manage-api/v1/community/room',
    method: 'delete',
    params: { id: id }
  })
} 