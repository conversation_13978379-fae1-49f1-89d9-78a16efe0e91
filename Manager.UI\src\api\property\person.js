import request from '@/utils/request'

// 分页查询物业人员
export function listPropertyPerson(params) {
  return request({
    url: '/manage-api/v1/property/person/page',
    method: 'get',
    params
  })
}

// 通过ID查询物业人员
export function getPropertyPerson(id) {
  return request({
    url: '/manage-api/v1/property/person',
    method: 'get',
    params: { id }
  })
}

// 新增物业人员
export function addPropertyPerson(data) {
  return request({
    url: '/manage-api/v1/property/person',
    method: 'post',
     data
  })
}

// 编辑物业人员
export function editPropertyPerson(data) {
  return request({
    url: '/manage-api/v1/property/person',
    method: 'put',
    data: data
  })
}

// 删除物业人员
export function deletePropertyPerson(id) {
  return request({
    url: '/manage-api/v1/property/person',
    method: 'delete',
    params: { id }
  })
} 