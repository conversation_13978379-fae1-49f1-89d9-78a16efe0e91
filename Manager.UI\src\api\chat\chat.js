import { baseUrl } from '@/utils/request'


// 检查内容是否为空或只有省略号
const isEmptyContent = (content) => {
    return undefined == content || !content || !content.trim() || content.trim() === '...';
};

// 使用原生fetch API处理聊天请求
export const sendChatMessage = async (message, onMessageCallback) => {
    try {
        // 构建URL
        const url = `${baseUrl}/manage-api/v1/chat?message=${encodeURIComponent(message)}`;

        // 准备认证头
        const headers = {};
        try {

            const token = window.$local?.get('smartPropertyToken')
            if (token) {
                const authData = JSON.parse(token);
                if (authData && authData.access_token) {
                    headers['Authorization'] = authData.access_token;
                }
            }
        } catch (e) {
            console.error('处理认证信息时出错:', e);
        }

        // 添加流式响应相关的请求头
        headers['Accept'] = 'text/event-stream';
        headers['Cache-Control'] = 'no-cache';
        headers['Connection'] = 'keep-alive';

        // 使用fetch发送请求
        const response = await fetch(url, {
            method: 'GET',
            headers: headers
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        while (true) {
            const { done, value } = await reader.read();
            if (done) {
                console.log('流读取完成');
                break;
            }

            // 解码数据
            const chunk = decoder.decode(value, { stream: true });
            console.log('接收到数据块:', chunk);

            try {
                // 获取要解析的JSON字符串
                let content = chunk.startsWith('data:');
                let index = chunk.indexOf('data:');
                console.log("----content：", content, index)
                const jsonStr = chunk.startsWith('data:') ? chunk.replace(/data:/g, '').trim() : chunk.trim();

                // 检查是否是特殊控制消息
                if (jsonStr === '[DONE]') {
                    console.log('接收完成');
                    continue;
                }

                // 尝试解析JSON
                try {
                    const jsonData = JSON.parse(jsonStr);
                    console.log('解析JSON数据:', jsonData);

                    // 处理空内容或只有省略号的内容
                    if (isEmptyContent(jsonData)) {
                        console.log('跳过空内容或只有省略号的内容');
                        continue; // 不发送空内容
                    }

                    onMessageCallback(jsonData.replace(/data:/g, ''));
                } catch (e) {
                    console.log("不是json直接使用原始内容", jsonStr)
                    // 如果不是JSON，直接使用原始内容，但处理特殊标记和换行符
                    if (isEmptyContent(jsonStr)) {
                        continue;
                    }
                    onMessageCallback(jsonStr);
                }
            } catch (e) {
                console.error('处理行时出错:', e, '行内容:', line);
            }

        }

        return { success: true };
    } catch (error) {
        console.error('发送消息失败:', error);

        // 提供更详细的错误信息
        let errorMessage = '发送消息失败';

        try {
            if (error && error.message) {
                errorMessage += `: ${error.message}`;
            }

            // 调用回调函数，显示错误信息
            onMessageCallback(`抱歉，${errorMessage}，请稍后再试。`);
        } catch (callbackError) {
            console.error('调用错误回调时出错:', callbackError);
        }

        // 不抛出错误，让UI继续运行
        return { success: false, error: errorMessage };
    }
};

