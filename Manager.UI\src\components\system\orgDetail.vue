<template>
  <el-dialog 
    width="800px" 
    v-loading="loading" 
    destroy-on-close 
    v-model="dialog.show" 
    :title="dialog.title"
  >
    <el-form
      :rules="rules"
      ref="formRef"
      :model="orgDetailModel"
      label-width="100px"
      class="org-detail-form"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="组织编码" prop="orgCode">
            <el-input v-model="orgDetailModel.orgCode" placeholder="请输入组织编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="orgDetailModel.sort"
              placeholder="排序"
              :min="0"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="法人姓名" prop="legalPersonName">
            <el-input v-model="orgDetailModel.legalPersonName" placeholder="请输入法人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="法人电话" prop="legalPersonPhone">
            <el-input v-model="orgDetailModel.legalPersonPhone" placeholder="请输入法人电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="orgDetailModel.email" placeholder="请输入邮箱地址" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <el-input v-model="orgDetailModel.address" placeholder="请输入地址" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="备注" prop="note">
            <el-input
              v-model="orgDetailModel.note"
              type="textarea"
              placeholder="请输入备注"
              :rows="2"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="扩展数据" prop="extentData">
            <el-input
              v-model="orgDetailModel.extentData"
              type="textarea"
              placeholder="请输入扩展数据（JSON格式）"
              :rows="2"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="媒体文件" prop="media">
            <el-upload
              class="media-uploader"
              :action="uploadUrl"
              :show-file-list="true"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeUpload"
              multiple
              accept="image/*,video/*"
            >
              <el-button size="small">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持图片和视频，单个文件不超过50MB
                </div>
              </template>
            </el-upload>
            <div v-if="mediaList.length > 0" class="media-preview">
              <div v-for="(item, index) in mediaList" :key="index" class="media-item">
                <img v-if="isImage(item)" :src="getMediaUrl(item)" class="preview-image" />
                <video v-else :src="getMediaUrl(item)" class="preview-video" controls></video>
                <el-button
                  type="danger"
                  size="mini"
                  @click="removeMedia(index)"
                  class="remove-btn"
                >
                  删除
                </el-button>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16" v-if="orgDetailModel.id">
        <el-col :span="12">
          <el-form-item label="创建时间">
            <el-input :value="formatDate(orgDetailModel.createTime)" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="更新时间">
            <el-input :value="formatDate(orgDetailModel.updateTime)" readonly />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="loading">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { getOrgDetail, updateOrgDetail, addOrgDetail } from '@/api/system/org'
import mitt from '@/utils/mitt'

export default {
  name: 'OrgDetail',
  data() {
    return {
      loading: false,
      dialog: {
        show: false,
        title: '组织详情'
      },
      orgDetailModel: {
        id: null,
        orgCode: '',
        address: '',
        note: '',
        legalPersonName: '',
        legalPersonPhone: '',
        email: '',
        media: '',
        sort: 0,
        orgId: null,
        extentData: ''
      },
      mediaList: [],
      uploadUrl: import.meta.env.VITE_BASE_API + "/common-api/v1/file/upload",
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      rules: {
        orgCode: [
          { required: true, message: '请输入组织编码', trigger: 'blur' }
        ],
        legalPersonPhone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 提交表单
    onSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (!valid) return

        this.loading = true
        
        // 处理媒体文件
        this.orgDetailModel.media = this.mediaList.join(',')
        
        const apiCall = this.orgDetailModel.id ? 
          updateOrgDetail(this.orgDetailModel) : 
          addOrgDetail(this.orgDetailModel)

        apiCall
          .then(() => {
            this.$message.success(this.orgDetailModel.id ? '修改成功' : '添加成功')
            this.dialog.show = false
            this.$emit('success')
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '操作失败')
          })
          .finally(() => {
            this.loading = false
          })
      })
    },

    // 文件上传成功
    handleUploadSuccess(response) {
      if (response.code === 200) {
        this.mediaList.push(response.data)
        this.$message.success('文件上传成功')
      } else {
        this.$message.error(response.message || '文件上传失败')
      }
    },

    // 文件上传失败
    handleUploadError() {
      this.$message.error('文件上传失败')
    },

    // 上传前检查
    beforeUpload(file) {
      const isValidSize = file.size / 1024 / 1024 < 50
      if (!isValidSize) {
        this.$message.error('文件大小不能超过50MB')
      }
      return isValidSize
    },

    // 移除媒体文件
    removeMedia(index) {
      this.mediaList.splice(index, 1)
    },

    // 判断是否为图片
    isImage(filename) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
      return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext))
    },

    // 获取媒体文件URL
    getMediaUrl(filename) {
      return process.env.VUE_APP_IMG_SERVER + filename
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 重置表单
    resetForm() {
      this.orgDetailModel = {
        id: null,
        orgCode: '',
        address: '',
        note: '',
        legalPersonName: '',
        legalPersonPhone: '',
        email: '',
        media: '',
        sort: 0,
        orgId: null,
        extentData: ''
      }
      this.mediaList = []
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate()
      }
    }
  },

  mounted() {
    // 监听打开组织详情事件
    mitt.on('openOrgDetail', (orgId) => {
      this.resetForm()
      this.orgDetailModel.orgId = orgId
      this.dialog.show = true
      
      if (orgId) {
        this.loading = true
        getOrgDetail(orgId)
          .then(res => {
            if (res.data.data) {
              this.orgDetailModel = { ...res.data.data }
              // 处理媒体文件
              if (this.orgDetailModel.media) {
                this.mediaList = this.orgDetailModel.media.split(',').filter(item => item.trim())
              }
            }
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '获取组织详情失败')
          })
          .finally(() => {
            this.loading = false
          })
      }
    })
  },

  beforeUnmount() {
    mitt.off('openOrgDetail')
  }
}
</script>

<style scoped>
.org-detail-form {
  padding: 0;
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__inner) {
  border-radius: 4px;
}

:deep(.el-textarea__inner) {
  border-radius: 4px;
}

:deep(.el-input-number) {
  width: 100%;
}

/* 文件上传样式 */
.media-uploader {
  width: 100%;
}

:deep(.el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

:deep(.el-upload:hover) {
  border-color: #409eff;
}

:deep(.el-upload__tip) {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
}

/* 媒体预览样式 */
.media-preview {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.media-item {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  width: 80px;
  height: 60px;
}

.preview-image,
.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.remove-btn {
  position: absolute;
  top: 2px;
  right: 2px;
  padding: 2px 4px;
  font-size: 10px;
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.media-item:hover .remove-btn {
  opacity: 1;
}

/* 弹窗样式 */
:deep(.el-dialog__header) {
  background: #409eff;
  color: white;
  padding: 16px 20px;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 500;
}

:deep(.el-dialog__close) {
  color: white;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

/* 底部按钮样式 */
.dialog-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #dcdfe6;
  margin-top: 16px;
}

/* 只读字段样式 */
:deep(.el-input.is-disabled .el-input__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-dialog__body) {
    padding: 16px;
  }

  .media-preview {
    gap: 6px;
  }

  .media-item {
    width: 60px;
    height: 45px;
  }
}
</style>
