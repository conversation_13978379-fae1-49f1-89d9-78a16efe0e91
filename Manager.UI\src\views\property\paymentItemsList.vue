<template>
  <div class="payment-items-list-container">
    <div class="payment-items-list-content">
      <payment-items-edit @search="search" ref="editDialog" />
      <div class="card card--search search-flex">
        <div class="search-left">
          <el-input v-model="searchModel.paymentItemName" placeholder="缴费项目名称" clearable style="width: 200px; margin-right: 16px;" />
          <el-select v-model="searchModel.communityId" placeholder="选择小区" clearable style="width: 200px; margin-right: 16px;" filterable>
            <el-option
              v-for="item in communityList"
              :key="item.id"
              :label="item.communityName"
              :value="item.id" />
          </el-select>
          <el-button type="primary" @click="search">搜索</el-button>
        </div>
        <div class="search-right">
          <el-button type="primary" @click="add">添加</el-button>
        </div>
      </div>
      <div class="card card--table">
        <div class="table-col">
          <el-table :data="paymentItemsList" row-key="id" style="width: 100%; height: 100%;" class="data-table">
            <el-table-column prop="id" label="ID" width="80" align="center"/>
            <el-table-column prop="paymentItemName" label="缴费项目名称" align="center" min-width="150"/>
            <el-table-column prop="paymentItemDescribe" label="项目描述" align="center" min-width="200" show-overflow-tooltip/>
            <el-table-column prop="unitPrice" label="单价(元)" align="center" width="100">
              <template #default="scope">
                <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.unitPrice }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" align="center" width="80"/>
            <el-table-column prop="billingCycle" label="计费周期" align="center" width="100">
              <template #default="scope">
                <span>{{ getBillingCycleLabel(scope.row.billingCycle) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="isActive" label="状态" align="center" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.isActive ? 'success' : 'danger'" size="small">
                  {{ scope.row.isActive ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="communityName" label="所属小区" align="center" min-width="120"/>
            <el-table-column prop="createTime" label="创建时间" align="center" width="160"/>
            <el-table-column prop="updateTime" label="修改时间" align="center" width="160"/>
            <el-table-column label="操作" width="180">
              <template #default="scope">
                <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
                <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination background layout="prev, pager, next" @current-change="currentChange" 
            :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listPropertyPaymentItems, deletePropertyPaymentItems, getPropertyPaymentItems } from '@/api/property/paymentItems'
import { listCommunity } from '@/api/community/community'
import mitt from '@/utils/mitt'
import paymentItemsEdit from '@/components/property/paymentItemsEdit.vue'

export default {
  components: { paymentItemsEdit },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        paymentItemName: '',
        communityId: ''
      },
      paymentItemsList: [],
      communityList: [],
      total: 0
    }
  },
  methods: {
    search() {
      listPropertyPaymentItems(this.searchModel).then(res => {
        this.paymentItemsList = res.data.data.list
        this.total = res.data.data.total
      })
    },
    loadCommunityList() {
      listCommunity({ pageNum: 1, pageSize: 500 }).then(res => {
        this.communityList = res.data.data.list || []
      })
    },
    add() {
      mitt.emit('openPaymentItemsAdd')
    },
    edit(id) {
      getPropertyPaymentItems(id).then(res => {
        mitt.emit('openPaymentItemsEdit', res.data.data)
      })
    },
    deleted(id) {
      this.$confirm('删除缴费项目, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePropertyPaymentItems(id).then(res => {
          this.search()
          this.$message.success('操作成功')
        })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    /**
     * 获取计费周期标签
     */
    getBillingCycleLabel(cycle) {
      const cycleMap = {
        'monthly': '月度',
        'quarterly': '季度',
        'yearly': '年度'
      }
      return cycleMap[cycle] || cycle || '--'
    }
  },
  created() {
    this.loadCommunityList()
    this.search()
  }
}
</script>

<style scoped>
::v-deep .--el-table-index {
  z-index: 9999 !important;
}

.payment-items-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.payment-items-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}
.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.dark-theme .card {
  background-color: var(--card-background);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}
.search-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.search-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.search-right {
  display: flex;
  align-items: center;
}
</style> 