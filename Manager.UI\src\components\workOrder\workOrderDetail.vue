<template>
  <el-dialog
    v-model="dialog.show"
    title="工单详情"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
    class="work-order-detail-dialog"
  >
    <div class="work-order-detail" v-if="workOrderData">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="工单ID">
          {{ workOrderData.id }}
        </el-descriptions-item>
        <el-descriptions-item label="工单类型">
          <el-tag :type="getTypeTagType(workOrderData.type)">
            {{ formatType(workOrderData.type) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="工单状态">
          <el-tag :type="getStatusTagType(workOrderData.status)">
            {{ formatStatus(workOrderData.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="区域类型">
          {{ formatRegionType(workOrderData.regionType) }}
        </el-descriptions-item>
        <!-- 住户姓名 - 只在区域类型不是"区域"时显示 -->
        <el-descriptions-item
          v-if="workOrderData.regionType !== 'region'"
          label="住户姓名"
          :span="2">
          {{ workOrderData.residentName || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="区域信息" :span="2">
          {{ workOrderData.region }}
        </el-descriptions-item>
        <el-descriptions-item label="用户描述" :span="2">
          <div class="description-text">
            {{ workOrderData.userDescribe }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="处理描述" :span="2">
          <div class="description-text">
            {{ workOrderData.personDescribe || '暂无处理描述' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ workOrderData.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ workOrderData.updateTime }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 媒体文件展示 -->
      <div v-if="mediaFiles.length > 0" class="content-section media-section">
        <div class="section-header">
          <h4 class="section-title">
            <el-icon class="section-icon"><Picture /></el-icon>
            媒体文件
          </h4>
          <el-tag size="small" type="info">{{ mediaFiles.length }} 个文件</el-tag>
        </div>
        <div class="media-grid">
          <div
            v-for="(file, index) in mediaFiles"
            :key="index"
            class="media-item"
            @click="previewMedia(file, index)"
          >
            <div class="media-wrapper">
              <img
                v-if="isImage(file)"
                :src="file"
                :alt="`图片${index + 1}`"
                class="media-thumbnail"
              />
              <div v-else class="video-thumbnail">
                <el-icon size="32"><VideoPlay /></el-icon>
                <span>视频文件</span>
              </div>
              <div class="media-overlay">
                <el-icon><ZoomIn /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工单处理按钮区域 -->
      <div class="work-order-actions" v-if="showActionButtons">
        <div class="action-buttons">
          <!-- 待处理状态 -->
          <template v-if="workOrderData.status === 'wait_process'">
            <el-button type="primary" @click="handleAccept">受理工单</el-button>
            <el-button type="danger" @click="handleCancel">取消工单</el-button>
          </template>

          <!-- 已受理状态 -->
          <template v-else-if="workOrderData.status === 'accepted'">
            <el-button type="primary" @click="handleProcess">处理工单</el-button>
            <el-button type="danger" @click="handleCancel">取消工单</el-button>
          </template>

          <!-- 处理中状态 -->
          <template v-else-if="workOrderData.status === 'processing'">
            <el-button type="warning" @click="handlePending">挂起工单</el-button>
            <el-button type="success" @click="handleComplete">完成工单</el-button>
          </template>

          <!-- 已挂起状态 -->
          <template v-else-if="workOrderData.status === 'pending'">
            <el-button type="primary" @click="handleProcess">恢复处理</el-button>
            <el-button type="success" @click="handleComplete">完成工单</el-button>
          </template>
        </div>
      </div>

      <!-- 处理进度时间线 -->
      <div class="content-section timeline-section" v-if="processedTimeLine && processedTimeLine.length > 0">
        <div class="section-header">
          <h4 class="section-title">
            <el-icon class="section-icon"><Clock /></el-icon>
            处理进度
          </h4>
          <el-tag size="small" type="success">{{ processedTimeLine.length }} 条记录</el-tag>
        </div>
        <div class="progress-timeline">
          <div
            v-for="(item, index) in processedTimeLine"
            :key="item.id"
            :class="['timeline-item', { 'last': index === processedTimeLine.length - 1 }]"
          >
            <!-- 时间线节点圆点 -->
            <div :class="['timeline-dot', `status-${item.status}`]">
              <el-icon class="timeline-dot-icon">
                <Check v-if="item.status === 'completed'" />
                <Clock v-else-if="item.status === 'processing'" />
                <Warning v-else-if="item.status === 'pending'" />
                <Close v-else-if="item.status === 'cancelled'" />
                <Operation v-else />
              </el-icon>
            </div>

            <!-- 时间线内容 -->
            <div class="timeline-content">
              <div class="timeline-header">
                <div class="timeline-action">{{ item.statusText }}</div>
                <div class="timeline-time">{{ item.formattedTime }}</div>
              </div>
              <div class="timeline-operator">
                <el-icon><User /></el-icon>
                {{ item.operatorName }}
              </div>
              <div v-if="item.note" class="timeline-remark">
                <el-icon><Document /></el-icon>
                {{ item.note }}
              </div>

              <!-- 时间线图片 -->
              <div v-if="item.imageList && item.imageList.length > 0" class="timeline-images">
                <div class="images-header">
                  <el-icon><Picture /></el-icon>
                  <span>相关图片 ({{ item.imageList.length }})</span>
                </div>
                <div class="images-grid">
                  <el-image
                    v-for="(image, imgIndex) in item.imageList"
                    :key="imgIndex"
                    :src="getTimelineImageUrl(image)"
                    :preview-src-list="item.imageList.map(img => getTimelineImageUrl(img))"
                    :initial-index="imgIndex"
                    fit="cover"
                    class="timeline-image"
                    preview-teleported
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <span>加载失败</span>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImageViewer"
      :url-list="imageUrls"
      :initial-index="currentImageIndex"
      @close="closeImageViewer"
      :z-index="9999"
    />

    <!-- 视频预览弹窗 -->
    <el-dialog
      v-model="videoDialog.show"
      title="视频预览"
      width="80%"
      :close-on-click-modal="false"
      append-to-body
    >
      <video
        v-if="videoDialog.url"
        :src="videoDialog.url"
        controls
        style="width: 100%; max-height: 500px;"
      >
        您的浏览器不支持视频播放
      </video>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
    <!-- 受理工单弹窗 -->
    <el-dialog
      v-model="acceptDialog.show"
      title="受理工单"
      width="900px"
      :close-on-click-modal="false"
    >
      <el-form :model="acceptForm" :rules="acceptRules" ref="acceptFormRef" label-width="100px">
        <el-form-item label="分配人员" prop="personIds">
          <div class="staff-selection-container">
            <!-- 已选择员工显示 - 始终显示，避免布局跳动 -->
            <div class="selected-staff-tags">
              <div class="selected-staff-header">
                <span class="selected-title">已选择员工</span>
                <span class="selected-count">{{ acceptForm.personIds.length }} 人</span>
              </div>
              <div class="selected-staff-content">
                <template v-if="acceptForm.personIds.length > 0">
                  <el-tag
                    v-for="personId in acceptForm.personIds"
                    :key="personId"
                    closable
                    @close="removeSelectedPerson(personId)"
                    type="primary"
                    class="selected-staff-tag"
                  >
                    {{ getPersonDisplayName(personId) }}
                  </el-tag>
                </template>
                <span v-else class="no-selected-tip">请从下方列表中选择员工</span>
              </div>
            </div>

            <!-- 员工选择列表 -->
            <div class="staff-list-container">
              <div class="staff-list-header">
                <span>可分配员工列表</span>
                <span class="available-count">共 {{ formattedPersonList.length }} 人可选</span>
              </div>

              <div class="staff-list" v-if="formattedPersonList.length > 0">
                <div
                  v-for="person in formattedPersonList"
                  :key="person.id"
                  :class="['staff-item', { 'selected': acceptForm.personIds.includes(person.id) }]"
                  @click="togglePersonSelection(person.id)"
                >
                  <div class="staff-checkbox">
                    <el-checkbox
                      :model-value="acceptForm.personIds.includes(person.id)"
                      @change="togglePersonSelection(person.id)"
                    />
                  </div>

                  <div class="staff-info">
                    <!-- 第一行：姓名 + 工号 -->
                    <div class="staff-name-line">
                      <span class="staff-name">{{ person.name }}</span>
                      <span v-if="person.employeeId" class="staff-employee-id">{{ person.employeeId }}</span>
                    </div>

                    <!-- 第二行：组织 | 职位 -->
                    <div class="staff-org-line">
                      <span class="staff-organization">{{ person.organization }}</span>
                      <span class="divider">|</span>
                      <span class="staff-position">{{ person.position }}</span>
                    </div>

                    <!-- 第三行：性别 | 年龄 + 状态 -->
                    <div class="staff-status-line">
                      <span class="staff-basic-info">{{ person.gender }} | {{ person.age }}岁</span>
                      <div class="staff-status-tags">
                        <el-tag
                          :type="getPersonStatusType(person.status)"
                          size="small"
                        >
                          {{ person.personStatusText }}
                        </el-tag>
                        <el-tag
                          :type="getWorkStatusType(person.workStatus)"
                          size="small"
                        >
                          {{ person.workStatusText }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="no-staff">
                <el-empty description="暂无可分配员工" />
              </div>

              <!-- 加载状态 -->
              <div v-if="acceptDialog.loading" class="loading-staff">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>正在加载员工数据...</span>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="受理备注" prop="note">
          <el-input
            v-model="acceptForm.note"
            type="textarea"
            :rows="3"
            placeholder="请输入受理备注"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="acceptDialog.show = false">取消</el-button>
        <el-button type="primary" @click="submitAccept" :loading="acceptDialog.loading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 处理工单弹窗 -->
    <el-dialog
      v-model="processDialog.show"
      title="处理工单"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="processForm" :rules="processRules" ref="processFormRef" label-width="100px">
        <el-form-item label="处理备注" prop="note">
          <el-input
            v-model="processForm.note"
            type="textarea"
            :rows="3"
            placeholder="请输入处理备注"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="预计时长" prop="minute">
          <el-input-number
            v-model="processForm.minute"
            :min="1"
            :max="9999"
            placeholder="预计处理时长（分钟）"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="processDialog.show = false">取消</el-button>
        <el-button type="primary" @click="submitProcess" :loading="processDialog.loading">确定</el-button>
      </template>
    </el-dialog>
    <!-- 挂起工单弹窗 -->
    <el-dialog
      v-model="pendingDialog.show"
      title="挂起工单"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="pendingForm" :rules="pendingRules" ref="pendingFormRef" label-width="100px">
        <el-form-item label="挂起原因" prop="note">
          <el-input
            v-model="pendingForm.note"
            type="textarea"
            :rows="3"
            placeholder="请输入挂起原因"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="相关图片">
          <el-upload
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="handlePendingUploadSuccess"
            :on-error="handleUploadError"
            :file-list="pendingFileList"
            list-type="picture-card"
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="pendingDialog.show = false">取消</el-button>
        <el-button type="primary" @click="submitPending" :loading="pendingDialog.loading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 完成工单弹窗 -->
    <el-dialog
      v-model="completeDialog.show"
      title="完成工单"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="completeForm" :rules="completeRules" ref="completeFormRef" label-width="100px">
        <el-form-item label="完成备注" prop="note">
          <el-input
            v-model="completeForm.note"
            type="textarea"
            :rows="3"
            placeholder="请输入完成备注"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="实际时长" prop="minute">
          <el-input-number
            v-model="completeForm.minute"
            :min="1"
            :max="9999"
            placeholder="实际处理时长（分钟）"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="完成图片">
          <el-upload
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="handleCompleteUploadSuccess"
            :on-error="handleUploadError"
            :file-list="completeFileList"
            list-type="picture-card"
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="completeDialog.show = false">取消</el-button>
        <el-button type="primary" @click="submitComplete" :loading="completeDialog.loading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 取消工单弹窗 -->
    <el-dialog
      v-model="cancelDialog.show"
      title="取消工单"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="cancelForm" :rules="cancelRules" ref="cancelFormRef" label-width="100px">
        <el-form-item label="取消原因" prop="note">
          <el-input
            v-model="cancelForm.note"
            type="textarea"
            :rows="3"
            placeholder="请输入取消原因"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelDialog.show = false">取消</el-button>
        <el-button type="primary" @click="submitCancel" :loading="cancelDialog.loading">确定</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script>
import {
  WORK_ORDER_TYPES,
  REGION_TYPES,
  WORK_ORDER_STATUS,
  acceptWorkOrder,
  processWorkOrder,
  pendingWorkOrder,
  completeWorkOrder,
  cancelWorkOrder,
  getWorkOrderPersons
} from '@/api/workOrder'
import { listDictByNameEn } from '@/api/system/dict'
import { listCommunityResident } from '@/api/community/communityResident'
import { getSelectedCommunityId } from '@/store/modules/options'
import {
  VideoPlay,
  Picture,
  Clock,
  Check,
  Warning,
  Close,
  Operation,
  User,
  Document,
  ZoomIn,
  Plus,
  Loading
} from '@element-plus/icons-vue'
import mitt from '@/utils/mitt'

export default {
  name: 'WorkOrderDetail',
  components: {
    VideoPlay,
    Picture,
    Clock,
    Check,
    Warning,
    Close,
    Operation,
    User,
    Document,
    ZoomIn,
    Plus,
    Loading
  },
  data() {
    return {
      dialog: {
        show: false
      },
      workOrderData: null,
      mediaFiles: [],
      // 处理进度时间线
      processedTimeLine: [],
      // 媒体文件配置
      imgServer: import.meta.env.VITE_BASE_API + '/common-api/v1/file/',
      // 字典选项
      typeOptions: [],
      statusOptions: [],
      regionTypeOptions: [],
      // 图片预览
      showImageViewer: false,
      imageUrls: [],
      currentImageIndex: 0,
      // 视频预览
      videoDialog: {
        show: false,
        url: ''
      },

      // 工单处理相关数据
      personList: [], // 原始人员列表
      formattedPersonList: [], // 格式化后的人员列表
      orgTreeData: [], // 组织树数据
      positionList: [], // 职位列表
      genderDict: [], // 性别字典
      personStatusDict: [], // 员工状态字典
      workStatusDict: [], // 工作状态字典
      uploadUrl: import.meta.env.VITE_BASE_API + '/common-api/v1/file/upload',
      uploadHeaders: {
        'Authorization': 'Bearer ' + (window.$local?.get('access_token') || '')
      },

      // 受理工单弹窗
      acceptDialog: {
        show: false,
        loading: false
      },
      acceptForm: {
        personIds: [],
        note: ''
      },
      acceptRules: {
        personIds: [
          { required: true, message: '请选择分配人员', trigger: 'change' }
        ],
        note: [
          { required: true, message: '请输入受理备注', trigger: 'blur' }
        ]
      },

      // 处理工单弹窗
      processDialog: {
        show: false,
        loading: false
      },
      processForm: {
        note: '',
        minute: null
      },
      processRules: {
        note: [
          { required: true, message: '请输入处理备注', trigger: 'blur' }
        ]
      },

      // 挂起工单弹窗
      pendingDialog: {
        show: false,
        loading: false
      },
      pendingForm: {
        note: '',
        media: ''
      },
      pendingRules: {
        note: [
          { required: true, message: '请输入挂起原因', trigger: 'blur' }
        ]
      },
      pendingFileList: [],

      // 完成工单弹窗
      completeDialog: {
        show: false,
        loading: false
      },
      completeForm: {
        note: '',
        minute: null,
        media: ''
      },
      completeRules: {
        note: [
          { required: true, message: '请输入完成备注', trigger: 'blur' }
        ]
      },
      completeFileList: [],

      // 取消工单弹窗
      cancelDialog: {
        show: false,
        loading: false
      },
      cancelForm: {
        note: ''
      },
      cancelRules: {
        note: [
          { required: true, message: '请输入取消原因', trigger: 'blur' }
        ]
      }
    }
  },

  computed: {
    /**
     * 是否显示操作按钮
     */
    showActionButtons() {
      if (!this.workOrderData || !this.workOrderData.status) {
        return false
      }

      // 只有未完成和未取消的工单才显示操作按钮
      const status = this.workOrderData.status
      return ['wait_process', 'accepted', 'processing', 'pending'].includes(status)
    }
  },

  methods: {
    /**
     * 关闭弹窗
     */
    handleClose() {
      this.dialog.show = false
      this.workOrderData = null
      this.mediaFiles = []
      this.closeImageViewer()
      this.videoDialog.show = false
    },

    /**
     * 处理媒体文件
     */
    processMediaFiles(media) {
      if (!media) {
        this.mediaFiles = []
        return
      }
      // 分割逗号分隔的文件路径，并构建完整URL
      this.mediaFiles = media.split(',')
        .map(filePath => filePath.trim())
        .filter(Boolean)
        .map(filePath => {
          // 如果已经是完整URL，直接返回
          if (filePath.startsWith('http')) {
            return filePath
          }
          // 否则拼接imgServer前缀
          return this.imgServer + filePath
        })
    },

    /**
     * 判断是否为图片
     */
    isImage(url) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
      return imageExtensions.some(ext => url.toLowerCase().includes(ext))
    },

    /**
     * 预览媒体文件
     */
    previewMedia(url, _index) {
      if (this.isImage(url)) {
        // 图片预览
        this.imageUrls = this.mediaFiles.filter(file => this.isImage(file))
        this.currentImageIndex = this.imageUrls.indexOf(url)
        this.showImageViewer = true
      } else {
        // 视频预览
        this.videoDialog.url = url
        this.videoDialog.show = true
      }
    },

    /**
     * 关闭图片预览
     */
    closeImageViewer() {
      this.showImageViewer = false
      this.imageUrls = []
      this.currentImageIndex = 0
    },

    /**
     * 格式化工单类型
     */
    formatType(type) {
      const option = this.typeOptions.find(item => item.value === type)
      return option ? option.label : type
    },

    /**
     * 格式化工单状态
     */
    formatStatus(status) {
      const option = this.statusOptions.find(item => item.value === status)
      return option ? option.label : status
    },

    /**
     * 格式化区域类型
     */
    formatRegionType(regionType) {
      const option = this.regionTypeOptions.find(item => item.value === regionType)
      return option ? option.label : regionType
    },

    /**
     * 获取类型标签样式
     */
    getTypeTagType(type) {
      const typeMap = {
        'repair': 'warning',
        'complaint': 'danger',
        'suggestion': 'success',
        'other': 'info'
      }
      return typeMap[type] || 'info'
    },

    /**
     * 获取状态标签样式
     */
    getStatusTagType(status) {
      const statusMap = {
        'wait_process': 'warning',
        'processing': 'primary',
        'completed': 'success',
        'cancel': 'info'
      }
      return statusMap[status] || 'info'
    },

    /**
     * 获取住户姓名
     */
    async getResidentName(residentId) {
      if (!residentId) return '无'

      try {
        const communityId = getSelectedCommunityId()
        if (!communityId) return '无'

        const response = await listCommunityResident({
          communityId: communityId,
          pageNum: 1,
          pageSize: 500
        })

        if (response.data.code === 0 && response.data.data) {
          const resident = response.data.data.find(r => r.residentId === residentId)
          return resident ? resident.residentName : '无'
        }
      } catch (err) {
        console.error('获取住户姓名失败:', err)
      }

      return '无'
    },

    /**
     * 处理时间线数据
     */
    processTimeLine(timeLine) {
      if (!timeLine || !Array.isArray(timeLine)) return []

      return timeLine.map(item => {
        // 1. 确定操作员名称
        let operatorName = '系统'
        if (item.personName) {
          operatorName = item.personName        // 物业人员（直接字段）
        } else if (item.personId && this.workOrderData && this.workOrderData.personList) {
          // 根据personId从personList中匹配处理人姓名
          const person = this.workOrderData.personList.find(p => p.id === item.personId)
          if (person && person.personName) {
            operatorName = person.personName
          }
        } else if (item.residentName) {
          operatorName = item.residentName      // 住户
        }

        // 2. 获取状态显示文本
        const statusText = this.formatStatus(item.status) || '状态变更'

        // 3. 处理图片字段 - 支持逗号分隔的多图
        const imageList = item.media ?
          item.media.split(',').filter(img => img.trim()) : []

        return {
          ...item,
          operatorName: operatorName,
          statusText: statusText,
          formattedTime: this.formatTimelineTime(item.createTime),
          imageList: imageList
        }
      }).sort((a, b) => new Date(b.createTime) - new Date(a.createTime)) // 按时间倒序
    },

    /**
     * 格式化时间线时间
     */
    formatTimelineTime(timeStr) {
      if (!timeStr) return ''

      const date = new Date(timeStr)
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

      const diffDays = Math.floor((today - itemDate) / (1000 * 60 * 60 * 24))
      const timeFormat = date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })

      if (diffDays === 0) {
        return `今天 ${timeFormat}`           // 今天 14:30
      } else if (diffDays === 1) {
        return `昨天 ${timeFormat}`           // 昨天 14:30
      } else if (diffDays < 7) {
        return `${diffDays}天前 ${timeFormat}` // 3天前 14:30
      } else {
        return date.toLocaleDateString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })                                   // 01-15 14:30
      }
    },

    /**
     * 获取时间线图片URL
     */
    getTimelineImageUrl(imageName) {
      if (!imageName) return ''
      if (imageName.startsWith('http')) return imageName
      return `${this.imgServer}${imageName}`
    },

    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载工单类型字典
        const typeRes = await listDictByNameEn('work_order_type')
        this.typeOptions = (typeRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载工单状态字典
        const statusRes = await listDictByNameEn('work_order_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载区域类型字典
        const regionRes = await listDictByNameEn('region_type')
        this.regionTypeOptions = (regionRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 如果字典数据为空，使用本地常量
        if (this.typeOptions.length === 0) {
          this.typeOptions = WORK_ORDER_TYPES
        }
        if (this.statusOptions.length === 0) {
          this.statusOptions = WORK_ORDER_STATUS
        }
        if (this.regionTypeOptions.length === 0) {
          this.regionTypeOptions = REGION_TYPES
        }
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用本地常量作为备选
        this.typeOptions = WORK_ORDER_TYPES
        this.statusOptions = WORK_ORDER_STATUS
        this.regionTypeOptions = REGION_TYPES
      }
    },

    // ==================== 工单处理相关方法 ====================

    /**
     * 受理工单
     */
    async handleAccept() {
      this.resetAcceptForm()
      this.acceptDialog.show = true
      this.acceptDialog.loading = true

      try {
        // 加载可分配人员列表
        await this.loadPersonList()
      } finally {
        this.acceptDialog.loading = false
      }
    },

    /**
     * 处理工单
     */
    handleProcess() {
      this.resetProcessForm()
      this.processDialog.show = true
    },

    /**
     * 挂起工单
     */
    handlePending() {
      this.resetPendingForm()
      this.pendingDialog.show = true
    },

    /**
     * 完成工单
     */
    handleComplete() {
      this.resetCompleteForm()
      this.completeDialog.show = true
    },

    /**
     * 取消工单
     */
    handleCancel() {
      this.resetCancelForm()
      this.cancelDialog.show = true
    },

    /**
     * 加载可分配人员列表
     */
    async loadPersonList() {
      try {
        const communityId = getSelectedCommunityId()
        if (!communityId) {
          this.$message.error('请先选择小区')
          return
        }

        // 先加载基础数据（组织、职位、字典）
        await Promise.all([
          this.loadOrgTreeData(),
          this.loadPositionData(),
          this.loadDictData()
        ])

        // 然后加载员工数据
        const personResponse = await getWorkOrderPersons({
          communityId: communityId,
          type: this.workOrderData.type,
          pageNum: 1,
          pageSize: 100
        })

        if (personResponse.data.code === 0) {
          this.personList = personResponse.data.data || []
          // 确保基础数据已加载完成后再格式化员工数据
          this.formatPersonList()
        } else {
          this.$message.error(personResponse.data.message || '加载人员列表失败')
        }
      } catch (error) {
        console.error('加载人员列表失败:', error)
        this.$message.error('加载人员列表失败')
      }
    },

    /**
     * 提交受理工单
     */
    async submitAccept() {
      try {
        await this.$refs.acceptFormRef.validate()

        this.acceptDialog.loading = true

        const response = await acceptWorkOrder({
          id: this.workOrderData.id,
          personIds: this.acceptForm.personIds,
          note: this.acceptForm.note
        })

        if (response.data.code === 0) {
          this.$message.success('工单受理成功')
          this.acceptDialog.show = false
          this.refreshWorkOrderDetail()
        } else {
          this.$message.error(response.data.message || '受理失败')
        }
      } catch (error) {
        console.error('受理工单失败:', error)
        this.$message.error('受理工单失败')
      } finally {
        this.acceptDialog.loading = false
      }
    },

    /**
     * 提交处理工单
     */
    async submitProcess() {
      try {
        await this.$refs.processFormRef.validate()

        this.processDialog.loading = true

        const response = await processWorkOrder({
          id: this.workOrderData.id,
          note: this.processForm.note,
          minute: this.processForm.minute
        })

        if (response.data.code === 0) {
          this.$message.success('工单处理成功')
          this.processDialog.show = false
          this.refreshWorkOrderDetail()
        } else {
          this.$message.error(response.data.message || '处理失败')
        }
      } catch (error) {
        console.error('处理工单失败:', error)
        this.$message.error('处理工单失败')
      } finally {
        this.processDialog.loading = false
      }
    },

    /**
     * 提交挂起工单
     */
    async submitPending() {
      try {
        await this.$refs.pendingFormRef.validate()

        this.pendingDialog.loading = true

        const response = await pendingWorkOrder({
          id: this.workOrderData.id,
          note: this.pendingForm.note,
          media: this.pendingForm.media
        })

        if (response.data.code === 0) {
          this.$message.success('工单挂起成功')
          this.pendingDialog.show = false
          this.refreshWorkOrderDetail()
        } else {
          this.$message.error(response.data.message || '挂起失败')
        }
      } catch (error) {
        console.error('挂起工单失败:', error)
        this.$message.error('挂起工单失败')
      } finally {
        this.pendingDialog.loading = false
      }
    },

    /**
     * 提交完成工单
     */
    async submitComplete() {
      try {
        await this.$refs.completeFormRef.validate()

        this.completeDialog.loading = true

        const response = await completeWorkOrder({
          id: this.workOrderData.id,
          note: this.completeForm.note,
          minute: this.completeForm.minute,
          media: this.completeForm.media
        })

        if (response.data.code === 0) {
          this.$message.success('工单完成成功')
          this.completeDialog.show = false
          this.refreshWorkOrderDetail()
        } else {
          this.$message.error(response.data.message || '完成失败')
        }
      } catch (error) {
        console.error('完成工单失败:', error)
        this.$message.error('完成工单失败')
      } finally {
        this.completeDialog.loading = false
      }
    },

    /**
     * 提交取消工单
     */
    async submitCancel() {
      try {
        await this.$refs.cancelFormRef.validate()

        this.cancelDialog.loading = true

        const response = await cancelWorkOrder({
          id: this.workOrderData.id,
          note: this.cancelForm.note
        })

        if (response.data.code === 0) {
          this.$message.success('工单取消成功')
          this.cancelDialog.show = false
          this.refreshWorkOrderDetail()
        } else {
          this.$message.error(response.data.message || '取消失败')
        }
      } catch (error) {
        console.error('取消工单失败:', error)
        this.$message.error('取消工单失败')
      } finally {
        this.cancelDialog.loading = false
      }
    },

    // ==================== 辅助方法 ====================

    /**
     * 刷新工单详情
     */
    async refreshWorkOrderDetail() {
      if (this.workOrderData && this.workOrderData.id) {
        try {
          const { getWorkOrder } = await import('@/api/workOrder')
          const response = await getWorkOrder(this.workOrderData.id)

          if (response.data.code === 0) {
            this.workOrderData = response.data.data
            this.processMediaFiles(this.workOrderData.media)

            // 处理时间线数据
            if (this.workOrderData.timeLine) {
              this.processedTimeLine = this.processTimeLine(this.workOrderData.timeLine)
            }
          }
        } catch (error) {
          console.error('刷新工单详情失败:', error)
        }
      }
    },

    /**
     * 重置受理表单
     */
    resetAcceptForm() {
      this.acceptForm = {
        personIds: [],
        note: ''
      }
      if (this.$refs.acceptFormRef) {
        this.$refs.acceptFormRef.resetFields()
      }
    },

    /**
     * 重置处理表单
     */
    resetProcessForm() {
      this.processForm = {
        note: '',
        minute: null
      }
      if (this.$refs.processFormRef) {
        this.$refs.processFormRef.resetFields()
      }
    },

    /**
     * 重置挂起表单
     */
    resetPendingForm() {
      this.pendingForm = {
        note: '',
        media: ''
      }
      this.pendingFileList = []
      if (this.$refs.pendingFormRef) {
        this.$refs.pendingFormRef.resetFields()
      }
    },

    /**
     * 重置完成表单
     */
    resetCompleteForm() {
      this.completeForm = {
        note: '',
        minute: null,
        media: ''
      }
      this.completeFileList = []
      if (this.$refs.completeFormRef) {
        this.$refs.completeFormRef.resetFields()
      }
    },

    /**
     * 重置取消表单
     */
    resetCancelForm() {
      this.cancelForm = {
        note: ''
      }
      if (this.$refs.cancelFormRef) {
        this.$refs.cancelFormRef.resetFields()
      }
    },

    /**
     * 文件上传前验证
     */
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!')
        return false
      }
      return true
    },

    /**
     * 挂起工单图片上传成功
     */
    handlePendingUploadSuccess(response, _file, fileList) {
      if (response.code === 0) {
        const filePaths = fileList.map(item => {
          if (item.response?.data) {
            return item.response.data
          }
          if (item.url && item.url.startsWith(this.imgServer)) {
            return item.url.replace(this.imgServer, '')
          }
          return item.url
        }).filter(Boolean)

        this.pendingForm.media = filePaths.join(',')
      }
    },

    /**
     * 完成工单图片上传成功
     */
    handleCompleteUploadSuccess(response, _file, fileList) {
      if (response.code === 0) {
        const filePaths = fileList.map(item => {
          if (item.response?.data) {
            return item.response.data
          }
          if (item.url && item.url.startsWith(this.imgServer)) {
            return item.url.replace(this.imgServer, '')
          }
          return item.url
        }).filter(Boolean)

        this.completeForm.media = filePaths.join(',')
      }
    },

    /**
     * 文件上传失败
     */
    handleUploadError(error) {
      console.error('文件上传失败:', error)
      this.$message.error('文件上传失败')
    },

    // ==================== 员工处理相关方法 ====================

    /**
     * 加载组织树数据
     */
    async loadOrgTreeData() {
      try {
        const communityId = getSelectedCommunityId()
        const { listCommunityOrg } = await import('@/api/community/communityOrg')
        const response = await listCommunityOrg({
          communityId: communityId,
          pageNum: 1,
          pageSize: 1000
        })

        if (response.data.code === 0) {
          this.orgTreeData = response.data.data.list || []
          console.log('组织数据加载完成:', this.orgTreeData.length, this.orgTreeData)
        }
      } catch (error) {
        console.error('加载组织数据失败:', error)
      }
    },

    /**
     * 加载职位数据
     */
    async loadPositionData() {
      try {
        const { listPosition } = await import('@/api/system/position')
        const response = await listPosition({
          pageNum: 1,
          pageSize: 1000
        })

        if (response.data.code === 0) {
          this.positionList = response.data.data.list || []
          console.log('职位数据加载完成:', this.positionList.length, this.positionList)
        }
      } catch (error) {
        console.error('加载职位数据失败:', error)
      }
    },

    /**
     * 加载字典数据
     */
    async loadDictData() {
      try {
        const { listDictByNameEn } = await import('@/api/system/dict')

        // 并行加载所有需要的字典
        const [genderResponse, personStatusResponse, workStatusResponse] = await Promise.all([
          listDictByNameEn('gender'),      // 性别字典
          listDictByNameEn('person_status'),     // 员工状态字典
          listDictByNameEn('person_work_status')        // 工作状态字典
        ])

        if (genderResponse.data.code === 0) {
          this.genderDict = genderResponse.data.data || []
          console.log('性别字典加载完成:', this.genderDict.length)
        }

        if (personStatusResponse.data.code === 0) {
          
          this.personStatusDict = personStatusResponse.data.data || []
          console.log('员工状态字典加载完成:', this.personStatusDict.length)
        }

        if (workStatusResponse.data.code === 0) {
          this.workStatusDict = workStatusResponse.data.data || []
          console.log('工作状态字典加载完成:', this.workStatusDict.length)
        }
      } catch (error) {
        console.error('加载字典数据失败:', error)
      }
    },

    /**
     * 格式化员工列表数据
     */
    formatPersonList() {
      
      console.log('开始格式化员工数据:', {
        personCount: this.personList.length,
        orgCount: this.orgTreeData.length,
        positionCount: this.positionList.length
      })

      this.formattedPersonList = this.personList.map(person => {
        const formattedPerson = {
          id: person.id,
          name: person.personName || '',
          employeeId: person.personNumber || '',
          phone: person.phone || '',
          age: person.age || 0,
          gender: this.getGenderText(person.gender),
          organization: this.getOrgName(person.orgId),
          position: this.getPositionName(person.positionId),
          personStatus: person.status,
          personStatusText: this.getPersonStatusText(person.status),
          workStatus: person.workStatus,
          workStatusText: this.getWorkStatusText(person.workStatus)
        }

        console.log('格式化员工:', {
          原始: {
            name: person.personName,
            orgId: person.orgId,
            positionId: person.positionId,
            status: person.status,
            workStatus: person.workStatus,
            gender: person.gender
          },
          格式化: {
            name: formattedPerson.name,
            organization: formattedPerson.organization,
            position: formattedPerson.position,
            personStatusText: formattedPerson.personStatusText,
            workStatusText: formattedPerson.workStatusText,
            gender: formattedPerson.gender
          }
        })

        return formattedPerson
      })

      console.log('员工数据格式化完成:', this.formattedPersonList.length)
    },

    /**
     * 获取性别文本
     */
    getGenderText(gender) {
      if (!gender) return '未知'

      // 如果有字典数据，优先使用字典匹配
      if (this.genderDict.length > 0) {
        const genderItem = this.genderDict.find(item =>
          item.nameEn === gender || item.dictValue === gender
        )
        if (genderItem) {
          return genderItem.nameCn || genderItem.dictLabel
        }
      }

      // 降级处理：直接映射常见值
      const genderMap = {
        'man': '男',
        'woman': '女',
        'male': '男',
        'female': '女',
        '1': '男',
        '2': '女'
      }

      return genderMap[gender] || '未知'
    },

    /**
     * 获取组织名称
     */
    getOrgName(orgId) {
      debugger
      if (!orgId) return '未分配'
      if (!this.orgTreeData.length) {
        console.warn('组织数据未加载，orgId:', orgId)
        return '未分配'
      }

      const org = this.orgTreeData.find(item => item.id === orgId)
      if (!org) {
        console.warn('未找到组织，orgId:', orgId, '可用组织:', this.orgTreeData.map(o => ({id: o.id, name: o.orgName})))
        return '未知组织'
      }

      return org.orgName || org.name || '未知组织'
    },

    /**
     * 获取职位名称
     */
    getPositionName(positionId) {
      if (!positionId) return '未分配'
      if (!this.positionList.length) {
        console.warn('职位数据未加载，positionId:', positionId)
        return '未分配'
      }

      const position = this.positionList.find(item => item.id === positionId)
      if (!position) {
        console.warn('未找到职位，positionId:', positionId, '可用职位:', this.positionList.map(p => ({id: p.id, name: p.positionName})))
        return '未知职位'
      }

      return position.positionName || position.name || '未知职位'
    },

    /**
     * 获取员工状态文本
     */
    getPersonStatusText(status) {
      if (!status) return '未知'

      // 如果有字典数据，优先使用字典匹配
      if (this.personStatusDict.length > 0) {
        const statusItem = this.personStatusDict.find(item =>
          item.nameEn === status || item.dictValue === status
        )
        if (statusItem) {
          return statusItem.nameCn || statusItem.dictLabel
        }
      }

      // 降级处理：直接映射常见值
      const statusMap = {
        'active': '在职',
        'inactive': '离职',
        'probation': '试用期',
        'leave': '请假',
        'resigned': '离职'
      }

      return statusMap[status] || '未知'
    },

    /**
     * 获取工作状态文本
     */
    getWorkStatusText(status) {
      if (!status) return '未知'

      // 如果有字典数据，优先使用字典匹配
      if (this.workStatusDict.length > 0) {
        const statusItem = this.workStatusDict.find(item =>
          item.nameEn === status || item.dictValue === status
        )
        if (statusItem) {
          return statusItem.nameCn || statusItem.dictLabel
        }
      }

      // 降级处理：直接映射常见值
      const statusMap = {
        'idle': '空闲',
        'busy': '忙碌',
        'leave': '请假',
        'free': '空闲',
        'working': '工作中'
      }

      return statusMap[status] || '未知'
    },

    /**
     * 获取员工状态标签类型
     */
    getPersonStatusType(status) {
      
      if (!status) return 'info'

      // 如果有字典数据，优先使用字典的cssClass
      if (this.personStatusDict.length > 0) {
        const statusItem = this.personStatusDict.find(item =>
          item.nameEn === status || item.dictValue === status
        )

        if (statusItem && statusItem.cssClass) {
          // 根据cssClass颜色值映射到Element Plus的标签类型
          const colorMap = {
            '#67c23a': 'success',  // 绿色
            '#e6a23c': 'warning',  // 黄色
            '#f56c6c': 'danger',   // 红色
            '#909399': 'info'      // 灰色
          }
          return colorMap[statusItem.cssClass] || 'info'
        }
      }

      // 降级处理：根据状态值直接映射
      const typeMap = {
        'active': 'success',    // 在职 - 绿色
        'inactive': 'danger',   // 离职 - 红色
        'probation': 'warning', // 试用期 - 黄色
        'leave': 'info',        // 请假 - 灰色
        'resigned': 'danger'    // 离职 - 红色
      }

      return typeMap[status] || 'info'
    },

    /**
     * 获取工作状态标签类型
     */
    getWorkStatusType(status) {
      if (!status) return 'info'

      // 如果有字典数据，优先使用字典的cssClass
      if (this.workStatusDict.length > 0) {
        const statusItem = this.workStatusDict.find(item =>
          item.nameEn === status || item.dictValue === status
        )

        if (statusItem && statusItem.cssClass) {
          // 根据cssClass颜色值映射到Element Plus的标签类型
          const colorMap = {
            '#67c23a': 'success',  // 绿色
            '#e6a23c': 'warning',  // 黄色
            '#f56c6c': 'danger',   // 红色
            '#909399': 'info'      // 灰色
          }
          return colorMap[statusItem.cssClass] || 'info'
        }
      }

      // 降级处理：根据状态值直接映射
      const typeMap = {
        'idle': 'success',      // 空闲 - 绿色
        'busy': 'warning',      // 忙碌 - 黄色
        'leave': 'info',        // 请假 - 灰色
        'free': 'success',      // 空闲 - 绿色
        'working': 'warning'    // 工作中 - 黄色
      }

      return typeMap[status] || 'info'
    },

    /**
     * 切换员工选择状态
     */
    togglePersonSelection(personId) {
      const index = this.acceptForm.personIds.indexOf(personId)
      if (index > -1) {
        this.acceptForm.personIds.splice(index, 1)
      } else {
        this.acceptForm.personIds.push(personId)
      }
    },

    /**
     * 移除已选择的员工
     */
    removeSelectedPerson(personId) {
      const index = this.acceptForm.personIds.indexOf(personId)
      if (index > -1) {
        this.acceptForm.personIds.splice(index, 1)
      }
    },

    /**
     * 获取员工显示名称
     */
    getPersonDisplayName(personId) {
      const person = this.formattedPersonList.find(p => p.id === personId)
      if (!person) return '未知员工'

      return person.employeeId ?
        `${person.name} (${person.employeeId})` :
        person.name
    }
  },

  async mounted() {
    await this.initDictData()

    // 监听详情事件
    mitt.on('openWorkOrderDetail', async (workOrder) => {
      this.workOrderData = { ...workOrder }
      this.processMediaFiles(workOrder.media)

      // 处理时间线数据
      if (workOrder.timeLine) {
        this.processedTimeLine = this.processTimeLine(workOrder.timeLine)
      } else {
        this.processedTimeLine = []
      }

      // 如果有住户ID且区域类型不是"区域"，获取住户姓名
      if (workOrder.residentId && workOrder.regionType !== 'region') {
        const residentName = await this.getResidentName(workOrder.residentId)
        this.workOrderData.residentName = residentName
      }

      this.dialog.show = true
    })
  },

  beforeUnmount() {
    mitt.off('openWorkOrderDetail')
  }
}
</script>

<style scoped>
/* 弹窗整体样式 */
.work-order-detail-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.work-order-detail {
  padding: 0;
}

/* 内容区块样式 */
.content-section {
  margin-top: 24px;
  padding: 20px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.content-section:first-of-type {
  margin-top: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e4e7ed;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.section-icon {
  margin-right: 8px;
  color: #409eff;
}

/* 媒体文件样式 */
.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 16px;
}

.media-item {
  position: relative;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.media-wrapper {
  position: relative;
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.media-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.media-wrapper:hover .media-overlay {
  opacity: 1;
}

.media-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 12px;
}

.video-thumbnail span {
  margin-top: 8px;
  font-weight: 500;
}

.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
  font-size: 20px;
}

/* 时间线样式 */
.progress-timeline {
  position: relative;
}

.timeline-item {
  position: relative;
  padding-left: 60px;
  padding-bottom: 32px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 40px;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #e4e7ed, #f5f7fa);
}

.timeline-item.last::before {
  display: none;
}

.timeline-dot {
  position: absolute;
  left: 0;
  top: 8px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: white;
  border: 3px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.timeline-dot-icon {
  font-size: 16px;
  color: #909399;
}

/* 不同状态的圆点样式 */
.timeline-dot.status-wait_process {
  border-color: #ff9800;
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
}
.timeline-dot.status-wait_process .timeline-dot-icon { color: #ff9800; }

.timeline-dot.status-accepted {
  border-color: #2196f3;
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}
.timeline-dot.status-accepted .timeline-dot-icon { color: #2196f3; }

.timeline-dot.status-processing {
  border-color: #2196f3;
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}
.timeline-dot.status-processing .timeline-dot-icon { color: #2196f3; }

.timeline-dot.status-pending {
  border-color: #ff9800;
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
}
.timeline-dot.status-pending .timeline-dot-icon { color: #ff9800; }

.timeline-dot.status-completed {
  border-color: #4caf50;
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
}
.timeline-dot.status-completed .timeline-dot-icon { color: #4caf50; }

.timeline-dot.status-cancelled {
  border-color: #9e9e9e;
  background: linear-gradient(135deg, #f5f5f5, #eeeeee);
}
.timeline-dot.status-cancelled .timeline-dot-icon { color: #9e9e9e; }

.timeline-content {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

.timeline-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.timeline-action {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.timeline-time {
  font-size: 13px;
  color: #909399;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.timeline-operator {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.timeline-operator .el-icon {
  margin-right: 6px;
  color: #909399;
}

.timeline-remark {
  display: flex;
  align-items: flex-start;
  font-size: 14px;
  color: #606266;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  margin-top: 12px;
  border-left: 3px solid #409eff;
}

.timeline-remark .el-icon {
  margin-right: 8px;
  margin-top: 2px;
  color: #409eff;
  flex-shrink: 0;
}

.timeline-images {
  margin-top: 16px;
  padding: 12px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.images-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.images-header .el-icon {
  margin-right: 6px;
  color: #909399;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.timeline-image {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-error {
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa, #e4e7ed);
  color: #909399;
  font-size: 11px;
  border-radius: 6px;
  border: 1px dashed #d3d4d6;
}

.image-error .el-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.description-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .work-order-detail-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }

  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .timeline-item {
    padding-left: 50px;
  }

  .timeline-dot {
    width: 32px;
    height: 32px;
  }

  .timeline-dot-icon {
    font-size: 14px;
  }
}

/* 图片预览器样式调整 */
:deep(.el-image-viewer__wrapper) {
  z-index: 9999 !important;
}

/* 工单处理按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-buttons .el-button {
  margin-left: 0;
  margin-right: 10px;
}

.action-buttons .el-button:last-child {
  margin-right: 0;
}

/* 上传组件样式 */
.el-upload--picture-card {
  width: 100px;
  height: 100px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

/* 员工选择相关样式 */
.staff-selection-container {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background-color: #fafafa;
  width: 100%;
}

.selected-staff-tags {
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f8f9fa;
  min-height: 60px;
}

.selected-staff-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

.selected-title {
  color: #303133;
}

.selected-count {
  color: #409eff;
  font-size: 12px;
}

.selected-staff-content {
  min-height: 24px;
}

.selected-staff-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.no-selected-tip {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.staff-list-container {
  max-height: 320px;
  overflow-y: auto;
  width: 100%;
}

.staff-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
}

.selected-count {
  color: #409eff;
  font-size: 12px;
}

.staff-list {
  padding: 8px;
}

.staff-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 12px;
  margin-bottom: 6px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.staff-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.staff-item.selected {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.staff-checkbox {
  margin-right: 12px;
  margin-top: 2px;
}

.staff-info {
  flex: 1;
  min-width: 0;
}

.staff-name-line {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.staff-name {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  margin-right: 8px;
}

.staff-employee-id {
  font-size: 11px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 1px 4px;
  border-radius: 2px;
}

.staff-org-line {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 13px;
  color: #606266;
}

.staff-organization {
  color: #409eff;
}

.divider {
  margin: 0 8px;
  color: #dcdfe6;
}

.staff-position {
  color: #67c23a;
}

.staff-status-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.staff-basic-info {
  font-size: 11px;
  color: #909399;
}

.staff-status-tags {
  display: flex;
  gap: 3px;
}

.staff-status-tags .el-tag {
  font-size: 10px;
  height: 18px;
  line-height: 16px;
  padding: 0 4px;
}

.no-staff {
  padding: 40px 20px;
  text-align: center;
}

.loading-staff {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

.loading-staff .el-icon {
  margin-right: 8px;
  font-size: 16px;
}
</style>
