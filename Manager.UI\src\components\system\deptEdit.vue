<template>
    <el-dialog width="50%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
        <el-form :rules="rules" ref="form" :model="deptModel" label-width="120px">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="部门名称" prop="deptName">
                        <el-input v-model="deptModel.deptName" placeholder="部门名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="排序" prop="sort">
                        <el-input-number v-model="deptModel.sort" placeholder="排序" :min="0"
                            style="width: 100%;"></el-input-number>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="所属组织" prop="orgId">
                        <el-cascader v-model="deptModel.orgId" :options="orgTree" :props="orgProps"
                            placeholder="请选择组织" style="width: 100%;" clearable filterable :show-all-levels="false">
                        </el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="上级部门" prop="parentId">
                        <el-cascader v-model="deptModel.parentId" :options="deptTree" :props="deptProps"
                            placeholder="请选择部门" style="width: 100%;" clearable filterable :show-all-levels="false">
                        </el-cascader>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialog.show = false">取消</el-button>
                <el-button type="primary" @click="onSubmit">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import { addDept, editDept, getDeptTree } from '@/api/system/dept'
import { getOrgTree } from '@/api/system/org'
import mitt from '@/utils/mitt'

export default {
    name: 'DeptEdit',
    data() {
        return {
            loading: false,
            deptModel: {
                id: 0,
                deptName: '',
                sort: 0,
                parentId: 0,
                orgId: null
            },
            dialog: {
                show: false,
                title: ''
            },
            orgTree: [],
            deptTree: [],
            orgProps: {
                value: 'id',
                label: 'orgName',
                children: 'children',
                emitPath: false,
                checkStrictly: true,
                expandTrigger: 'click'
            },
            deptProps: {
                value: 'id',
                label: 'deptName',
                children: 'children',
                emitPath: false,
                checkStrictly: true,
                expandTrigger: 'click'
            },
            rules: {
                deptName: [
                    { required: true, message: '请输入部门名称', trigger: 'blur' }
                ],
                orgId: [
                    { required: true, message: '请选择组织', trigger: 'change' }
                ],
                sort: [
                    { type: 'number', message: '排序必须为数字', trigger: 'blur' }
                ]
            }
        }
    },
    methods: {
        onSubmit() {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    this.loading = true
                    const submitData = { ...this.deptModel }
                    if (!submitData.id) {
                        // 添加部门
                        addDept(submitData)
                            .then(() => {
                                this.$message.success('操作成功')
                                this.$emit('search')
                                this.dialog.show = false
                            })
                            .catch(err => {
                                this.$message.error(err.data.errorMessage)
                            })
                            .finally(() => {
                                this.loading = false
                            })
                    } else {
                        // 编辑部门
                        editDept(submitData)
                            .then(() => {
                                this.$message.success('操作成功')
                                this.$emit('search')
                                this.dialog.show = false
                            })
                            .catch(err => {
                                this.$message.error(err.data.errorMessage)
                            })
                            .finally(() => {
                                this.loading = false
                            })
                    }
                }
            })
        }
    },
    mounted() {
        // 加载组织树和部门树
        getOrgTree()
            .then(res => {
                this.orgTree = res.data.data.list || res.data.data
            })
            .catch(() => { })
        getDeptTree()
            .then(res => {
                this.deptTree = res.data.data.list || res.data.data
            })
            .catch(() => { })

        // 打开编辑
        mitt.on('openDeptEdit', dept => {
            this.deptModel = { ...dept }
            this.dialog.title = '编辑部门'
            this.dialog.show = true
        })
        // 打开添加
        mitt.on('openDeptAdd', parentId => {
            this.deptModel = {
                id: 0,
                deptName: '',
                sort: 0,
                parentId: parentId || 0,
                orgId: null
            }
            this.dialog.title = '添加部门'
            this.dialog.show = true
        })
    },
    beforeUnmount() {
        mitt.off('openDeptEdit')
        mitt.off('openDeptAdd')
    }
}
</script>

<style scoped>
.dialog-footer {
    text-align: right;
}
</style>