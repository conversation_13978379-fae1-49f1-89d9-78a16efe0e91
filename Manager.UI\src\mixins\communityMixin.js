/**
 * 简化的小区变化监听混入
 *
 * 使用方式：
 * 1. 在组件中定义 onCommunityChange 方法来处理小区变化
 * 2. 直接导入 getSelectedCommunityId() 等方法使用
 */

import {
  addCommunityChangeListener,
  removeCommunityChangeListener
} from '@/store/modules/options'

export default {
  data() {
    return {
      // 小区变化监听器引用
      _communityChangeListener: null
    }
  },

  created() {
    // 创建小区变化监听器
    this._communityChangeListener = (community) => {
      // 如果组件定义了 onCommunityChange 方法，则调用它
      if (typeof this.onCommunityChange === 'function') {
        this.onCommunityChange(community)
      }
    }

    // 注册监听器
    addCommunityChangeListener(this._communityChangeListener)
  },

  beforeUnmount() {
    // Vue 3 的生命周期钩子
    if (this._communityChangeListener) {
      removeCommunityChangeListener(this._communityChangeListener)
    }
  },

  beforeDestroy() {
    // Vue 2 的生命周期钩子（向后兼容）
    if (this._communityChangeListener) {
      removeCommunityChangeListener(this._communityChangeListener)
    }
  }
}
