<template>
  <div class="person-list-container">
    <div class="person-list-content">
      <person-edit @search="search" ref="editDialog" />
      <person-detail ref="detailDialog" />
      <div class="card card--search search-flex">
        <el-input v-model="searchModel.personName" placeholder="人员姓名" clearable style="width: 200px; margin-right: 16px;" />
        <el-input v-model="searchModel.phone" placeholder="手机号" clearable style="width: 200px; margin-right: 16px;" />
        <el-input v-model="searchModel.duty" placeholder="职位" clearable style="width: 200px; margin-right: 16px;" />
        <el-select v-model="searchModel.status" placeholder="状态" clearable style="width: 200px; margin-right: 16px;">
          <el-option label="在职" value="active" />
          <el-option label="离职" value="inactive" />
          <el-option label="试用" value="trial" />
        </el-select>
        <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
        <el-button type="primary" @click="add">添加</el-button>
      </div>
      <div class="card card--table">
        <div class="table-col">
          <el-table :data="personList" row-key="id" style="width: 100%; height: 100%;" class="data-table">
            <el-table-column prop="id" label="ID" width="80" align="center"/>
            <el-table-column prop="personName" label="姓名" width="120" align="center"/>
            <el-table-column prop="gender" label="性别" width="80" align="center">
              <template #default="scope">
                <span>{{ formatGender(scope.row.gender) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="手机号" width="130" align="center"/>
            <el-table-column prop="duty" label="职位" width="120" align="center"/>
            <el-table-column prop="number" label="人员编号" width="100" align="center"/>
            <el-table-column prop="certificateType" label="证件类型" width="100" align="center">
              <template #default="scope">
                <span>{{ formatCertificateType(scope.row.certificateType) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="qualification" label="学历" width="100" align="center">
              <template #default="scope">
                <span>{{ formatQualification(scope.row.qualification) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="salary" label="薪资" width="100" align="center">
              <template #default="scope">
                <span v-if="scope.row.salary">{{ scope.row.salary.toFixed(2) }}</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="media" label="照片" align="center" width="80">
              <template #default="scope">
                <el-image v-if="scope.row.media && getImageUrl(scope.row.media)"
                  :src="getImageUrl(scope.row.media)"
                  style="width: 40px; height: 40px; border-radius: 50%;"
                  fit="cover"
                  :preview-src-list="[getImageUrl(scope.row.media)]"/>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="entryTime" label="入职时间" width="120" align="center"/>
            <el-table-column prop="createTime" label="创建时间" width="160" align="center"/>
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="scope">
                <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
                <el-button type="text" size="mini" @click="viewDetail(scope.row)">详情</el-button>
                <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination background layout="prev, pager, next" @current-change="currentChange"
            :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listPropertyPerson, deletePropertyPerson, getPropertyPerson } from '@/api/property/person'
import mitt from '@/utils/mitt'
import personEdit from '@/components/property/personEdit.vue'
import personDetail from '@/components/property/personDetail.vue'

export default {
  components: { personEdit, personDetail },
  data() {
    return {
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        personName: '',
        phone: '',
        duty: '',
        status: ''
      },
      personList: [],
      total: 0
    }
  },
  methods: {
    search() {
      listPropertyPerson(this.searchModel).then(res => {
        this.personList = res.data.data.list
        this.total = res.data.data.total
      })
    },
    add() {
      mitt.emit('openPersonAdd')
    },
    edit(id) {
      getPropertyPerson(id).then(res => {
        mitt.emit('openPersonEdit', res.data.data)
      })
    },
    deleted(id) {
      this.$confirm('删除物业人员, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePropertyPerson(id).then(() => {
          this.search()
          this.$message.success('操作成功')
        })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },
    getStatusType(status) {
      const statusMap = {
        'active': 'success',
        'inactive': 'danger',
        'trial': 'warning'
      }
      return statusMap[status] || 'info'
    },
    getStatusText(status) {
      const statusMap = {
        'active': '在职',
        'inactive': '离职',
        'trial': '试用'
      }
      return statusMap[status] || status
    },
    getImageUrl(media) {
      if (!media) return null
      try {
        // 如果是新格式（直接是文件路径字符串）
        if (typeof media === 'string' && !media.startsWith('{')) {
          return this.imgServer + media
        }
        // 如果是旧格式（JSON对象）
        const mediaObj = JSON.parse(media)
        return mediaObj.face_url ? this.imgServer + mediaObj.face_url : null
      } catch (e) {
        // 如果解析失败，尝试直接作为路径使用
        return this.imgServer + media
      }
    },

    /**
     * 格式化性别
     */
    formatGender(gender) {
      const genderMap = {
        'male': '男',
        'female': '女',
        'man': '男',
        'woman': '女'
      }
      return genderMap[gender] || gender || '--'
    },

    /**
     * 格式化证件类型
     */
    formatCertificateType(type) {
      const typeMap = {
        'id_card': '身份证',
        'passport': '护照',
        'military_id': '军官证',
        'driver_license': '驾驶证'
      }
      return typeMap[type] || type || '--'
    },

    /**
     * 格式化学历
     */
    formatQualification(qualification) {
      const qualificationMap = {
        'primary': '小学',
        'junior': '初中',
        'senior': '高中',
        'college': '大专',
        'bachelor': '本科',
        'master': '硕士',
        'doctor': '博士'
      }
      return qualificationMap[qualification] || qualification || '--'
    },

    /**
     * 查看详情
     */
    viewDetail(person) {
      mitt.emit('openPersonDetail', person)
    }
  },
  created() {
    this.search()
  }
}
</script>

<style scoped>
::v-deep .--el-table-index {
  z-index: 9999 !important;
}

.person-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.person-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}
.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.dark-theme .card {
  background-color: var(--card-background);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}
.search-flex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}
</style>