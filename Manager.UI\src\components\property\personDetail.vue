<template>
  <el-dialog
    v-model="dialog.show"
    title="物业人员详情"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="person-detail" v-if="personData">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="人员ID">
          {{ personData.id }}
        </el-descriptions-item>
        <el-descriptions-item label="姓名">
          {{ personData.personName }}
        </el-descriptions-item>
        <el-descriptions-item label="性别">
          {{ formatGender(personData.gender) }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          {{ personData.phone || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="证件类型">
          {{ formatCertificateType(personData.certificateType) }}
        </el-descriptions-item>
        <el-descriptions-item label="身份证号">
          {{ personData.idCard || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="生日">
          {{ personData.birthday || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="邮箱">
          {{ personData.email || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="应急手机号">
          {{ personData.emerPhone || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="人员编号">
          {{ personData.number || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="职位">
          {{ personData.duty || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="入职时间">
          {{ personData.entryTime || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(personData.status)">
            {{ getStatusText(personData.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="薪资">
          {{ personData.salary ? personData.salary.toFixed(2) + '元' : '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="学历">
          {{ formatQualification(personData.qualification) }}
        </el-descriptions-item>
        <el-descriptions-item label="专业">
          {{ personData.major || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="绩效">
          {{ personData.performance || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="住址" :span="2">
          {{ personData.address || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="技能" :span="2">
          {{ personData.skills || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="证书" :span="2">
          {{ personData.certificates || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          <div class="note-text">
            {{ personData.note || '--' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ personData.createTime || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ personData.updateTime || '--' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 照片展示 -->
      <div v-if="personData.media && getImageUrl(personData.media)" class="photo-section">
        <h4>照片</h4>
        <el-image
          :src="getImageUrl(personData.media)"
          style="width: 200px; height: 150px; border-radius: 8px;"
          fit="cover"
          :preview-src-list="[getImageUrl(personData.media)]"
        />
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
import mitt from '@/utils/mitt'

export default {
  name: 'PersonDetail',
  data() {
    return {
      dialog: {
        show: false
      },
      personData: null,
      imgServer: import.meta.env.VITE_BASE_API + '/common-api/v1/file/'
    }
  },
  methods: {
    /**
     * 关闭弹窗
     */
    handleClose() {
      this.dialog.show = false
      this.personData = null
    },

    /**
     * 格式化性别
     */
    formatGender(gender) {
      const genderMap = {
        'male': '男',
        'female': '女',
        'man': '男',
        'woman': '女'
      }
      return genderMap[gender] || gender || '--'
    },

    /**
     * 格式化证件类型
     */
    formatCertificateType(type) {
      const typeMap = {
        'id_card': '身份证',
        'passport': '护照',
        'military_id': '军官证',
        'driver_license': '驾驶证'
      }
      return typeMap[type] || type || '--'
    },

    /**
     * 格式化学历
     */
    formatQualification(qualification) {
      const qualificationMap = {
        'primary': '小学',
        'junior': '初中',
        'senior': '高中',
        'college': '大专',
        'bachelor': '本科',
        'master': '硕士',
        'doctor': '博士'
      }
      return qualificationMap[qualification] || qualification || '--'
    },

    /**
     * 获取状态标签样式
     */
    getStatusType(status) {
      const statusMap = {
        'active': 'success',
        'inactive': 'danger',
        'trial': 'warning'
      }
      return statusMap[status] || 'info'
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        'active': '在职',
        'inactive': '离职',
        'trial': '试用'
      }
      return statusMap[status] || status || '--'
    },

    /**
     * 获取图片URL
     */
    getImageUrl(media) {
      if (!media) return null
      try {
        // 如果是新格式（直接是文件路径字符串）
        if (typeof media === 'string' && !media.startsWith('{')) {
          return this.imgServer + media
        }
        // 如果是旧格式（JSON对象）
        const mediaObj = JSON.parse(media)
        return mediaObj.face_url ? this.imgServer + mediaObj.face_url : null
      } catch (e) {
        // 如果解析失败，尝试直接作为路径使用
        return this.imgServer + media
      }
    }
  },

  mounted() {
    // 监听详情事件
    mitt.on('openPersonDetail', (person) => {
      this.personData = person
      this.dialog.show = true
    })
  },

  beforeUnmount() {
    mitt.off('openPersonDetail')
  }
}
</script>

<style scoped>
.person-detail {
  padding: 20px 0;
}

.note-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}

.photo-section {
  margin-top: 30px;
}

.photo-section h4 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
}
</style>
