import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import {resolve } from "path"
import { viteCommonjs } from '@originjs/vite-plugin-commonjs'
function pathResolve(dir) {
	return resolve(__dirname, ".", dir);
}

export default defineConfig({
	plugins: [vue(),viteCommonjs()],
	base:"./",
	publicDir:"./static",
	server: {
		host: "0.0.0.0"
	},
	build: {
		assetsDir:"./static"
	},
	resolve: {
		alias: {
			"@": pathResolve("src"),
		}
	}
})
