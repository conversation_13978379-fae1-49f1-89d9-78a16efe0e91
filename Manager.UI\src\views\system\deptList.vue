<template>
    <div class="dept-list-container">
        <div class="dept-list-content">
            <div class="card card--search search-flex">
                <div class="search-left">
                    <el-input v-model="searchModel.deptName" placeholder="部门名称" clearable style="width: 200px; margin-right: 16px;" />
                    <el-button type="primary" @click="search">搜索</el-button>
                </div>
                <div class="search-right">
                    <el-button type="primary" @click="add(0)">添加</el-button>
                </div>
            </div>
            <div class="card card--table">
                <div class="table-col">
                    <el-table :data="deptList" row-key="id" style="width: 100%; height: 100%;" class="data-table">
                        <el-table-column prop="deptName" header-align="center" label="部门名称" min-width="200" />
                        <el-table-column prop="sort" header-align="center" align="center" label="排序" width="100" />
                        <el-table-column prop="createTime" header-align="center" align="center" label="创建时间" width="180">
                            <template #default="scope">
                                {{ formatDate(scope.row.createTime) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="updateTime" header-align="center" align="center" label="更新时间" width="180">
                            <template #default="scope">
                                {{ formatDate(scope.row.updateTime) }}
                            </template>
                        </el-table-column>
                        <el-table-column header-align="center" align="center" label="操作" width="240" fixed="right">
                            <template #default="scope">
                                <el-button type="text" size="mini" @click="add(scope.row.id)">添加子级</el-button>
                                <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
                                <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="pagination-col">
                    <el-pagination background layout="prev, pager, next" @current-change="currentChange" @prev-click="prevClick" @next-click="nextClick" :total="total"></el-pagination>
                </div>
            </div>
        </div>
        <dept-edit @search="search"></dept-edit>
    </div>
</template>

<script>
import { listDept, deleteDept, getDept } from '@/api/system/dept'
import mitt from '@/utils/mitt'
import deptEdit from '@/components/system/deptEdit.vue'

export default {
    components: { deptEdit },
    data() {
        return {
            searchModel: {
                pageNum: 1,
                pageSize: 10,
                deptName: ''
            },
            deptList: [],
            total: 0
        }
    },
    methods: {
        search() {
            listDept(this.searchModel)
                .then(res => {
                    this.deptList = res.data.data.list
                    this.total = res.data.data.total
                })
                .catch(err => {
                    this.$message.error(err.data.errorMessage)
                })
        },
        edit(id) {
            getDept(id)
                .then(res => {
                    mitt.emit('openDeptEdit', res.data.data)
                })
                .catch(err => {
                    this.$message.error(err.data.errorMessage)
                })
        },
        add(parentId) {
            mitt.emit('openDeptAdd', parentId)
        },
        deleted(id) {
            this.$confirm('删除部门, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteDept(id)
                    .then(() => {
                        this.search()
                        this.$message.success('操作成功')
                    })
                    .catch(err => {
                        this.$message.error(err.data.errorMessage)
                    })
            }).catch(() => {})
        },
        currentChange(num) {
            this.searchModel.pageNum = num
            this.search()
        },
        prevClick() {
            if (this.searchModel.pageNum > 1) {
                this.searchModel.pageNum--
                this.search()
            }
        },
        nextClick() {
            this.searchModel.pageSize++
            this.search()
        },
        formatDate(dateString) {
            if (!dateString) return ''
            const date = new Date(dateString)
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            })
        },
        async init() {
            mitt.off('openDeptEdit')
            mitt.off('openDeptAdd')
            try {
                const res = await listDept(this.searchModel)
                this.deptList = res.data.data.list
                this.total = res.data.data.total
            } catch (err) {
                this.$message.error(err.data.errorMessage)
            }
        }
    },
    created() {
        this.init()
    }
}
</script>

<style scoped>
.dept-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.dept-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.data-table {
  flex: 1;
  height: 100%;
}

.pagination-col {
  padding: 16px 0;
  text-align: center;
  flex-shrink: 0;
}

.search-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.search-left {
  display: flex;
  align-items: center;
}

.search-right {
  display: flex;
  align-items: center;
}

.card--search {
  margin-bottom: 20px;
  flex: none;
  height: auto;
  padding: 20px 20px;
  display: flex;
  align-items: center;
}

.card--table {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  margin-top: 0;
}
</style> 