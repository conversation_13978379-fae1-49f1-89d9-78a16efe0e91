<template>
  <div class="theme-switcher">
    <el-tooltip :content="tooltipContent" placement="bottom">
      <el-button
        circle
        :icon="currentIcon"
        @click="toggleTheme"
        class="theme-button"
        :class="{ 'theme-button--dark': isDarkTheme }"
        type="default"
        size="default"
        aria-label="切换主题"
      ></el-button>
    </el-tooltip>
  </div>
</template>

<script>
import { Sunny, Moon } from '@element-plus/icons-vue'

/**
 * 主题切换组件
 *
 * 提供一个按钮，允许用户在亮色和暗色主题之间切换
 */
export default {
  name: 'ThemeSwitcher',

  data() {
    return {
      // 图标组件
      Sunny,
      Moon,
      // 主题变化监听器
      themeChangeListener: null
    }
  },

  computed: {
    /**
     * 获取当前主题
     * @returns {string} 当前主题 ('light' 或 'dark')
     */
    currentTheme() {
      // 从 Pinia 获取主题
      return window.$local?.get('theme') || 'light';
    },

    /**
     * 判断是否为暗色主题
     * @returns {boolean} 如果当前是暗色主题则返回true
     */
    isDarkTheme() {
      return this.currentTheme === 'dark';
    },

    /**
     * 获取当前应显示的图标
     * @returns {Component} 亮色主题显示月亮图标，暗色主题显示太阳图标
     */
    currentIcon() {
      return this.isDarkTheme ? Sunny : Moon;
    },

    /**
     * 获取工具提示文本
     * @returns {string} 根据当前主题显示相应的提示文本
     */
    tooltipContent() {
      return this.isDarkTheme ? '切换到亮色模式' : '切换到暗色模式';
    }
  },

  methods: {
    /**
     * 切换主题
     * 调用主题模块的toggleTheme方法切换主题
     */
    toggleTheme() {
      // 直接操作 Pinia
      const currentTheme = window.$local?.get('theme') || 'light';
      const newTheme = currentTheme === 'light' ? 'dark' : 'light';
      window.$local?.setTheme(newTheme);

      // 手动应用主题
      this.applyThemeToDOM(newTheme);
    },

    /**
     * 应用主题到 DOM
     */
    applyThemeToDOM(theme) {
      document.documentElement.classList.remove('dark-theme');
      document.body.classList.remove('dark-theme');

      if (theme === 'dark') {
        document.documentElement.classList.add('dark-theme');
        document.body.classList.add('dark-theme');
      }
    },

    /**
     * 主题变化事件处理器
     * 当主题变化时更新组件状态
     * @param {CustomEvent} event - 主题变化事件
     */
    handleThemeChange(event) {
      // 可以在这里处理主题变化后的额外逻辑
      // 例如记录主题变化或执行其他操作
    }
  },

  mounted() {
    // 添加主题变化事件监听器
    this.themeChangeListener = this.handleThemeChange.bind(this);
    document.addEventListener('themechange', this.themeChangeListener);
  },

  beforeUnmount() {
    // 移除主题变化事件监听器，防止内存泄漏
    if (this.themeChangeListener) {
      document.removeEventListener('themechange', this.themeChangeListener);
      this.themeChangeListener = null;
    }
  }
}
</script>

<style scoped>
.theme-switcher {
  display: inline-block;
}

.theme-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-button:hover {
  transform: rotate(30deg);
}

.theme-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.theme-button:active::after {
  width: 100%;
  height: 100%;
}

/* 暗色主题下的主题切换按钮 */
.theme-button--dark,
:deep(.dark-theme .theme-button) {
  background-color: #444 !important;
  border-color: #555 !important;
  color: #e0e0e0 !important;
}

.theme-button--dark:hover,
:deep(.dark-theme .theme-button:hover) {
  background-color: #555 !important;
  border-color: #666 !important;
  color: #fff !important;
}

.theme-button--dark .el-icon,
.theme-button--dark i,
:deep(.dark-theme .theme-button .el-icon),
:deep(.dark-theme .theme-button i) {
  color: #e0e0e0 !important;
  background-color: transparent !important;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .theme-button {
    font-size: 16px;
    padding: 8px;
  }
}
</style>
