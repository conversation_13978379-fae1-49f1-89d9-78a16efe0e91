import axios from 'axios'
import router from '@/router'
import { tokenMonitor, eventTypes } from './tokenMonitor.js'

export var baseUrl = import.meta.env.VITE_BASE_API

// token刷新相关变量
let isRefreshing = false
let refreshSubscribers = []

// 防止死循环的控制变量
let tokenUpdateInProgress = false
let consecutiveRefreshFailures = 0
const MAX_REFRESH_FAILURES = 3

// 创建axios实例
const service = axios.create({
    baseURL: baseUrl,
    timeout: 30000
})

// 获取token
function getToken() {
    
    return window.$local?.get('smartPropertyToken')
}

// 判断token是否过期
function isTokenExpired(token) {
    // 记录 token 检查事件
    tokenMonitor.logEvent(eventTypes.TOKEN_CHECK, {
        hasToken: !!token,
        tokenLength: token ? token.length : 0
    });

    try {
        let tokenObj = JSON.parse(token);
        // 检查token对象是否有效
        if (!tokenObj || !tokenObj.access_token) {
            console.log('Token格式无效或缺少access_token');
            return true;
        }

        // 如果是新登录的token（有access_token但没有expires_time），需要获取服务器的真实过期时间
        if (!tokenObj.expires_time) {
            console.log('检测到新登录的token，需要设置过期时间');

            // 防止死循环：如果正在更新token，直接返回false
            if (tokenUpdateInProgress) {
                console.log('Token更新正在进行中，跳过重复更新');
                return false;
            }

            tokenUpdateInProgress = true;
            tokenMonitor.logEvent(eventTypes.TOKEN_UPDATE, {
                reason: 'set_expire_time_from_server',
                tokenHasExpireTime: false
            });

            try {
                // 🔧 修复：从token中的expires_in获取服务器设置的过期时间
                let expireTime;
                if (tokenObj.expires_in) {
                    // 统一转换为数字，不管是字符串还是数字
                    const serverExpireSeconds = parseInt(tokenObj.expires_in);
                    if (!isNaN(serverExpireSeconds) && serverExpireSeconds > 0) {
                        // 为了安全，提前5分钟过期
                        const safetyMarginSeconds = 5 * 60; // 5分钟安全边距
                        const actualExpireSeconds = Math.max(serverExpireSeconds - safetyMarginSeconds, 60); // 最少1分钟
                        expireTime = new Date().getTime() + (actualExpireSeconds * 1000);
                        console.log(`使用服务器过期时间: ${serverExpireSeconds}秒, 安全过期时间: ${actualExpireSeconds}秒`);
                    } else {
                        // expires_in 无效，使用默认值
                        expireTime = new Date().getTime() + (30 * 60 * 1000);
                        console.warn('expires_in 无效，使用保守默认值: 30分钟');
                    }
                } else {
                    // 如果没有expires_in，使用保守的默认值（30分钟）
                    expireTime = new Date().getTime() + (30 * 60 * 1000);
                    console.warn('未找到服务器过期时间，使用保守默认值: 30分钟');
                }

                tokenObj.expires_time = expireTime;

                // 更新 Pinia 中的 token
                window.$local?.setSmartPropertyToken(JSON.stringify(tokenObj));
                return false; // 新token不过期
            } finally {
                // 确保标志位被重置
                setTimeout(() => {
                    tokenUpdateInProgress = false;
                }, 100);
            }
        }

        let expires_time = tokenObj.expires_time;
        let currentTime = new Date().getTime();

        // 提前5分钟(300000毫秒)判断过期，避免即将过期的情况
        if (currentTime >= (expires_time - 300000)) {
            console.log('Token已过期或即将过期');
            return true;
        } else {
            return false;
        }
    } catch (error) {
        console.error('解析token时出错:', error);
        return true; // 解析出错时视为过期
    }
}

// 重新登录
function reLogin() {
    console.log('🚨 执行重新登录，清除所有数据');

    // 记录重新登录事件
    tokenMonitor.logEvent(eventTypes.RELOGIN, {
        consecutiveFailures: consecutiveRefreshFailures,
        isRefreshing: isRefreshing
    });

    // 防止重复执行
    if (tokenUpdateInProgress) {
        console.log('重新登录已在进行中，跳过重复执行');
        return;
    }

    tokenUpdateInProgress = true;

    try {
        // 重置刷新状态
        isRefreshing = false;
        refreshSubscribers = [];
        consecutiveRefreshFailures = 0;

        // 清除 Pinia 存储
        window.$local?.removeAll();

        // 跳转到登录页
        router.replace({
            path: '/login'
        });
    } finally {
        // 延迟重置标志位，确保登录页面加载完成
        setTimeout(() => {
            tokenUpdateInProgress = false;
        }, 1000);
    }
}

// 添加请求到刷新队列
function subscribeTokenRefresh(cb) {
    refreshSubscribers.push(cb)
}

// 执行刷新队列中的请求
function onRefreshed(token) {
    refreshSubscribers.map((cb) => cb(token))
    refreshSubscribers = []
}

// 刷新token
function refreshToken() {
    return new Promise((resolve, reject) => {
        // 记录刷新token事件
        tokenMonitor.logEvent(eventTypes.TOKEN_REFRESH, {
            consecutiveFailures: consecutiveRefreshFailures,
            isRefreshing: isRefreshing
        });

        try {
            // 检查连续失败次数，防止无限重试
            if (consecutiveRefreshFailures >= MAX_REFRESH_FAILURES) {
                console.error('🚨 Token刷新连续失败次数过多，停止重试');
                reLogin();
                return reject('连续刷新失败次数过多');
            }

            let token = getToken();
            if (!token) {
                console.error('刷新token失败: token不存在');
                consecutiveRefreshFailures++;
                reLogin();
                return reject('token不存在');
            }

            let tokenObj = JSON.parse(token);
            if (!tokenObj || !tokenObj.refresh_token) {
                console.error('刷新token失败: refresh_token不存在');
                consecutiveRefreshFailures++;
                reLogin();
                return reject('refresh_token不存在');
            }


            axios({
                method: 'post',
                url: baseUrl + '/manage-api/v1/auth/refresh-token?refreshToken=' + tokenObj.refresh_token, // 刷新token的接口
                headers: {
                    'content-type': 'application/json'
                },
            })
                .then((res) => {
                    if (!res.data || res.data.code !== 0 || !res.data.data) {
                        console.error('刷新token失败: 接口返回错误', res.data);
                        consecutiveRefreshFailures++;
                        reLogin();
                        return reject('刷新token接口返回错误');
                    }

                    try {
                        // 🔧 修复：统一转换expires_in为数字，不管是字符串还是数字
                        let expires_time;
                        if (res.data.data.expires_in) {
                            // 统一转换为数字，不管是字符串还是数字
                            const serverExpireSeconds = parseInt(res.data.data.expires_in);
                            if (!isNaN(serverExpireSeconds) && serverExpireSeconds > 0) {
                                // 为了安全，提前5分钟过期
                                const safetyMarginSeconds = 5 * 60; // 5分钟安全边距
                                const actualExpireSeconds = Math.max(serverExpireSeconds - safetyMarginSeconds, 60); // 最少1分钟
                                expires_time = new Date().getTime() + (actualExpireSeconds * 1000);
                                console.log(`Token刷新成功，设置过期时间: 服务器${serverExpireSeconds}秒, 实际${actualExpireSeconds}秒`);
                            } else {
                                // expires_in 无效，使用默认值
                                expires_time = new Date().getTime() + (30 * 60 * 1000);
                                console.warn('刷新响应中expires_in无效，使用默认30分钟过期时间');
                            }
                        } else {
                            // 如果没有expires_in，使用保守的默认值（30分钟）
                            expires_time = new Date().getTime() + (30 * 60 * 1000);
                            console.warn('刷新响应中未找到expires_in，使用默认30分钟过期时间');
                        }

                        let currentToken = JSON.parse(getToken()) || {};
                        currentToken.expires_time = expires_time;
                        currentToken.access_token = res.data.data.access_token;
                        currentToken.refresh_token = res.data.data.refresh_token;
                        // 保留原有的expires_in以供后续使用
                        if (res.data.data.expires_in) {
                            currentToken.expires_in = res.data.data.expires_in;
                        }

                        // 更新 Pinia 中的 token
                        window.$local?.setSmartPropertyToken(JSON.stringify(currentToken));

                        // 执行队列中的请求
                        onRefreshed(res.data.data.access_token);

                        // 刷新成功，重置失败计数
                        consecutiveRefreshFailures = 0;
                        console.log('✅ Token刷新成功');

                        resolve(res.data.data.access_token);
                    } catch (error) {
                        console.error('保存新token时出错:', error);
                        consecutiveRefreshFailures++;
                        reLogin();
                        reject(error);
                    }
                })
                .catch((err) => {
                    console.error('刷新token请求失败:', err);
                    consecutiveRefreshFailures++;

                    // 特殊处理401状态码 - refreshToken也过期了
                    if (err.response && err.response.status === 401) {
                        console.error('🚨 RefreshToken也已过期，强制跳转登录页');
                        // 清除用户信息
                        if (window.$local?.clearUserInfo) {
                            window.$local.clearUserInfo();
                        }
                    }

                    reLogin();
                    reject(err);
                });
        } catch (error) {
            console.error('刷新token过程中出错:', error);
            consecutiveRefreshFailures++;
            reLogin();
            reject(error);
        }
    });
}

// request拦截器
service.interceptors.request.use(
    (config) => {
        // 记录API请求事件
        tokenMonitor.logEvent(eventTypes.API_REQUEST, {
            url: config.url,
            method: config.method,
            hasAuth: !!config.headers?.Authorization
        });

        // 防止在重新登录过程中继续发送请求
        if (tokenUpdateInProgress && config.url && !config.url.includes('/auth/')) {
            console.log('🚫 重新登录进行中，拒绝非认证请求:', config.url);
            return Promise.reject(new Error('重新登录进行中'));
        }

        let token = getToken()

        if (token) {
            // 判断token是否过期,如果过期请求刷新token
            if (isTokenExpired(token)) {
                // 判断当前是否正在请求刷新token
                if (!isRefreshing) {
                    isRefreshing = true // 设置刷新状态，防止重复刷新
                    console.log('🔄 开始刷新token...');

                    // 刷新token
                    return refreshToken()
                        .then((newToken) => {
                            isRefreshing = false
                            config.headers['Authorization'] = newToken
                            console.log('✅ Token刷新成功，继续原请求');
                            return config
                        })
                        .catch((error) => {
                            isRefreshing = false
                            console.error('❌ Token刷新失败:', error);
                            return Promise.reject(error)
                        })
                } else {
                    // 如果正在刷新token，将请求加入队列
                    console.log('⏳ Token刷新中，请求加入队列');
                    return new Promise((resolve) => {
                        subscribeTokenRefresh((newToken) => {
                            config.headers['Authorization'] = newToken
                            resolve(config)
                        })
                    })
                }
            } else {
                // token未过期，直接添加到请求头
                try {
                    const authData = JSON.parse(token);
                    if (authData && authData.access_token) {
                        config.headers['Authorization'] = authData.access_token;
                    }
                } catch (e) {
                    console.error('处理认证信息时出错:', e);
                }
                return config
            }
        } else {
            return config
        }
    },
    (error) => {
        return Promise.reject(error)
    }
)

// response拦截器
service.interceptors.response.use(
    async response => {
        let code = response.data.code
        if (code == 401 || code == 402) {
            console.log('接口返回401/402，token过期！')
            reLogin()
            return Promise.reject(response)
        } else if (code != 0) {
            
            return Promise.reject(response)
        } else {
            return response
        }
    },
    error => {
        // 处理HTTP状态码401/402
        if (error.response && (error.response.status === 401 || error.response.status === 402)) {
            console.log('🚨 请求返回401/402，token过期！')

            // 记录401事件
            tokenMonitor.logEvent(eventTypes.API_REQUEST, {
                status: error.response.status,
                url: error.config?.url,
                isRefreshing: isRefreshing
            });

            const originalRequest = error.config;

            // 如果不是刷新token的请求，尝试刷新token
            if (!originalRequest.url.includes('/auth/refresh') && !isRefreshing) {
                isRefreshing = true;
                console.log('🔄 尝试刷新token...');

                return refreshToken()
                    .then((newToken) => {
                        isRefreshing = false;
                        console.log('✅ Token刷新成功，重新发起原请求');
                        // 更新原请求的token
                        originalRequest.headers['Authorization'] = newToken;
                        // 执行队列中的请求
                        onRefreshed(newToken);
                        // 重新发起原请求
                        return service(originalRequest);
                    })
                    .catch((refreshError) => {
                        isRefreshing = false;
                        console.error('❌ Token刷新失败，跳转登录页');
                        // 刷新失败，跳转到登录页
                        reLogin();
                        return Promise.reject(refreshError);
                    });
            } else if (isRefreshing && !originalRequest.url.includes('/auth/refresh')) {
                // 如果正在刷新token，将请求加入队列
                console.log('⏳ Token刷新中，请求加入队列');
                return new Promise((resolve) => {
                    subscribeTokenRefresh((newToken) => {
                        originalRequest.headers['Authorization'] = newToken;
                        resolve(service(originalRequest));
                    });
                });
            } else {
                // 如果是刷新token的请求失败，直接跳转登录页
                console.error('❌ 刷新token请求失败，跳转登录页');
                reLogin();
                return Promise.reject(error);
            }
        }

        // 网络错误
        if (error && error.message === 'Network Error') {
            return Promise.reject("请求超时");
        }

        // 返回错误，确保错误能被正确捕获
        return Promise.reject(error);
    }
)

export default service
