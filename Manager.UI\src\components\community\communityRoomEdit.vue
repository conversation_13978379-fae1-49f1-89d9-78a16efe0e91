<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="600px" class="room-edit-dialog">
    <el-form :model="roomModel" :rules="rules" ref="formRef" label-width="100px" class="room-edit-form">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="房间号" prop="roomNumber">
            <el-input v-model="roomModel.roomNumber" maxlength="20" placeholder="请输入房间号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单元号" prop="unitNumber">
            <el-input v-model="roomModel.unitNumber" maxlength="20" placeholder="请输入单元号" clearable />
          </el-form-item>
        </el-col>
      <el-col :span="12">
          <el-form-item label="房间户型" prop="type">
            <el-select
              v-model="roomModel.type"
              placeholder="请选择住户类型"
              style="width: 100%"
            >
              <el-option
                v-for="type in roomTypeList"
                :key="type.nameEn"
                :label="type.nameCn"
                :value="type.nameEn"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="面积(㎡)" prop="area">
            <el-input v-model="roomModel.area" 
              style="width: 100%;" placeholder="请输入房间面积" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="所属楼栋" prop="buildingId">
            <el-select v-model="roomModel.buildingId" placeholder="请选择楼栋" 
              style="width: 100%;" filterable>
              <el-option 
                v-for="item in buildingList" 
                :key="item.id" 
                :label="item.buildingNumber" 
                :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="note">
            <el-input v-model="roomModel.note" type="textarea" :rows="3" 
              maxlength="200" placeholder="请输入备注信息" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="扩展数据" prop="expandData">
            <el-input v-model="roomModel.expandData" type="textarea" :rows="2" 
              maxlength="500" placeholder="JSON格式的扩展数据" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addCommunityRoom, editCommunityRoom } from '@/api/community/communityRoom'
import { listCommunityBuilding } from '@/api/community/communityBuilding'
import mitt from '@/utils/mitt'

export default {
  name: 'communityRoomEdit',
  props: ['roomTypeList'],
  data() {
    return {
      roomModel: {},
      buildingList: [],
      dialog: {
        show: false,
        title: ''
      },
      rules: {
        roomNumber: [
          { required: true, message: '请输入房间号', trigger: 'blur' }
        ],
        buildingId: [
          { required: true, message: '请选择楼栋', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
       //获取户型标签
    getRoomTypeLabel(type) {
      const item = this.roomTypeList.find((item) => item.nameEn === type);
      return item ? item.nameCn : type;
    },
    loadBuildingList() {
      listCommunityBuilding({ pageNum: 1, pageSize: 500 }).then(res => {
        this.buildingList = res.data.data.list || []
      })
    },
    
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.roomModel.id ? editCommunityRoom : addCommunityRoom
        api(this.roomModel).then(() => {
          this.$emit('search')
          this.dialog.show = false
          this.$message.success('操作成功')
        })
      })
    }
  },
  mounted() {
    this.loadBuildingList()
    
    mitt.on('openRoomEdit', (data) => {
      this.roomModel = { ...data }
      this.dialog.show = true
      this.dialog.title = '编辑房间'
    })

    mitt.on('openRoomAdd', (buildingId = null) => {
      this.roomModel = buildingId ? { buildingId } : {}
      this.dialog.show = true
      this.dialog.title = '新增房间'
    })
  },
  beforeDestroy() {
    mitt.off('openRoomEdit')
    mitt.off('openRoomAdd')
  }
}
</script>

<style scoped>
.room-edit-dialog>>>.el-dialog__body {
  padding-top: 10px;
  padding-bottom: 0;
}

.room-edit-form {
  padding: 0 10px;
}

.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}
</style> 