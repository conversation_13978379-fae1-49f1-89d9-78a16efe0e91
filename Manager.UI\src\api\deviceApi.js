import request from '@/utils/request'
//设备信息相关------------------------------------------------------------------------
//分页查询设备信息列表
export const listDevice = (data) =>
  request({
    url: '/manage-api/v1/device/info/page',
    method: 'get',
    params: data
  })

// 列表返回的item 结构如下：
// {
// idCollapse allintegerint64
// 设备ID

// deviceNameCollapse allstring
// 设备名称

// deviceNoCollapse allstring
// 设备编号

// communityIdCollapse allintegerint64
// 小区ID

// typeIdCollapse allintegerint64
// 设备类型ID

// modelCollapse allstring
// 设备型号

// ipCollapse allstring
// 设备IP

// macCollapse allstring
// 设备Mac

// lngCollapse allnumberdouble
// 经度

// latCollapse allnumberdouble
// 纬度

// altCollapse allnumberdouble
// 海拔

// addressCollapse allstring
// 地址

// installTypeCollapse allintegerint32
// 安装类型

// noteCollapse allstring
// 描述

// sortCollapse allintegerint32
// 排序

// statusCollapse allintegerint32
// 状态

// polyCoordsCollapse allstring
// 地理围栏

// expandDataCollapse allstring
// 扩展参数

// createTimeCollapse allstringdate-time
// 创建时间

// updateTimeCollapse allstringdate-time
// 修改时间

// openidCollapse allstring
// 第三方平台openid
// }





//添加设备
export const addDevice = (data) =>
  request({
    url: '/manage-api/v1/device/info',
    method: 'post',
    data
  })

// 入参
// {

// deviceNameCollapse allstring
// 设备名称

// deviceNoCollapse allstring
// 设备编号

// communityIdCollapse allintegerint64
// 小区ID

// typeIdCollapse allintegerint64
// 设备类型ID

// modelCollapse allstring
// 设备型号

// ipCollapse allstring
// 设备IP

// macCollapse allstring
// 设备Mac

// lngCollapse allnumberdouble
// 经度

// latCollapse allnumberdouble
// 纬度

// altCollapse allnumberdouble
// 海拔

// addressCollapse allstring
// 地址

// installTypeCollapse allintegerint32
// 安装类型

// noteCollapse allstring
// 描述

// sortCollapse allintegerint32
// 排序

// statusCollapse allintegerint32
// 状态

// polyCoordsCollapse allstring
// 地理围栏

// expandDataCollapse allstring
// 扩展参数

// createTimeCollapse allstringdate-time
// 创建时间

// updateTimeCollapse allstringdate-time
// 修改时间

// openidCollapse allstring
// 第三方平台openid
// }


//编辑设备
export const editDevice = (data) =>
  request({
    url: '/manage-api/v1/device/info',
    method: 'put',
    data
  })

// 入参
// {
// idCollapse allintegerint64
// 设备ID

// deviceNameCollapse allstring
// 设备名称

// deviceNoCollapse allstring
// 设备编号

// communityIdCollapse allintegerint64
// 小区ID

// typeIdCollapse allintegerint64
// 设备类型ID

// modelCollapse allstring
// 设备型号

// ipCollapse allstring
// 设备IP

// macCollapse allstring
// 设备Mac

// lngCollapse allnumberdouble
// 经度

// latCollapse allnumberdouble
// 纬度

// altCollapse allnumberdouble
// 海拔

// addressCollapse allstring
// 地址

// installTypeCollapse allintegerint32
// 安装类型

// noteCollapse allstring
// 描述

// sortCollapse allintegerint32
// 排序

// statusCollapse allintegerint32
// 状态

// polyCoordsCollapse allstring
// 地理围栏

// expandDataCollapse allstring
// 扩展参数

// createTimeCollapse allstringdate-time
// 创建时间

// updateTimeCollapse allstringdate-time
// 修改时间

// openidCollapse allstring
// 第三方平台openid
// }



//删除设备
export const deleteDevice = (id) =>
  request({
    url: '/manage-api/v1/device/info?id=' + id,
    method: 'delete'
  })

//通过id查询设备
export const getDevice = (id) =>
  request({
    url: '/manage-api/v1/device/info?id=' + id,
    method: 'get'
  })
// 返回
// {
//     idCollapse allintegerint64
// 设备ID

// deviceNameCollapse allstring
// 设备名称

// deviceNoCollapse allstring
// 设备编号

// communityIdCollapse allintegerint64
// 小区ID

// typeIdCollapse allintegerint64
// 设备类型ID

// modelCollapse allstring
// 设备型号

// ipCollapse allstring
// 设备IP

// macCollapse allstring
// 设备Mac

// lngCollapse allnumberdouble
// 经度

// latCollapse allnumberdouble
// 纬度

// altCollapse allnumberdouble
// 海拔

// addressCollapse allstring
// 地址

// installTypeCollapse allintegerint32
// 安装类型

// noteCollapse allstring
// 描述

// sortCollapse allintegerint32
// 排序

// statusCollapse allintegerint32
// 状态

// polyCoordsCollapse allstring
// 地理围栏

// expandDataCollapse allstring
// 扩展参数

// createTimeCollapse allstringdate-time
// 创建时间

// updateTimeCollapse allstringdate-time
// 修改时间

// openidCollapse allstring
// 第三方平台openid
// }



//设备类型相关------------------------------------------------------------------------
//分页查询设备类型
export const listDeviceType = (data) =>
  request({
    url: '/manage-api/v1/device/type/page',
    method: 'get',
    params: data
  })
// 返回结果：
// {
//   idCollapse allintegerint64
// 设备类型ID

// typeNameCollapse allstring
// 类型名称

// typeCodeCollapse allstring
// 类型代号

// parentIdCollapse allintegerint64
// 父级ID

// ancestorsCollapse allstring
// 祖级列表(逗号分隔）

// sortCollapse allintegerint32
// 排序

// communityIdCollapse allintegerint64
// 小区ID
// }



//添加设备类型
export const addDeviceType = (data) =>
  request({
    url: '/manage-api/v1/device/type',
    method: 'post',
    data
  })
// 入参
// {

// typeNameCollapse allstring
// 类型名称

// typeCodeCollapse allstring
// 类型代号

// parentIdCollapse allintegerint64
// 父级ID

// ancestorsCollapse allstring
// 祖级列表(逗号分隔）

// sortCollapse allintegerint32
// 排序

// communityIdCollapse allintegerint64
// 小区ID
// }



//编辑设备类型
export const editDeviceType = (data) =>
  request({
    url: '/manage-api/v1/device/type',
    method: 'put',
    data
  })

// 入参
// {
//     idCollapse allintegerint64
// 设备类型ID

// typeNameCollapse allstring
// 类型名称

// typeCodeCollapse allstring
// 类型代号

// parentIdCollapse allintegerint64
// 父级ID

// ancestorsCollapse allstring
// 祖级列表(逗号分隔）

// sortCollapse allintegerint32
// 排序

// communityIdCollapse allintegerint64
// 小区ID
// }



//删除设备类型
export const deleteDeviceType = (id) =>
  request({
    url: '/manage-api/v1/device/type?id=' + id,
    method: 'delete'
  })

//通过id查询设备类型
export const getDeviceType = (id) =>
  request({
    url: '/manage-api/v1/device/type?id=' + id,
    method: 'get'
  })
