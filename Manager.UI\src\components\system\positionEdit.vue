<template>
  <el-dialog
    width="600px"
    v-loading="loading"
    destroy-on-close
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-form
      :rules="rules"
      ref="form"
      :model="positionModel"
      label-width="120px"
    >
      <el-row>
        <el-col :span="20">
          <el-form-item label="职位名称" prop="positionName">
            <el-input
              v-model="positionModel.positionName"
              placeholder="职位名称"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="positionModel.sort"
              placeholder="排序"
              :min="0"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20">
          <el-form-item label="所属组织" prop="orgId">
            <el-cascader
              v-model="positionModel.orgId"
              :options="orgTreeData"
              :props="orgCascaderProps"
              placeholder="请选择组织（可选）"
              clearable
              filterable
              :show-all-levels="false"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { addPosition, editPosition } from "@/api/system/position";
import mitt from "@/utils/mitt";

export default {
  props: {
    orgTreeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      positionModel: {},
      dialog: {},
      orgCascaderProps: {
        value: 'id',
        label: 'orgName',
        children: 'children',
        emitPath: false,
        checkStrictly: true,
        expandTrigger: 'click'
      },
      rules: {
        positionName: [
          {
            required: true,
            message: "请输入职位名称",
            trigger: "blur",
          },
        ],
        sort: [
          {
            type: "number",
            message: "排序必须为数字",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.positionModel.id == 0 || !this.positionModel.id) {
            // 添加职位
            addPosition(this.positionModel)
              .then((res) => {
                this.$message.success("操作成功");
                this.$emit("search");
                this.dialog.show = false;
              })
              .catch((err) => {
                this.$message.error(err.data.errorMessage);
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            // 编辑职位
            editPosition(this.positionModel)
              .then((res) => {
                this.$message.success("操作成功");
                this.$emit("search");
                this.dialog.show = false;
              })
              .catch((err) => {
                this.$message.error(err.data.errorMessage);
              })
              .finally(() => {
                this.loading = false;
              });
          }
        }
      });
    },
  },
  mounted() {
    mitt.on("openPositionEdit", (position) => {
      this.positionModel = { ...position };
      this.dialog.show = true;
      this.dialog.title = "修改职位信息";
    });
    mitt.on("openPositionAdd", () => {
      this.positionModel = {
        positionName:  "",
        sort: 0,
        orgId: null
      };
      this.dialog.show = true;
      this.dialog.title = "添加职位";
    });
  },
  beforeUnmount() {
    mitt.off("openPositionEdit");
    mitt.off("openPositionAdd");
  },
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
