<template>
  <div class="resident-list-container">
    <community-resident-edit @search="search" ref="editDialog" />

    <div class="card card--search search-flex">
      <el-input
        v-model="searchModel.phone"
        placeholder="手机号"
        clearable
        style="width: 200px; margin-right: 16px"
      />
      <el-input
        v-model="searchModel.residentName"
        placeholder="住户姓名"
        clearable
        style="width: 200px; margin-right: 16px"
      />
      <el-button
        type="primary"
        @click="search"
        style="margin-right: 8px"
        :disabled="!hasCurrentCommunity"
        >搜索</el-button
      >
      <el-button type="primary" @click="add" :disabled="!hasCurrentCommunity"
        >添加</el-button
      >
    </div>
    <div class="card card--table">
      <div class="table-col">
        <el-table
          :data="residentList"
          row-key="id"
          style="width: 100%; height: 100%"
          class="data-table"
        >
          <el-table-column
            prop="residentName"
            label="住户姓名"
            width="120"
            align="center"
          />
          <el-table-column
            prop="phone"
            label="手机号"
            width="140"
            align="center"
          />
          <el-table-column prop="tags" label="标签" align="center">
            <template #default="scope">
              <el-tag
                v-for="tag in getTagArray(scope.row.tags)"
                :key="tag"
                :type="getTagElementType(tag)"
                :style="{ ...getTagStyle(tag), marginRight: '5px' }"
              >
                {{ getTagLabel(tag) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="note" label="备注" align="center" />
          <el-table-column prop="createTime" label="创建时间" align="center" />
          <el-table-column prop="updateTime" label="更新时间" align="center" />
          <el-table-column label="操作" width="240" align="center">
            <template #default="scope">
              <el-button type="text" size="mini" @click="edit(scope.row.id)"
                >编辑</el-button
              >
              <el-button
                type="text"
                size="mini"
                @click="openPropertyManagement(scope.row)"
                >房产</el-button
              >
              <el-button type="text" size="mini" @click="deleted(scope.row.id)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-col">
        <el-pagination
          background
          layout="prev, pager, next"
          @current-change="currentChange"
          :total="total"
          :page-size="searchModel.pageSize"
          :current-page="searchModel.pageNum"
        />
      </div>
    </div>
  </div>

  <!-- 住户编辑对话框 -->
  <communityResidentEdit />

  <!-- 房产管理对话框 -->
  <communityResidentProperty />
</template>

<script>
import {
  listCommunityResident,
  deleteCommunityResident,
  getCommunityResident,
} from "@/api/community/communityResident";
import {
  getSelectedCommunityId,
  hasSelectedCommunity,
} from "@/store/modules/options";
import { eventManagerMixin } from "@/utils/eventManager";
import { listDictByNameEn } from "@/api/system/dict";
import communityResidentEdit from "@/components/community/communityResidentEdit.vue";
import communityResidentProperty from "@/components/community/communityResidentProperty.vue";
export default {
  mixins: [eventManagerMixin],
  components: {
    communityResidentEdit,
    communityResidentProperty,
  },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        phone: "",
        residentName: "",
      },
      residentList: [],
      total: 0,
      dictData: [],
    };
  },
  computed: {
    // 是否有选中的小区
    hasCurrentCommunity() {
      return hasSelectedCommunity();
    },
  },
  methods: {
    search() {
      // 获取当前选中的小区ID - 使用新的简化API
      const communityId = getSelectedCommunityId();

      if (!communityId) {
        this.residentList = [];
        this.total = 0;
        return;
      }

      // 将小区ID添加到搜索参数中
      const searchParams = {
        ...this.searchModel,
        communityId: communityId,
      };

      listCommunityResident(searchParams)
        .then((res) => {
          this.residentList = res.data.data.list;
          this.total = res.data.data.total;
        })
        .catch((err) => {
          console.error("查询住户列表失败:", err);
          this.$message.error(err.data?.errorMessage || "查询失败");
          this.residentList = [];
          this.total = 0;
        });
    },

    // 小区变化时的处理方法
    onCommunityChange(community) {
      console.log("小区已切换到:", community?.communityName || "无");

      // 清空当前列表
      this.residentList = [];
      this.total = 0;

      // 重置搜索条件
      this.searchModel.pageNum = 1;

      // 如果有选中的小区，重新加载数据
      if (community) {
        this.search();
      }
    },
    add() {
      console.log("新增");
      this.$safeEmit("openCommunityResidentEdit");
    },
    edit(id) {
      getCommunityResident(id).then((res) => {
        this.$safeEmit("openCommunityResidentEdit", res.data.data);
      });
    },
    openPropertyManagement(resident) {
      this.$safeEmit("openResidentProperty", resident);
    },
    deleted(id) {
      this.$confirm("删除住户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteCommunityResident(id)
            .then(() => {
              this.search();
              this.$message.success("操作成功");
            })
            .catch((err) => {
            
              this.$message.error("删除住户失败" + (err.data.errorMessage?(','+err.data.errorMessage):''));
            });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    getTagArray(tags) {
      if (!tags) return [];
      return tags.split(",").filter((tag) => tag.trim());
    },

    /**
     * 获取标签显示文本
     */
    getTagLabel(tag) {
      const dictItem = this.dictData.find((item) => item.nameEn === tag);
      return dictItem ? dictItem.nameCn : tag;
    },

    /**
     * 获取标签类型（颜色）
     */
    getTagType(tag) {
      const dictItem = this.dictData.find((item) => item.nameEn === tag);
      if (dictItem?.cssClass) {
        return dictItem.cssClass;
      }

      // 根据标签内容返回默认颜色
      const label = this.getTagLabel(tag);
      const colorMap = {
        老人: "warning",
        低保: "danger",
        党员: "danger",
        军人: "success",
        残疾人: "warning",
        VIP: "danger",
        业主: "success",
        租户: "primary",
        独居老人: "warning",
        空巢老人: "warning",
        留守儿童: "warning",
        困难户: "danger",
        五保户: "danger",
        退休干部: "info",
        教师: "success",
        医生: "success",
        志愿者: "primary",
        网格员: "primary",
      };
      return colorMap[label] || "";
    },

    /**
     * 获取Element Plus支持的标签类型
     */
    getTagElementType(tag) {
      const tagType = this.getTagType(tag);

      // 如果是Element Plus支持的类型，直接返回
      const elementTypes = ["primary", "success", "warning", "danger", "info"];
      if (elementTypes.includes(tagType)) {
        return tagType;
      }

      // 如果是自定义颜色，返回空字符串，使用style属性
      return "";
    },

    /**
     * 获取标签自定义样式
     */
    getTagStyle(tag) {
      const tagType = this.getTagType(tag);

      // 如果是Element Plus支持的类型，不需要自定义样式
      const elementTypes = ["primary", "success", "warning", "danger", "info"];
      if (elementTypes.includes(tagType)) {
        return {};
      }

      // 如果是十六进制颜色或其他自定义颜色
      if (tagType && tagType.startsWith("#")) {
        return {
          backgroundColor: tagType,
          borderColor: tagType,
          color: "#fff",
        };
      }

      // 如果是CSS颜色名
      if (tagType) {
        return {
          backgroundColor: tagType,
          borderColor: tagType,
          color: "#fff",
        };
      }

      return {};
    },

    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        const res = await listDictByNameEn("resident_tag");
        this.dictData = res.data.data || [];
      } catch (err) {
        console.error("加载住户标签字典失败:", err);
        this.dictData = [];
      }
    },
  },
  created() {
    // 初始化字典数据
    this.initDictData();

    // 初始化时检查是否有选中的小区
    if (hasSelectedCommunity()) {
      this.search();
    } else {
      console.log("页面初始化时未选择小区，等待用户选择");
    }
  },

  mounted() {
    // 监听住户编辑完成后的刷新事件
    this.$safeOn("refreshResidentList", () => {
      this.search();
    });
  },
};
</script>

<style scoped>
.resident-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}
.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
/* 小区信息显示 */
.card--info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 16px;
}

.community-info {
  display: flex;
  align-items: center;
  padding: 12px 16px;
}

.info-label {
  font-weight: bold;
  margin-right: 8px;
}

.info-value {
  font-size: 16px;
  font-weight: 600;
  margin-right: 8px;
}

.info-count {
  font-size: 14px;
  opacity: 0.9;
}

/* 警告信息显示 */
.card--warning {
  background: #fdf6ec;
  border: 1px solid #f5dab1;
  color: #e6a23c;
  margin-bottom: 16px;
}

.warning-info {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  font-size: 14px;
}

.warning-info .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.dark-theme .card {
  background-color: var(--card-background);
  border: 1px solid #444;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.dark-theme .card--warning {
  background: #2d2f36;
  border: 1px solid #5a5a5a;
  color: #e6a23c;
}
</style>