<template>
  <div class="person-list-container">
    <person-edit
      ref="personEdit"
      :certificateList="certificateList"
      :statusList="statusList"
      @search="search"
    ></person-edit>

    <user-edit ref="userEdit" :userStatusList="userStatusList"></user-edit>

    <div class="card card--search search-flex">
      <el-input
        v-model="searchModel.personName"
        placeholder="人员姓名"
        clearable
        style="width: 200px; margin-right: 16px"
      />
      <el-input
        v-model="searchModel.phone"
        placeholder="手机号"
        clearable
        style="width: 200px; margin-right: 16px"
      />
      <el-input
        v-model="searchModel.personNumber"
        placeholder="员工编号"
        clearable
        style="width: 200px; margin-right: 16px"
      />
      <el-select
        v-model="searchModel.status"
        placeholder="状态"
        clearable
        style="width: 200px; margin-right: 16px"
      >
        <el-option
          v-for="item in statusList"
          :key="item.nameEn"
          :label="item.nameCn"
          :value="item.nameEn"
        />
      </el-select>
      <el-button type="primary" @click="search" style="margin-right: 8px"
        >搜索</el-button
      >
      <el-button type="primary" @click="add">添加</el-button>
    </div>
    <div class="card card--table">
      <div class="table-col">
        <el-table
          stripe
          :data="personList"
          style="width: 100%; height: 100%"
          class="data-table"
        >
          <el-table-column prop="id" align="center" label="ID" width="80" />
          <el-table-column
            prop="personName"
            align="center"
            label="姓名"
            min-width="140"
          />
          <el-table-column
            prop="personNumber"
            align="center"
            label="员工编号"
            min-width="140"
          />
          <el-table-column
            prop="phone"
            align="center"
            label="手机号"
            min-width="140"
          />
          <el-table-column
            prop="email"
            align="center"
            label="邮箱"
            min-width="220"
          />
          <!-- <el-table-column
            prop="entryTime"
            align="center"
            label="入职时间"
            width="220"
          /> -->
          <el-table-column
            prop="status"
            align="center"
            label="状态"
            min-width="140"
          >
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ formatStatus(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>


          <el-table-column
            prop="createTime"
            align="center"
            label="创建时间"
            width="170"
          />
             <el-table-column
            align="center"
            label="是否分配账号"
            min-width="140"
          >
            <template #default="scope">
              <el-tag :type="scope.row.userId ? 'success' : 'info'">
                {{ scope.row.userId ? '已分配' : '未分配' }}
              </el-tag>
            </template>
          </el-table-column>
     
          <el-table-column align="center" label="操作" width="350">
            <template #default="scope">
              <el-button
                type="text"
               
                @click="edit(scope.row.id)"
                style="margin-right: 8px;"
              >编辑</el-button>
              <el-button
                type="text"
             
                @click="deleted(scope.row.id)"
                style="margin-right: 8px;"
              >删除</el-button>
              <el-dropdown
                @command="(command) => auditPerson(scope.row, command)"
                trigger="click"
                style="margin-right: 8px;"
              >
                <el-button type="text" >
                  审核<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="status in getAvailableStatuses(scope.row.status)"
                      :key="status.nameEn"
                      :command="status.nameEn"
                    >
                      <el-icon><check /></el-icon>
                      设为{{ status.nameCn }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-button
                type="text"
                @click="assignAccount(scope.row)"
              >{{ scope.row.userId ? '编辑账号' : '分配账号' }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="searchModel.pageNum"
          v-model:page-size="searchModel.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="search"
          @current-change="currentChange"
        />
      </div>
    </div>
  </div>
</template>
<script>
import {
  listPerson,
  deletePerson,
  getPerson,
  editPerson,
} from "@/api/system/person";
import { getUser } from "@/api/system/user";
import { listDictByNameEn } from "@/api/system/dict";
import mitt from "@/utils/mitt";
import personEdit from "@/components/system/personEdit.vue";
import userEdit from "@/components/system/userEditUseByProperty.vue";
import { ArrowDown, Check } from "@element-plus/icons-vue";
export default {
  components: {
    personEdit,
    userEdit,
    ArrowDown,
    Check,
  },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        personName: "",
        phone: "",
        personNumber: "",
        status: "",
      },
      personList: [],
      certificateList: [],
      statusList: [],
      userStatusList: [],
      total: 0,
    };
  },
  methods: {
    search() {
      listPerson(this.searchModel)
        .then((res) => {
          this.personList = res.data.data.list;
          this.total = res.data.data.total;
        })
        .catch((err) => {
          this.$message.error(err.data.errorMessage);
        });
    },
    add() {
      mitt.emit("openPersonAdd");
    },
    edit(id) {
      getPerson(id)
        .then((res) => {
          mitt.emit("openPersonEdit", res.data.data);
        })
        .catch((err) => {
          this.$message.error(err.data.errorMessage);
        });
    },
    deleted(id) {
      this.$confirm("删除员工, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deletePerson(id)
            .then(() => {
              this.search();
              this.$message.success("操作成功");
            })
            .catch((err) => {
              this.$message.error(err.data.errorMessage);
            });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    formatStatus(status) {
      const statusItem = this.statusList.find((item) => item.nameEn === status);
      return statusItem ? statusItem.nameCn : status;
    },
    getStatusType(status) {
      // 根据状态返回对应的标签类型
      const statusMap = {
        normal: "success",
        inactive: "danger",
        trial: "warning",
      };
      return statusMap[status] || "info";
    },

    /**
     * 获取可用的状态选项（排除当前状态）
     */
    getAvailableStatuses(currentStatus) {
      return this.statusList.filter(
        (status) => status.nameEn !== currentStatus
      );
    },

    /**
     * 审核员工状态
     */
    auditPerson(row, newStatus) {
      // 根据状态获取对应的文本描述
      const currentStatusText = this.formatStatus(row.status);
      const newStatusText = this.formatStatus(newStatus);
      const actionText = `从"${currentStatusText}"变更为"${newStatusText}"`;

      this.$confirm(`确定要将员工${actionText}吗？`, "状态变更确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        message: `
          <div>
            <p><strong>员工：</strong>${row.personName}</p>
            <p><strong>当前状态：</strong><span style="color: #E6A23C;">${currentStatusText}</span></p>
            <p><strong>变更为：</strong><span style="color: #67C23A;">${newStatusText}</span></p>
            <p style="color: #E6A23C; margin-top: 10px;">此操作将改变员工状态，请谨慎操作！</p>
          </div>
        `,
      })
        .then(() => {
          // 构建状态变更数据
          const updateData = {
            id: row.id,
            status: newStatus,
          };

          console.log("员工状态变更数据:", updateData);

          editPerson(updateData)
            .then((res) => {
              console.log("员工状态变更成功响应:", res);
              this.search();
              this.$message.success(`员工状态已${actionText}`);
            })
            .catch((err) => {
              console.error(`员工状态变更失败:`, err);
              const errorMsg =
                err.response?.data?.errorMessage ||
                err.data?.errorMessage ||
                `状态变更失败`;
              this.$message.error(errorMsg);
            });
        })
        .catch(() => {
          // 用户取消操作
        });
    },

    /**
     * 分配账号
     */
    async assignAccount(row) {
      console.log("分配账号给员工:", row);

      // 检查是否已分配账号
      if (row.userId && row.userId !== '' && row.userId !== null) {
        // 已分配账号，查询用户信息进行编辑
        try {
          const res = await getUser(row.userId);
          if (res.data && res.data.data) {
            // 触发用户编辑弹窗，传入现有用户信息
            mitt.emit("openUserEdit", res.data.data);
          } else {
            this.$message.error('获取用户信息失败');
          }
        } catch (err) {
          this.$message.error(err.data?.errorMessage || '获取用户信息失败');
        }
      } else {
        // 未分配账号，创建新账号
        const userData = {
          userName: "", // 用户名需要手动填写
          nickName: row.personName, // 使用员工姓名作为昵称
          phone: row.phone, // 使用员工手机号
          personId: row.id, // 员工ID
          orgId: row.orgId, // 员工所属组织ID
          email: row.email || "", // 员工邮箱
        };
        mitt.emit("openUserAdd", userData);
      }
    },
    async init() {
      try {
        const [certificate_res, status_res, user_status_res, person_res] =
          await Promise.all([
            listDictByNameEn("certificate_type"),
            listDictByNameEn("person_status"),
            listDictByNameEn("sys_user_status"),
            listPerson(this.searchModel),
          ]);
        this.certificateList = certificate_res.data.data;
        this.statusList = status_res.data.data;

        this.userStatusList = user_status_res.data.data;
        this.personList = person_res.data.data.list;
        this.total = person_res.data.data.total;
      } catch (err) {
        this.$message.error(err.data?.errorMessage || "加载数据失败");
      }
    },
  },
  created() {
    this.init();
  },
  unmounted() {
    mitt.off("openPersonAdd");
    mitt.off("openPersonEdit");
  },
};
</script>

<style scoped>
.person-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.card--table {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  margin-top: 0;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  padding: 0 20px 20px;
}

.search-flex {
  display: flex;
  align-items: center;
}

.card--search {
  margin-bottom: 20px;
  flex: none;
  height: auto;
  padding: 20px 20px;
  display: flex;
  align-items: center;
}
</style>