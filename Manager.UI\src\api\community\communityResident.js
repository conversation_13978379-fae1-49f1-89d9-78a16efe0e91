/**
 * 小区住户管理API
 *
 * 提供住户的增删改查、验证等功能
 */
import request from '@/utils/request'

/**
 * 分页查询小区住户列表
 * @param {Object} data - 查询参数
 * @param {number} data.communityId - 小区ID（必填）
 * @param {number} data.pageNum - 页码，默认1
 * @param {number} data.pageSize - 每页数量，默认10
 * @param {string} data.phone - 手机号（模糊查询）
 * @param {string} data.address - 地址（模糊查询）
 * @param {string} data.residentType - 住户类型
 * @param {string} data.status - 状态
 * @returns {Promise} 住户列表数据
 */
export const listCommunityResident = (data) =>
	request({
		url: '/manage-api/v1/community/resident/page',
		method: 'get',
		params: data
	})

/**
 * 通过ID查询住户详情
 * @param {number} id - 住户ID
 * @returns {Promise} 住户详细信息
 */
export const getCommunityResident = (id) =>
	request({
		url: '/manage-api/v1/community/resident',
		method: 'get',
		params: { id: id }
	})

/**
 * 新增小区住户
 * @param {Object} data - 住户数据
 * @param {number} data.communityId - 小区ID（必填）
 * @param {string} data.residentName - 住户姓名（必填）
 * @param {string} data.certificateType - 证件类型（必填）
 * @param {string} data.idCardNumber - 身份证号（必填）
 * @param {string} data.phone - 手机号（必填）
 * @param {string} data.residentType - 住户类型（必填）
 * @param {string} data.status - 状态，默认normal
 * @returns {Promise} 新增结果
 */
export const addCommunityResident = (data) =>
	request({
		url: '/manage-api/v1/community/resident',
		method: 'post',
		data: data
	})

/**
 * 编辑小区住户
 * @param {Object} data - 住户数据（包含id字段）
 * @returns {Promise} 编辑结果
 */
export const editCommunityResident = (data) =>
	request({
		url: '/manage-api/v1/community/resident',
		method: 'put',
		data: data
	})

/**
 * 删除小区住户
 * @param {number} id - 住户ID
 * @returns {Promise} 删除结果
 */
export const deleteCommunityResident = (id) =>
	request({
		url: '/manage-api/v1/community/resident',
		method: 'delete',
		params: {
			id: id
		}
	})

/**
 * 住户信息验证
 * 在新增住户前验证身份信息的真实性和唯一性
 * @param {Object} data - 验证数据
 * @param {string} data.residentName - 住户姓名（必填）
 * @param {string} data.certificateType - 证件类型（必填）
 * @param {string} data.idCardNumber - 身份证号（必填）
 * @param {number} data.communityId - 小区ID（必填）
 * @param {string} data.phone - 手机号（必填）
 * @returns {Promise} 验证结果
 */
export const residentVerify = (data) =>
	request({
		url: '/manage-api/v1/community/resident/verify',
		method: 'post',
		data: data
	})

/**
 * 住户类型字典
 */
export const RESIDENT_TYPES = [
	{ value: 'owner', label: '业主' },
	{ value: 'tenant', label: '租户' },
	{ value: 'family', label: '家属' }
]

/**
 * 证件类型字典
 */
export const CERTIFICATE_TYPES = [
	{ value: 'idCard', label: '身份证' },
	{ value: 'passport', label: '护照' },
	{ value: 'other', label: '其他' }
]

/**
 * 住户状态字典
 */
export const RESIDENT_STATUS = [
	{ value: 'normal', label: '正常' },
	{ value: 'disabled', label: '禁用' },
	{ value: 'pending', label: '审核中' }
]